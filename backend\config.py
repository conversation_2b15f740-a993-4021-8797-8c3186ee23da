"""
BrAInBI - Configuration pour Azure OpenAI
Business Intelligence augmentée par l'IA
Modifiez ces valeurs selon votre déploiement Azure
"""

import os
from typing import Optional

class AzureOpenAIConfig:
    """Configuration Azure OpenAI"""
    
    # Configuration de base Azure OpenAI
    API_TYPE = "azure"
    API_VERSION = "2025-01-01-preview"
    
    # À MODIFIER selon votre déploiement Azure
    API_BASE = os.getenv("AZURE_OPENAI_ENDPOINT", "https://endpont.openai.azure.com/")
    API_KEY = os.getenv("AZURE_OPENAI_KEY", "key")
 
 
 
    
    # Noms des déploiements disponibles (à modifier selon vos déploiements)
    DEPLOYMENTS = {
        "gpt-35-turbo": "gpt-35-turbo",  # Nom du déploiement pour GPT-3.5 Turbo
        "gpt-4": "gpt-4",                # Nom du déploiement pour GPT-4
        "gpt-4-1106-preview": "gpt-4-turbo",  # Nom du déploiement pour GPT-4 Turbo
        "gpt-4o": "gpt-4o"               # Nom du déploiement pour GPT-4o
    }
    
    # Configuration par défaut
    DEFAULT_MODEL = "gpt-4.1"
    DEFAULT_DEPLOYMENT = DEPLOYMENTS.get(DEFAULT_MODEL, "gpt-4.1")
    DEFAULT_TEMPERATURE = 0.4
    DEFAULT_MAX_TOKENS = 2000

class ModelSettings:
    """Paramètres du modèle IA configurables"""
    
    def __init__(self):
        self.model_name = AzureOpenAIConfig.DEFAULT_MODEL
        self.deployment_name = AzureOpenAIConfig.DEFAULT_DEPLOYMENT
        self.temperature = AzureOpenAIConfig.DEFAULT_TEMPERATURE
        self.max_tokens = AzureOpenAIConfig.DEFAULT_MAX_TOKENS
    
    def update_model(self, model_name: str, deployment_name: Optional[str] = None):
        """Met à jour le modèle et le déploiement"""
        self.model_name = model_name
        
        if deployment_name:
            self.deployment_name = deployment_name
        else:
            # Utiliser le déploiement par défaut pour ce modèle
            self.deployment_name = AzureOpenAIConfig.DEPLOYMENTS.get(model_name, model_name)
    
    def get_config(self) -> dict:
        """Retourne la configuration actuelle"""
        return {
            "model_name": self.model_name,
            "deployment_name": self.deployment_name,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens
        }

# Instructions de configuration
CONFIGURATION_INSTRUCTIONS = """
🔧 CONFIGURATION AZURE OPENAI - BrAInBI

1. Modifiez les valeurs dans config.py :
   - API_BASE : Votre endpoint Azure OpenAI
   - API_KEY : Votre clé API Azure OpenAI
   - DEPLOYMENTS : Noms de vos déploiements Azure

2. Ou utilisez les variables d'environnement :
   - AZURE_OPENAI_ENDPOINT
   - AZURE_OPENAI_KEY

3. Exemple de configuration :
   API_BASE = "https://mon-resource.openai.azure.com/"
   API_KEY = "votre-cle-api-azure"

   DEPLOYMENTS = {
       "gpt-4": "mon-deploiement-gpt4",
       "gpt-35-turbo": "mon-deploiement-gpt35"
   }

4. Testez la configuration avec :
   python test_backend.py

💡 BrAInBI - Explore your Data. Amplified by AI.
"""

def validate_config() -> tuple[bool, str]:
    """Valide la configuration Azure OpenAI"""
    
    if AzureOpenAIConfig.API_BASE == "https://TON-ENDPOINT.openai.azure.com/":
        return False, "❌ API_BASE non configuré. Modifiez config.py avec votre endpoint Azure."
    
    if AzureOpenAIConfig.API_KEY == "TA-CLE":
        return False, "❌ API_KEY non configuré. Modifiez config.py avec votre clé Azure."
    
    if not AzureOpenAIConfig.API_BASE.startswith("https://"):
        return False, "❌ API_BASE doit commencer par https://"
    
    if not AzureOpenAIConfig.API_BASE.endswith("/"):
        return False, "❌ API_BASE doit se terminer par /"
    
    return True, "✅ Configuration Azure OpenAI valide"

def print_config_status():
    """Affiche le statut de la configuration"""
    is_valid, message = validate_config()
    print(message)
    
    if not is_valid:
        print(CONFIGURATION_INSTRUCTIONS)
    else:
        print(f"📍 Endpoint: {AzureOpenAIConfig.API_BASE}")
        print(f"🔑 Clé configurée: {'Oui' if AzureOpenAIConfig.API_KEY != 'TA-CLE' else 'Non'}")
        print(f"🤖 Modèle par défaut: {AzureOpenAIConfig.DEFAULT_MODEL}")
        print(f"🚀 Déploiement par défaut: {AzureOpenAIConfig.DEFAULT_DEPLOYMENT}")

if __name__ == "__main__":
    print_config_status()
