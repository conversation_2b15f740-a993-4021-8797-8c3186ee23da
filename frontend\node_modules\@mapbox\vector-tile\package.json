{"name": "@mapbox/vector-tile", "description": "Parses vector tiles", "repository": "https://github.com/mapbox/vector-tile-js.git", "version": "1.3.1", "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "dependencies": {"@mapbox/point-geometry": "~0.1.0"}, "devDependencies": {"benchmark": "^1.0.0", "coveralls": "~2.11.2", "istanbul": "~0.3.6", "mapnik": "~3.6.0", "jshint": "^2.6.3", "pbf": "^1.3.2", "tape": "~3.5.0", "eslint": "~1.00.0", "eslint-config-unstyled": "^1.1.0"}, "jshintConfig": {"trailing": true, "undef": true, "unused": true, "indent": 4, "node": true}, "scripts": {"test": "eslint lib index.js && jshint lib && tape test/parse.test.js", "cov": "istanbul cover ./node_modules/.bin/tape test/parse.test.js && coveralls < ./coverage/lcov.info"}}