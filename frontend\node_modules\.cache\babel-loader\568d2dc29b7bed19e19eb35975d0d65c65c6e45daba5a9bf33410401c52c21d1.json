{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIZekri\\\\frontend\\\\src\\\\components\\\\DragDropComponents.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\n/**\n * DragDropComponents - Composants pour l'interface drag & drop\n * Utilise @dnd-kit pour créer des colonnes draggables et des zones de drop\n */\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useDraggable, useDroppable } from '@dnd-kit/core';\nimport { DarkBadge } from './YellowMindUI';\n\n// Composant de menu contextuel pour les colonnes\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const ColumnContextMenu = ({\n  isOpen,\n  onClose,\n  position,\n  column,\n  onAddToAxis,\n  selectedTable\n}) => {\n  _s();\n  const menuRef = useRef(null);\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (menuRef.current && !menuRef.current.contains(event.target)) {\n        onClose();\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => document.removeEventListener('mousedown', handleClickOutside);\n    }\n  }, [isOpen, onClose]);\n  if (!isOpen) return null;\n  const menuItems = [{\n    id: 'x-axis',\n    label: 'Ajouter à Axe X',\n    icon: '📊',\n    color: 'text-blue-400'\n  }, {\n    id: 'y-axis',\n    label: 'Ajouter à Axe Y',\n    icon: '📈',\n    color: 'text-green-400'\n  }, {\n    id: 'legend',\n    label: 'Ajouter à Légende',\n    icon: '🏷️',\n    color: 'text-yellow-400'\n  }, {\n    id: 'values',\n    label: 'Ajouter aux Valeurs',\n    icon: '💎',\n    color: 'text-purple-400'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: menuRef,\n    className: \"fixed z-50 bg-gray-800 border border-gray-600 rounded-lg shadow-xl backdrop-blur-sm\",\n    style: {\n      left: position.x,\n      top: position.y,\n      minWidth: '200px'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-3 py-2 border-b border-gray-600 mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm font-medium text-gray-200\",\n          children: column.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-400\",\n          children: column.type\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), menuItems.map(item => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          const columnWithTable = {\n            ...column,\n            table: selectedTable\n          };\n          onAddToAxis(item.id, columnWithTable);\n          onClose();\n        },\n        className: `\n              w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left\n              hover:bg-gray-700 transition-colors duration-200 group\n            `,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-lg\",\n          children: item.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `text-sm ${item.color} group-hover:text-gray-100`,\n          children: item.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this)]\n      }, item.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour une colonne draggable\n_s(ColumnContextMenu, \"lbfKxozlpk19p2tUpYavRIkbEU0=\");\n_c = ColumnContextMenu;\nexport const DraggableColumn = ({\n  column,\n  id,\n  onAddToAxis,\n  selectedTable\n}) => {\n  _s2();\n  const [contextMenu, setContextMenu] = useState({\n    isOpen: false,\n    position: {\n      x: 0,\n      y: 0\n    }\n  });\n  const {\n    attributes,\n    listeners,\n    setNodeRef,\n    transform,\n    isDragging\n  } = useDraggable({\n    id: id,\n    data: {\n      column: column,\n      type: 'column'\n    }\n  });\n  const style = transform ? {\n    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,\n    opacity: isDragging ? 0.7 : 1,\n    zIndex: isDragging ? 1000 : 1\n  } : undefined;\n  const handleContextMenu = e => {\n    e.preventDefault();\n    setContextMenu({\n      isOpen: true,\n      position: {\n        x: e.clientX,\n        y: e.clientY\n      }\n    });\n  };\n  const handleQuickAdd = e => {\n    e.stopPropagation();\n    setContextMenu({\n      isOpen: true,\n      position: {\n        x: e.currentTarget.getBoundingClientRect().right + 10,\n        y: e.currentTarget.getBoundingClientRect().top\n      }\n    });\n  };\n  const getColumnIcon = type => {\n    const dataType = type.toLowerCase();\n    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n      return '🔢';\n    }\n    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n      return '📝';\n    }\n    if (dataType.includes('date') || dataType.includes('time')) {\n      return '📅';\n    }\n    if (dataType.includes('bit') || dataType.includes('boolean')) {\n      return '☑️';\n    }\n    return '📊';\n  };\n  const getColumnColor = type => {\n    const dataType = type.toLowerCase();\n    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n      return 'bg-blue-600/20 border-blue-500 text-blue-300';\n    }\n    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n      return 'bg-green-600/20 border-green-500 text-green-300';\n    }\n    if (dataType.includes('date') || dataType.includes('time')) {\n      return 'bg-purple-600/20 border-purple-500 text-purple-300';\n    }\n    return 'bg-gray-600/20 border-gray-500 text-gray-300';\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: setNodeRef,\n      style: style,\n      ...listeners,\n      ...attributes,\n      onContextMenu: handleContextMenu,\n      className: `\n          p-3 rounded-lg border-2 border-dashed cursor-grab active:cursor-grabbing\n          transition-all duration-300 ease-in-out transform hover:scale-105 hover:shadow-lg\n          ${getColumnColor(column.type)}\n          ${isDragging ? 'shadow-2xl column-drag-start rotate-3 scale-110' : 'hover:shadow-md hover:shadow-purple-500/10'}\n          group relative\n        `,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 flex-1 min-w-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: `text-lg transition-transform duration-200 ${isDragging ? 'scale-110' : 'group-hover:scale-105'}`,\n            children: getColumnIcon(column.type)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: `font-medium truncate transition-colors duration-200 ${isDragging ? 'text-purple-200' : 'group-hover:text-gray-100'}`,\n              children: column.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-xs transition-all duration-200 ${isDragging ? 'opacity-90 text-purple-300' : 'opacity-75 group-hover:opacity-90'}`,\n              children: column.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), onAddToAxis && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleQuickAdd,\n          className: \"opacity-0 group-hover:opacity-100 transition-all duration-200 p-1 rounded hover:bg-gray-700 text-gray-400 hover:text-purple-400 transform hover:scale-110 active:scale-95\",\n          title: \"Ajouter rapidement\",\n          children: \"\\u26A1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ColumnContextMenu, {\n      isOpen: contextMenu.isOpen,\n      onClose: () => setContextMenu({\n        ...contextMenu,\n        isOpen: false\n      }),\n      position: contextMenu.position,\n      column: column,\n      onAddToAxis: onAddToAxis,\n      selectedTable: selectedTable\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// Composant pour une zone de drop\n_s2(DraggableColumn, \"Ug8s2XjgY4HyuI7lKr8hN/wtf3s=\", false, function () {\n  return [useDraggable];\n});\n_c2 = DraggableColumn;\nexport const DropZone = ({\n  id,\n  title,\n  subtitle,\n  icon,\n  children,\n  acceptedColumn,\n  onClear\n}) => {\n  _s3();\n  const {\n    isOver,\n    setNodeRef\n  } = useDroppable({\n    id: id,\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: setNodeRef,\n    className: `\n        p-6 rounded-lg border-2 border-dashed min-h-[140px] transition-all duration-200\n        ${isOver ? 'border-purple-400 bg-purple-600/10 scale-105' : 'border-gray-600 bg-gray-800/30'}\n        ${acceptedColumn ? 'border-purple-500 bg-purple-600/20' : ''}\n      `,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-lg\",\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium text-gray-200\",\n            children: title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), subtitle && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-400\",\n            children: subtitle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 26\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this), acceptedColumn && onClear && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClear,\n        className: \"text-gray-400 hover:text-red-400 transition-colors\",\n        title: \"Supprimer\",\n        children: \"\\u2715\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-[60px] flex items-center justify-center\",\n      children: acceptedColumn ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3 p-3 bg-purple-600/30 rounded-lg border border-purple-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xl\",\n            children: acceptedColumn.type.toLowerCase().includes('int') || acceptedColumn.type.toLowerCase().includes('decimal') || acceptedColumn.type.toLowerCase().includes('float') ? '🔢' : acceptedColumn.type.toLowerCase().includes('date') ? '📅' : '📝'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-medium text-purple-200 truncate\",\n              children: acceptedColumn.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-purple-300\",\n              children: acceptedColumn.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this), acceptedColumn.table && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-purple-400 mt-1\",\n              children: [\"\\uD83D\\uDCCB \", acceptedColumn.table]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 text-sm\",\n          children: isOver ? 'Relâchez ici' : 'Glissez une colonne ici'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this), children]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 227,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour afficher les colonnes disponibles\n_s3(DropZone, \"fT702R7NW3L8KUJObOwGrnMsXMQ=\", false, function () {\n  return [useDroppable];\n});\n_c3 = DropZone;\nexport const ColumnsPanel = ({\n  columns,\n  title,\n  onAddToAxis,\n  selectedTable\n}) => {\n  if (!columns || columns.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-16 h-16 bg-gray-700 rounded-2xl mx-auto mb-4 flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-2xl\",\n          children: \"\\uD83D\\uDCCB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-400\",\n        children: \"Aucune colonne disponible\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-200\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DarkBadge, {\n        variant: \"info\",\n        children: columns.length\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3 max-h-[500px] overflow-y-auto\",\n      children: columns.map((column, index) => /*#__PURE__*/_jsxDEV(DraggableColumn, {\n        id: `column-${column.name}`,\n        column: column,\n        onAddToAxis: onAddToAxis,\n        selectedTable: selectedTable\n      }, `${column.name}-${index}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 306,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour les zones de drop organisées\n_c4 = ColumnsPanel;\nexport const DropZonesPanel = ({\n  xAxis,\n  yAxis,\n  legend,\n  values,\n  onClearX,\n  onClearY,\n  onClearLegend,\n  onClearValues\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n    children: [/*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"x-axis\",\n      title: \"Axe X\",\n      subtitle: \"Cat\\xE9gories ou groupes\",\n      icon: \"\\uD83D\\uDCCA\",\n      acceptedColumn: xAxis,\n      onClear: onClearX\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"y-axis\",\n      title: \"Axe Y\",\n      subtitle: \"Valeurs num\\xE9riques (optionnel)\",\n      icon: \"\\uD83D\\uDCC8\",\n      acceptedColumn: yAxis,\n      onClear: onClearY\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"legend\",\n      title: \"L\\xE9gende\",\n      subtitle: \"Sous-cat\\xE9gories (optionnel)\",\n      icon: \"\\uD83C\\uDFF7\\uFE0F\",\n      acceptedColumn: legend,\n      onClear: onClearLegend\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"values\",\n      title: \"Valeurs\",\n      subtitle: \"Donn\\xE9es \\xE0 agr\\xE9ger\",\n      icon: \"\\uD83D\\uDC8E\",\n      acceptedColumn: values,\n      onClear: onClearValues\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 339,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour une zone de drop compacte dans la barre fixe\n_c5 = DropZonesPanel;\nexport const CompactDropZone = ({\n  id,\n  title,\n  icon,\n  acceptedColumn,\n  onClear,\n  isOver\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n        flex items-center justify-between p-3 rounded-lg border-2 border-dashed min-h-[60px]\n        transition-all duration-300 ease-in-out transform\n        ${isOver ? 'border-purple-400 bg-purple-600/20 scale-105 drop-zone-glow drop-zone-hover' : 'border-gray-600 bg-gray-800/50 hover:border-gray-500 hover:bg-gray-800/70'}\n        ${acceptedColumn ? 'border-purple-500 bg-purple-600/30 shadow-lg shadow-purple-500/20' : ''}\n        backdrop-blur-sm hover:backdrop-blur-md\n      `,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2 flex-1 min-w-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: `text-lg transition-transform duration-200 ${isOver ? 'scale-110' : ''}`,\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-w-0 flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: `font-medium text-sm truncate transition-colors duration-200 ${isOver ? 'text-purple-200' : 'text-gray-200'}`,\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this), acceptedColumn ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1 mt-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-purple-300 truncate font-medium\",\n            children: acceptedColumn.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 15\n          }, this), acceptedColumn.table && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-400\",\n            children: [\"(\", acceptedColumn.table, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-xs transition-colors duration-200 ${isOver ? 'text-purple-300' : 'text-gray-400'}`,\n          children: isOver ? 'Relâchez ici' : 'Glissez ici'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 7\n    }, this), acceptedColumn && onClear && /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onClear,\n      className: \"text-gray-400 hover:text-red-400 transition-all duration-200 ml-2 p-1 hover:bg-red-600/20 rounded hover:scale-110 active:scale-95\",\n      title: \"Supprimer\",\n      children: \"\\u2715\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 425,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 382,\n    columnNumber: 5\n  }, this);\n};\n\n// Barre fixe avec les zones de drop\n_c6 = CompactDropZone;\nexport const FixedDropBar = ({\n  xAxis,\n  yAxis,\n  legend,\n  values,\n  onClearX,\n  onClearY,\n  onClearLegend,\n  onClearValues,\n  isVisible = true,\n  position = 'bottom' // 'top' ou 'bottom'\n}) => {\n  _s4();\n  const {\n    isOver: isOverX,\n    setNodeRef: setNodeRefX\n  } = useDroppable({\n    id: 'x-axis-fixed',\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  const {\n    isOver: isOverY,\n    setNodeRef: setNodeRefY\n  } = useDroppable({\n    id: 'y-axis-fixed',\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  const {\n    isOver: isOverLegend,\n    setNodeRef: setNodeRefLegend\n  } = useDroppable({\n    id: 'legend-fixed',\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  const {\n    isOver: isOverValues,\n    setNodeRef: setNodeRefValues\n  } = useDroppable({\n    id: 'values-fixed',\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  if (!isVisible) return null;\n  const positionClasses = position === 'top' ? 'top-0 border-b' : 'bottom-0 border-t';\n  const hasAnyColumn = xAxis || yAxis || legend || values;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n      fixed left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-md ${positionClasses} border-gray-700\n      transition-all duration-300 ease-in-out shadow-2xl\n      ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'}\n    `,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 py-3 sm:py-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg animate-pulse\",\n            children: \"\\uD83C\\uDFAF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-semibold text-gray-200\",\n              children: \"Zones de Drop\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 15\n            }, this), hasAnyColumn && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-purple-400\",\n              children: [[xAxis, yAxis, legend, values].filter(Boolean).length, \" colonne(s) assign\\xE9e(s)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden sm:block text-xs text-gray-400\",\n          children: \"Glissez vos colonnes ici pour cr\\xE9er votre visualisation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          ref: setNodeRefX,\n          children: /*#__PURE__*/_jsxDEV(CompactDropZone, {\n            id: \"x-axis-fixed\",\n            title: \"Axe X\",\n            icon: \"\\uD83D\\uDCCA\",\n            acceptedColumn: xAxis,\n            onClear: onClearX,\n            isOver: isOverX\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: setNodeRefY,\n          children: /*#__PURE__*/_jsxDEV(CompactDropZone, {\n            id: \"y-axis-fixed\",\n            title: \"Axe Y\",\n            icon: \"\\uD83D\\uDCC8\",\n            acceptedColumn: yAxis,\n            onClear: onClearY,\n            isOver: isOverY\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: setNodeRefLegend,\n          children: /*#__PURE__*/_jsxDEV(CompactDropZone, {\n            id: \"legend-fixed\",\n            title: \"L\\xE9gende\",\n            icon: \"\\uD83C\\uDFF7\\uFE0F\",\n            acceptedColumn: legend,\n            onClear: onClearLegend,\n            isOver: isOverLegend\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: setNodeRefValues,\n          children: /*#__PURE__*/_jsxDEV(CompactDropZone, {\n            id: \"values-fixed\",\n            title: \"Valeurs\",\n            icon: \"\\uD83D\\uDC8E\",\n            acceptedColumn: values,\n            onClear: onClearValues,\n            isOver: isOverValues\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sm:hidden mt-2 text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-500\",\n          children: \"Glissez ou utilisez le menu \\u26A1 des colonnes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 550,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 480,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour le sélecteur de fonction d'agrégation\n_s4(FixedDropBar, \"iDE4Z2x6jYrF9tSCiODbcpzg8qI=\", false, function () {\n  return [useDroppable, useDroppable, useDroppable, useDroppable];\n});\n_c7 = FixedDropBar;\nexport const AggregationSelector = ({\n  value,\n  onChange,\n  disabled\n}) => {\n  const aggregations = [{\n    value: 'SUM',\n    label: 'Somme',\n    icon: '➕',\n    description: 'Addition de toutes les valeurs'\n  }, {\n    value: 'AVG',\n    label: 'Moyenne',\n    icon: '📊',\n    description: 'Valeur moyenne'\n  }, {\n    value: 'COUNT',\n    label: 'Nombre',\n    icon: '🔢',\n    description: 'Nombre d\\'occurrences'\n  }, {\n    value: 'MIN',\n    label: 'Minimum',\n    icon: '⬇️',\n    description: 'Valeur minimale'\n  }, {\n    value: 'MAX',\n    label: 'Maximum',\n    icon: '⬆️',\n    description: 'Valeur maximale'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"block text-sm font-medium text-gray-300 mb-2\",\n      children: \"Fonction d'agr\\xE9gation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 572,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n      value: value,\n      onChange: e => onChange(e.target.value),\n      disabled: disabled,\n      className: \"w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-gray-100 focus:border-purple-500 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed\",\n      children: aggregations.map(agg => /*#__PURE__*/_jsxDEV(\"option\", {\n        value: agg.value,\n        children: [agg.icon, \" \", agg.label, \" - \", agg.description]\n      }, agg.value, true, {\n        fileName: _jsxFileName,\n        lineNumber: 582,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 575,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 571,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour le sélecteur de type de graphique\n_c8 = AggregationSelector;\nexport const ChartTypeSelector = ({\n  value,\n  onChange\n}) => {\n  const chartTypes = [{\n    value: 'bar',\n    label: 'Barres',\n    icon: '📊',\n    description: 'Graphique en barres'\n  }, {\n    value: 'line',\n    label: 'Ligne',\n    icon: '📈',\n    description: 'Graphique linéaire'\n  }, {\n    value: 'pie',\n    label: 'Circulaire',\n    icon: '🥧',\n    description: 'Graphique circulaire'\n  }, {\n    value: 'scatter',\n    label: 'Nuage',\n    icon: '⚫',\n    description: 'Nuage de points'\n  }, {\n    value: 'stacked_bar',\n    label: 'Barres empilées',\n    icon: '📚',\n    description: 'Barres empilées'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"block text-sm font-medium text-gray-300 mb-2\",\n      children: \"Type de graphique\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 603,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 md:grid-cols-3 gap-2\",\n      children: chartTypes.map(type => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onChange(type.value),\n        className: `p-3 rounded-lg border transition-colors text-center ${value === type.value ? 'border-purple-500 bg-purple-600/20 text-purple-300' : 'border-gray-700 bg-gray-800/50 text-gray-300 hover:border-gray-600'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg mb-1\",\n          children: type.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs font-medium\",\n          children: type.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 13\n        }, this)]\n      }, type.value, true, {\n        fileName: _jsxFileName,\n        lineNumber: 608,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 606,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 602,\n    columnNumber: 5\n  }, this);\n};\n_c9 = ChartTypeSelector;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"ColumnContextMenu\");\n$RefreshReg$(_c2, \"DraggableColumn\");\n$RefreshReg$(_c3, \"DropZone\");\n$RefreshReg$(_c4, \"ColumnsPanel\");\n$RefreshReg$(_c5, \"DropZonesPanel\");\n$RefreshReg$(_c6, \"CompactDropZone\");\n$RefreshReg$(_c7, \"FixedDropBar\");\n$RefreshReg$(_c8, \"AggregationSelector\");\n$RefreshReg$(_c9, \"ChartTypeSelector\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useDraggable", "useDroppable", "DarkBadge", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ColumnContextMenu", "isOpen", "onClose", "position", "column", "onAddToAxis", "selectedTable", "_s", "menuRef", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "menuItems", "id", "label", "icon", "color", "ref", "className", "style", "left", "x", "top", "y", "min<PERSON><PERSON><PERSON>", "children", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "map", "item", "onClick", "columnWithTable", "table", "_c", "DraggableColumn", "_s2", "contextMenu", "setContextMenu", "attributes", "listeners", "setNodeRef", "transform", "isDragging", "data", "opacity", "zIndex", "undefined", "handleContextMenu", "e", "preventDefault", "clientX", "clientY", "handleQuickAdd", "stopPropagation", "currentTarget", "getBoundingClientRect", "right", "getColumnIcon", "dataType", "toLowerCase", "includes", "getColumnColor", "onContextMenu", "title", "_c2", "DropZone", "subtitle", "acceptedColumn", "onClear", "_s3", "isOver", "accepts", "_c3", "ColumnsPanel", "columns", "length", "variant", "index", "_c4", "DropZonesPanel", "xAxis", "yAxis", "legend", "values", "onClearX", "onClearY", "onClearLegend", "onClearValues", "_c5", "CompactDropZone", "_c6", "FixedDropBar", "isVisible", "_s4", "isOverX", "setNodeRefX", "isOverY", "setNodeRefY", "isOverLegend", "setNodeRefLegend", "isOverValues", "setNodeRefValues", "positionClasses", "hasAnyColumn", "filter", "Boolean", "_c7", "AggregationSelector", "value", "onChange", "disabled", "aggregations", "description", "agg", "_c8", "ChartTypeSelector", "chartTypes", "_c9", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/AIZekri/frontend/src/components/DragDropComponents.js"], "sourcesContent": ["/**\n * DragDropComponents - Composants pour l'interface drag & drop\n * Utilise @dnd-kit pour créer des colonnes draggables et des zones de drop\n */\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useDraggable, useDroppable } from '@dnd-kit/core';\nimport { DarkBadge } from './YellowMindUI';\n\n// Composant de menu contextuel pour les colonnes\nexport const ColumnContextMenu = ({\n  isOpen,\n  onClose,\n  position,\n  column,\n  onAddToAxis,\n  selectedTable\n}) => {\n  const menuRef = useRef(null);\n\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (menuRef.current && !menuRef.current.contains(event.target)) {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => document.removeEventListener('mousedown', handleClickOutside);\n    }\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  const menuItems = [\n    { id: 'x-axis', label: 'Ajouter à Axe X', icon: '📊', color: 'text-blue-400' },\n    { id: 'y-axis', label: 'Ajouter à Axe Y', icon: '📈', color: 'text-green-400' },\n    { id: 'legend', label: 'Ajouter à Légende', icon: '🏷️', color: 'text-yellow-400' },\n    { id: 'values', label: 'Ajouter aux Valeurs', icon: '💎', color: 'text-purple-400' }\n  ];\n\n  return (\n    <div\n      ref={menuRef}\n      className=\"fixed z-50 bg-gray-800 border border-gray-600 rounded-lg shadow-xl backdrop-blur-sm\"\n      style={{\n        left: position.x,\n        top: position.y,\n        minWidth: '200px'\n      }}\n    >\n      <div className=\"p-2\">\n        <div className=\"px-3 py-2 border-b border-gray-600 mb-2\">\n          <p className=\"text-sm font-medium text-gray-200\">{column.name}</p>\n          <p className=\"text-xs text-gray-400\">{column.type}</p>\n        </div>\n\n        {menuItems.map((item) => (\n          <button\n            key={item.id}\n            onClick={() => {\n              const columnWithTable = { ...column, table: selectedTable };\n              onAddToAxis(item.id, columnWithTable);\n              onClose();\n            }}\n            className={`\n              w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left\n              hover:bg-gray-700 transition-colors duration-200 group\n            `}\n          >\n            <span className=\"text-lg\">{item.icon}</span>\n            <span className={`text-sm ${item.color} group-hover:text-gray-100`}>\n              {item.label}\n            </span>\n          </button>\n        ))}\n      </div>\n    </div>\n  );\n};\n\n// Composant pour une colonne draggable\nexport const DraggableColumn = ({ column, id, onAddToAxis, selectedTable }) => {\n  const [contextMenu, setContextMenu] = useState({ isOpen: false, position: { x: 0, y: 0 } });\n\n  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({\n    id: id,\n    data: {\n      column: column,\n      type: 'column'\n    }\n  });\n\n  const style = transform ? {\n    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,\n    opacity: isDragging ? 0.7 : 1,\n    zIndex: isDragging ? 1000 : 1\n  } : undefined;\n\n  const handleContextMenu = (e) => {\n    e.preventDefault();\n    setContextMenu({\n      isOpen: true,\n      position: { x: e.clientX, y: e.clientY }\n    });\n  };\n\n  const handleQuickAdd = (e) => {\n    e.stopPropagation();\n    setContextMenu({\n      isOpen: true,\n      position: {\n        x: e.currentTarget.getBoundingClientRect().right + 10,\n        y: e.currentTarget.getBoundingClientRect().top\n      }\n    });\n  };\n\n  const getColumnIcon = (type) => {\n    const dataType = type.toLowerCase();\n    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n      return '🔢';\n    }\n    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n      return '📝';\n    }\n    if (dataType.includes('date') || dataType.includes('time')) {\n      return '📅';\n    }\n    if (dataType.includes('bit') || dataType.includes('boolean')) {\n      return '☑️';\n    }\n    return '📊';\n  };\n\n  const getColumnColor = (type) => {\n    const dataType = type.toLowerCase();\n    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n      return 'bg-blue-600/20 border-blue-500 text-blue-300';\n    }\n    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n      return 'bg-green-600/20 border-green-500 text-green-300';\n    }\n    if (dataType.includes('date') || dataType.includes('time')) {\n      return 'bg-purple-600/20 border-purple-500 text-purple-300';\n    }\n    return 'bg-gray-600/20 border-gray-500 text-gray-300';\n  };\n\n  return (\n    <>\n      <div\n        ref={setNodeRef}\n        style={style}\n        {...listeners}\n        {...attributes}\n        onContextMenu={handleContextMenu}\n        className={`\n          p-3 rounded-lg border-2 border-dashed cursor-grab active:cursor-grabbing\n          transition-all duration-300 ease-in-out transform hover:scale-105 hover:shadow-lg\n          ${getColumnColor(column.type)}\n          ${isDragging ? 'shadow-2xl column-drag-start rotate-3 scale-110' : 'hover:shadow-md hover:shadow-purple-500/10'}\n          group relative\n        `}\n      >\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2 flex-1 min-w-0\">\n            <span className={`text-lg transition-transform duration-200 ${\n              isDragging ? 'scale-110' : 'group-hover:scale-105'\n            }`}>\n              {getColumnIcon(column.type)}\n            </span>\n            <div className=\"flex-1 min-w-0\">\n              <p className={`font-medium truncate transition-colors duration-200 ${\n                isDragging ? 'text-purple-200' : 'group-hover:text-gray-100'\n              }`}>\n                {column.name}\n              </p>\n              <p className={`text-xs transition-all duration-200 ${\n                isDragging ? 'opacity-90 text-purple-300' : 'opacity-75 group-hover:opacity-90'\n              }`}>\n                {column.type}\n              </p>\n            </div>\n          </div>\n\n          {/* Bouton d'ajout rapide */}\n          {onAddToAxis && (\n            <button\n              onClick={handleQuickAdd}\n              className=\"opacity-0 group-hover:opacity-100 transition-all duration-200\n                         p-1 rounded hover:bg-gray-700 text-gray-400 hover:text-purple-400\n                         transform hover:scale-110 active:scale-95\"\n              title=\"Ajouter rapidement\"\n            >\n              ⚡\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Menu contextuel */}\n      <ColumnContextMenu\n        isOpen={contextMenu.isOpen}\n        onClose={() => setContextMenu({ ...contextMenu, isOpen: false })}\n        position={contextMenu.position}\n        column={column}\n        onAddToAxis={onAddToAxis}\n        selectedTable={selectedTable}\n      />\n    </>\n  );\n};\n\n// Composant pour une zone de drop\nexport const DropZone = ({ id, title, subtitle, icon, children, acceptedColumn, onClear }) => {\n  const { isOver, setNodeRef } = useDroppable({\n    id: id,\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n\n  return (\n    <div\n      ref={setNodeRef}\n      className={`\n        p-6 rounded-lg border-2 border-dashed min-h-[140px] transition-all duration-200\n        ${isOver\n          ? 'border-purple-400 bg-purple-600/10 scale-105'\n          : 'border-gray-600 bg-gray-800/30'\n        }\n        ${acceptedColumn ? 'border-purple-500 bg-purple-600/20' : ''}\n      `}\n    >\n      {/* Header de la zone */}\n      <div className=\"flex items-center justify-between mb-3\">\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-lg\">{icon}</span>\n          <div>\n            <h4 className=\"font-medium text-gray-200\">{title}</h4>\n            {subtitle && <p className=\"text-xs text-gray-400\">{subtitle}</p>}\n          </div>\n        </div>\n        {acceptedColumn && onClear && (\n          <button\n            onClick={onClear}\n            className=\"text-gray-400 hover:text-red-400 transition-colors\"\n            title=\"Supprimer\"\n          >\n            ✕\n          </button>\n        )}\n      </div>\n\n      {/* Contenu de la zone */}\n      <div className=\"min-h-[60px] flex items-center justify-center\">\n        {acceptedColumn ? (\n          <div className=\"w-full\">\n            <div className=\"flex items-center space-x-3 p-3 bg-purple-600/30 rounded-lg border border-purple-500\">\n              <span className=\"text-xl\">\n                {acceptedColumn.type.toLowerCase().includes('int') ||\n                 acceptedColumn.type.toLowerCase().includes('decimal') ||\n                 acceptedColumn.type.toLowerCase().includes('float') ? '🔢' :\n                 acceptedColumn.type.toLowerCase().includes('date') ? '📅' : '📝'}\n              </span>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"font-medium text-purple-200 truncate\">{acceptedColumn.name}</p>\n                <p className=\"text-xs text-purple-300\">{acceptedColumn.type}</p>\n                {acceptedColumn.table && (\n                  <p className=\"text-xs text-purple-400 mt-1\">📋 {acceptedColumn.table}</p>\n                )}\n              </div>\n            </div>\n          </div>\n        ) : (\n          <div className=\"text-center\">\n            <p className=\"text-gray-500 text-sm\">\n              {isOver ? 'Relâchez ici' : 'Glissez une colonne ici'}\n            </p>\n          </div>\n        )}\n      </div>\n\n      {children}\n    </div>\n  );\n};\n\n// Composant pour afficher les colonnes disponibles\nexport const ColumnsPanel = ({ columns, title, onAddToAxis, selectedTable }) => {\n  if (!columns || columns.length === 0) {\n    return (\n      <div className=\"text-center py-8\">\n        <div className=\"w-16 h-16 bg-gray-700 rounded-2xl mx-auto mb-4 flex items-center justify-center\">\n          <span className=\"text-2xl\">📋</span>\n        </div>\n        <p className=\"text-gray-400\">Aucune colonne disponible</p>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-200\">{title}</h3>\n        <DarkBadge variant=\"info\">{columns.length}</DarkBadge>\n      </div>\n\n      <div className=\"space-y-3 max-h-[500px] overflow-y-auto\">\n        {columns.map((column, index) => (\n          <DraggableColumn\n            key={`${column.name}-${index}`}\n            id={`column-${column.name}`}\n            column={column}\n            onAddToAxis={onAddToAxis}\n            selectedTable={selectedTable}\n          />\n        ))}\n      </div>\n    </div>\n  );\n};\n\n// Composant pour les zones de drop organisées\nexport const DropZonesPanel = ({\n  xAxis,\n  yAxis,\n  legend,\n  values,\n  onClearX,\n  onClearY,\n  onClearLegend,\n  onClearValues\n}) => {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n      <DropZone\n        id=\"x-axis\"\n        title=\"Axe X\"\n        subtitle=\"Catégories ou groupes\"\n        icon=\"📊\"\n        acceptedColumn={xAxis}\n        onClear={onClearX}\n      />\n\n      <DropZone\n        id=\"y-axis\"\n        title=\"Axe Y\"\n        subtitle=\"Valeurs numériques (optionnel)\"\n        icon=\"📈\"\n        acceptedColumn={yAxis}\n        onClear={onClearY}\n      />\n\n      <DropZone\n        id=\"legend\"\n        title=\"Légende\"\n        subtitle=\"Sous-catégories (optionnel)\"\n        icon=\"🏷️\"\n        acceptedColumn={legend}\n        onClear={onClearLegend}\n      />\n\n      <DropZone\n        id=\"values\"\n        title=\"Valeurs\"\n        subtitle=\"Données à agréger\"\n        icon=\"💎\"\n        acceptedColumn={values}\n        onClear={onClearValues}\n      />\n    </div>\n  );\n};\n\n// Composant pour une zone de drop compacte dans la barre fixe\nexport const CompactDropZone = ({ id, title, icon, acceptedColumn, onClear, isOver }) => {\n  return (\n    <div\n      className={`\n        flex items-center justify-between p-3 rounded-lg border-2 border-dashed min-h-[60px]\n        transition-all duration-300 ease-in-out transform\n        ${isOver\n          ? 'border-purple-400 bg-purple-600/20 scale-105 drop-zone-glow drop-zone-hover'\n          : 'border-gray-600 bg-gray-800/50 hover:border-gray-500 hover:bg-gray-800/70'\n        }\n        ${acceptedColumn ? 'border-purple-500 bg-purple-600/30 shadow-lg shadow-purple-500/20' : ''}\n        backdrop-blur-sm hover:backdrop-blur-md\n      `}\n    >\n      <div className=\"flex items-center space-x-2 flex-1 min-w-0\">\n        <span className={`text-lg transition-transform duration-200 ${isOver ? 'scale-110' : ''}`}>\n          {icon}\n        </span>\n        <div className=\"min-w-0 flex-1\">\n          <h4 className={`font-medium text-sm truncate transition-colors duration-200 ${\n            isOver ? 'text-purple-200' : 'text-gray-200'\n          }`}>\n            {title}\n          </h4>\n          {acceptedColumn ? (\n            <div className=\"flex items-center space-x-1 mt-1\">\n              <span className=\"text-xs text-purple-300 truncate font-medium\">\n                {acceptedColumn.name}\n              </span>\n              {acceptedColumn.table && (\n                <span className=\"text-xs text-gray-400\">\n                  ({acceptedColumn.table})\n                </span>\n              )}\n            </div>\n          ) : (\n            <p className={`text-xs transition-colors duration-200 ${\n              isOver ? 'text-purple-300' : 'text-gray-400'\n            }`}>\n              {isOver ? 'Relâchez ici' : 'Glissez ici'}\n            </p>\n          )}\n        </div>\n      </div>\n      {acceptedColumn && onClear && (\n        <button\n          onClick={onClear}\n          className=\"text-gray-400 hover:text-red-400 transition-all duration-200 ml-2 p-1\n                     hover:bg-red-600/20 rounded hover:scale-110 active:scale-95\"\n          title=\"Supprimer\"\n        >\n          ✕\n        </button>\n      )}\n    </div>\n  );\n};\n\n// Barre fixe avec les zones de drop\nexport const FixedDropBar = ({\n  xAxis,\n  yAxis,\n  legend,\n  values,\n  onClearX,\n  onClearY,\n  onClearLegend,\n  onClearValues,\n  isVisible = true,\n  position = 'bottom' // 'top' ou 'bottom'\n}) => {\n  const { isOver: isOverX, setNodeRef: setNodeRefX } = useDroppable({\n    id: 'x-axis-fixed',\n    data: { type: 'dropzone', accepts: ['column'] }\n  });\n\n  const { isOver: isOverY, setNodeRef: setNodeRefY } = useDroppable({\n    id: 'y-axis-fixed',\n    data: { type: 'dropzone', accepts: ['column'] }\n  });\n\n  const { isOver: isOverLegend, setNodeRef: setNodeRefLegend } = useDroppable({\n    id: 'legend-fixed',\n    data: { type: 'dropzone', accepts: ['column'] }\n  });\n\n  const { isOver: isOverValues, setNodeRef: setNodeRefValues } = useDroppable({\n    id: 'values-fixed',\n    data: { type: 'dropzone', accepts: ['column'] }\n  });\n\n  if (!isVisible) return null;\n\n  const positionClasses = position === 'top'\n    ? 'top-0 border-b'\n    : 'bottom-0 border-t';\n\n  const hasAnyColumn = xAxis || yAxis || legend || values;\n\n  return (\n    <div className={`\n      fixed left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-md ${positionClasses} border-gray-700\n      transition-all duration-300 ease-in-out shadow-2xl\n      ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'}\n    `}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 py-3 sm:py-4\">\n        <div className=\"flex items-center justify-between mb-3\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-lg animate-pulse\">🎯</span>\n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-200\">Zones de Drop</h3>\n              {hasAnyColumn && (\n                <p className=\"text-xs text-purple-400\">\n                  {[xAxis, yAxis, legend, values].filter(Boolean).length} colonne(s) assignée(s)\n                </p>\n              )}\n            </div>\n          </div>\n          <div className=\"hidden sm:block text-xs text-gray-400\">\n            Glissez vos colonnes ici pour créer votre visualisation\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3\">\n          <div ref={setNodeRefX}>\n            <CompactDropZone\n              id=\"x-axis-fixed\"\n              title=\"Axe X\"\n              icon=\"📊\"\n              acceptedColumn={xAxis}\n              onClear={onClearX}\n              isOver={isOverX}\n            />\n          </div>\n\n          <div ref={setNodeRefY}>\n            <CompactDropZone\n              id=\"y-axis-fixed\"\n              title=\"Axe Y\"\n              icon=\"📈\"\n              acceptedColumn={yAxis}\n              onClear={onClearY}\n              isOver={isOverY}\n            />\n          </div>\n\n          <div ref={setNodeRefLegend}>\n            <CompactDropZone\n              id=\"legend-fixed\"\n              title=\"Légende\"\n              icon=\"🏷️\"\n              acceptedColumn={legend}\n              onClear={onClearLegend}\n              isOver={isOverLegend}\n            />\n          </div>\n\n          <div ref={setNodeRefValues}>\n            <CompactDropZone\n              id=\"values-fixed\"\n              title=\"Valeurs\"\n              icon=\"💎\"\n              acceptedColumn={values}\n              onClear={onClearValues}\n              isOver={isOverValues}\n            />\n          </div>\n        </div>\n\n        {/* Indicateur mobile */}\n        <div className=\"sm:hidden mt-2 text-center\">\n          <p className=\"text-xs text-gray-500\">\n            Glissez ou utilisez le menu ⚡ des colonnes\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Composant pour le sélecteur de fonction d'agrégation\nexport const AggregationSelector = ({ value, onChange, disabled }) => {\n  const aggregations = [\n    { value: 'SUM', label: 'Somme', icon: '➕', description: 'Addition de toutes les valeurs' },\n    { value: 'AVG', label: 'Moyenne', icon: '📊', description: 'Valeur moyenne' },\n    { value: 'COUNT', label: 'Nombre', icon: '🔢', description: 'Nombre d\\'occurrences' },\n    { value: 'MIN', label: 'Minimum', icon: '⬇️', description: 'Valeur minimale' },\n    { value: 'MAX', label: 'Maximum', icon: '⬆️', description: 'Valeur maximale' }\n  ];\n\n  return (\n    <div>\n      <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n        Fonction d'agrégation\n      </label>\n      <select\n        value={value}\n        onChange={(e) => onChange(e.target.value)}\n        disabled={disabled}\n        className=\"w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-gray-100 focus:border-purple-500 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed\"\n      >\n        {aggregations.map((agg) => (\n          <option key={agg.value} value={agg.value}>\n            {agg.icon} {agg.label} - {agg.description}\n          </option>\n        ))}\n      </select>\n    </div>\n  );\n};\n\n// Composant pour le sélecteur de type de graphique\nexport const ChartTypeSelector = ({ value, onChange }) => {\n  const chartTypes = [\n    { value: 'bar', label: 'Barres', icon: '📊', description: 'Graphique en barres' },\n    { value: 'line', label: 'Ligne', icon: '📈', description: 'Graphique linéaire' },\n    { value: 'pie', label: 'Circulaire', icon: '🥧', description: 'Graphique circulaire' },\n    { value: 'scatter', label: 'Nuage', icon: '⚫', description: 'Nuage de points' },\n    { value: 'stacked_bar', label: 'Barres empilées', icon: '📚', description: 'Barres empilées' }\n  ];\n\n  return (\n    <div>\n      <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n        Type de graphique\n      </label>\n      <div className=\"grid grid-cols-2 md:grid-cols-3 gap-2\">\n        {chartTypes.map((type) => (\n          <button\n            key={type.value}\n            onClick={() => onChange(type.value)}\n            className={`p-3 rounded-lg border transition-colors text-center ${\n              value === type.value\n                ? 'border-purple-500 bg-purple-600/20 text-purple-300'\n                : 'border-gray-700 bg-gray-800/50 text-gray-300 hover:border-gray-600'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">{type.icon}</div>\n            <div className=\"text-xs font-medium\">{type.label}</div>\n          </button>\n        ))}\n      </div>\n    </div>\n  );\n};\n"], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,YAAY,EAAEC,YAAY,QAAQ,eAAe;AAC1D,SAASC,SAAS,QAAQ,gBAAgB;;AAE1C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAAC;EAChCC,MAAM;EACNC,OAAO;EACPC,QAAQ;EACRC,MAAM;EACNC,WAAW;EACXC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,OAAO,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAE5BC,SAAS,CAAC,MAAM;IACd,MAAMiB,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIF,OAAO,CAACG,OAAO,IAAI,CAACH,OAAO,CAACG,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QAC9DX,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAED,IAAID,MAAM,EAAE;MACVa,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;MAC1D,OAAO,MAAMK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC5E;EACF,CAAC,EAAE,CAACR,MAAM,EAAEC,OAAO,CAAC,CAAC;EAErB,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMgB,SAAS,GAAG,CAChB;IAAEC,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAC9E;IAAEH,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAiB,CAAC,EAC/E;IAAEH,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,mBAAmB;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAkB,CAAC,EACnF;IAAEH,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,qBAAqB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAkB,CAAC,CACrF;EAED,oBACExB,OAAA;IACEyB,GAAG,EAAEd,OAAQ;IACbe,SAAS,EAAC,qFAAqF;IAC/FC,KAAK,EAAE;MACLC,IAAI,EAAEtB,QAAQ,CAACuB,CAAC;MAChBC,GAAG,EAAExB,QAAQ,CAACyB,CAAC;MACfC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,eAEFjC,OAAA;MAAK0B,SAAS,EAAC,KAAK;MAAAO,QAAA,gBAClBjC,OAAA;QAAK0B,SAAS,EAAC,yCAAyC;QAAAO,QAAA,gBACtDjC,OAAA;UAAG0B,SAAS,EAAC,mCAAmC;UAAAO,QAAA,EAAE1B,MAAM,CAAC2B;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClEtC,OAAA;UAAG0B,SAAS,EAAC,uBAAuB;UAAAO,QAAA,EAAE1B,MAAM,CAACgC;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,EAELlB,SAAS,CAACoB,GAAG,CAAEC,IAAI,iBAClBzC,OAAA;QAEE0C,OAAO,EAAEA,CAAA,KAAM;UACb,MAAMC,eAAe,GAAG;YAAE,GAAGpC,MAAM;YAAEqC,KAAK,EAAEnC;UAAc,CAAC;UAC3DD,WAAW,CAACiC,IAAI,CAACpB,EAAE,EAAEsB,eAAe,CAAC;UACrCtC,OAAO,CAAC,CAAC;QACX,CAAE;QACFqB,SAAS,EAAE;AACvB;AACA;AACA,aAAc;QAAAO,QAAA,gBAEFjC,OAAA;UAAM0B,SAAS,EAAC,SAAS;UAAAO,QAAA,EAAEQ,IAAI,CAAClB;QAAI;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5CtC,OAAA;UAAM0B,SAAS,EAAE,WAAWe,IAAI,CAACjB,KAAK,4BAA6B;UAAAS,QAAA,EAChEQ,IAAI,CAACnB;QAAK;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA,GAdFG,IAAI,CAACpB,EAAE;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAeN,CACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA5B,EAAA,CAxEaP,iBAAiB;AAAA0C,EAAA,GAAjB1C,iBAAiB;AAyE9B,OAAO,MAAM2C,eAAe,GAAGA,CAAC;EAAEvC,MAAM;EAAEc,EAAE;EAAEb,WAAW;EAAEC;AAAc,CAAC,KAAK;EAAAsC,GAAA;EAC7E,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAC;IAAEW,MAAM,EAAE,KAAK;IAAEE,QAAQ,EAAE;MAAEuB,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE;EAAE,CAAC,CAAC;EAE3F,MAAM;IAAEmB,UAAU;IAAEC,SAAS;IAAEC,UAAU;IAAEC,SAAS;IAAEC;EAAW,CAAC,GAAG1D,YAAY,CAAC;IAChFyB,EAAE,EAAEA,EAAE;IACNkC,IAAI,EAAE;MACJhD,MAAM,EAAEA,MAAM;MACdgC,IAAI,EAAE;IACR;EACF,CAAC,CAAC;EAEF,MAAMZ,KAAK,GAAG0B,SAAS,GAAG;IACxBA,SAAS,EAAE,eAAeA,SAAS,CAACxB,CAAC,OAAOwB,SAAS,CAACtB,CAAC,QAAQ;IAC/DyB,OAAO,EAAEF,UAAU,GAAG,GAAG,GAAG,CAAC;IAC7BG,MAAM,EAAEH,UAAU,GAAG,IAAI,GAAG;EAC9B,CAAC,GAAGI,SAAS;EAEb,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBZ,cAAc,CAAC;MACb7C,MAAM,EAAE,IAAI;MACZE,QAAQ,EAAE;QAAEuB,CAAC,EAAE+B,CAAC,CAACE,OAAO;QAAE/B,CAAC,EAAE6B,CAAC,CAACG;MAAQ;IACzC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAIJ,CAAC,IAAK;IAC5BA,CAAC,CAACK,eAAe,CAAC,CAAC;IACnBhB,cAAc,CAAC;MACb7C,MAAM,EAAE,IAAI;MACZE,QAAQ,EAAE;QACRuB,CAAC,EAAE+B,CAAC,CAACM,aAAa,CAACC,qBAAqB,CAAC,CAAC,CAACC,KAAK,GAAG,EAAE;QACrDrC,CAAC,EAAE6B,CAAC,CAACM,aAAa,CAACC,qBAAqB,CAAC,CAAC,CAACrC;MAC7C;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMuC,aAAa,GAAI9B,IAAI,IAAK;IAC9B,MAAM+B,QAAQ,GAAG/B,IAAI,CAACgC,WAAW,CAAC,CAAC;IACnC,IAAID,QAAQ,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MACxH,OAAO,IAAI;IACb;IACA,IAAIF,QAAQ,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1F,OAAO,IAAI;IACb;IACA,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1D,OAAO,IAAI;IACb;IACA,IAAIF,QAAQ,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;MAC5D,OAAO,IAAI;IACb;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMC,cAAc,GAAIlC,IAAI,IAAK;IAC/B,MAAM+B,QAAQ,GAAG/B,IAAI,CAACgC,WAAW,CAAC,CAAC;IACnC,IAAID,QAAQ,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MACxH,OAAO,8CAA8C;IACvD;IACA,IAAIF,QAAQ,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1F,OAAO,iDAAiD;IAC1D;IACA,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1D,OAAO,oDAAoD;IAC7D;IACA,OAAO,8CAA8C;EACvD,CAAC;EAED,oBACExE,OAAA,CAAAE,SAAA;IAAA+B,QAAA,gBACEjC,OAAA;MACEyB,GAAG,EAAE2B,UAAW;MAChBzB,KAAK,EAAEA,KAAM;MAAA,GACTwB,SAAS;MAAA,GACTD,UAAU;MACdwB,aAAa,EAAEf,iBAAkB;MACjCjC,SAAS,EAAE;AACnB;AACA;AACA,YAAY+C,cAAc,CAAClE,MAAM,CAACgC,IAAI,CAAC;AACvC,YAAYe,UAAU,GAAG,iDAAiD,GAAG,4CAA4C;AACzH;AACA,SAAU;MAAArB,QAAA,eAEFjC,OAAA;QAAK0B,SAAS,EAAC,mCAAmC;QAAAO,QAAA,gBAChDjC,OAAA;UAAK0B,SAAS,EAAC,4CAA4C;UAAAO,QAAA,gBACzDjC,OAAA;YAAM0B,SAAS,EAAE,6CACf4B,UAAU,GAAG,WAAW,GAAG,uBAAuB,EACjD;YAAArB,QAAA,EACAoC,aAAa,CAAC9D,MAAM,CAACgC,IAAI;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACPtC,OAAA;YAAK0B,SAAS,EAAC,gBAAgB;YAAAO,QAAA,gBAC7BjC,OAAA;cAAG0B,SAAS,EAAE,uDACZ4B,UAAU,GAAG,iBAAiB,GAAG,2BAA2B,EAC3D;cAAArB,QAAA,EACA1B,MAAM,CAAC2B;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACJtC,OAAA;cAAG0B,SAAS,EAAE,uCACZ4B,UAAU,GAAG,4BAA4B,GAAG,mCAAmC,EAC9E;cAAArB,QAAA,EACA1B,MAAM,CAACgC;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL9B,WAAW,iBACVR,OAAA;UACE0C,OAAO,EAAEsB,cAAe;UACxBtC,SAAS,EAAC,2KAE2C;UACrDiD,KAAK,EAAC,oBAAoB;UAAA1C,QAAA,EAC3B;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA,CAACG,iBAAiB;MAChBC,MAAM,EAAE4C,WAAW,CAAC5C,MAAO;MAC3BC,OAAO,EAAEA,CAAA,KAAM4C,cAAc,CAAC;QAAE,GAAGD,WAAW;QAAE5C,MAAM,EAAE;MAAM,CAAC,CAAE;MACjEE,QAAQ,EAAE0C,WAAW,CAAC1C,QAAS;MAC/BC,MAAM,EAAEA,MAAO;MACfC,WAAW,EAAEA,WAAY;MACzBC,aAAa,EAAEA;IAAc;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC;EAAA,eACF,CAAC;AAEP,CAAC;;AAED;AAAAS,GAAA,CApIaD,eAAe;EAAA,QAG2ClD,YAAY;AAAA;AAAAgF,GAAA,GAHtE9B,eAAe;AAqI5B,OAAO,MAAM+B,QAAQ,GAAGA,CAAC;EAAExD,EAAE;EAAEsD,KAAK;EAAEG,QAAQ;EAAEvD,IAAI;EAAEU,QAAQ;EAAE8C,cAAc;EAAEC;AAAQ,CAAC,KAAK;EAAAC,GAAA;EAC5F,MAAM;IAAEC,MAAM;IAAE9B;EAAW,CAAC,GAAGvD,YAAY,CAAC;IAC1CwB,EAAE,EAAEA,EAAE;IACNkC,IAAI,EAAE;MACJhB,IAAI,EAAE,UAAU;MAChB4C,OAAO,EAAE,CAAC,QAAQ;IACpB;EACF,CAAC,CAAC;EAEF,oBACEnF,OAAA;IACEyB,GAAG,EAAE2B,UAAW;IAChB1B,SAAS,EAAE;AACjB;AACA,UAAUwD,MAAM,GACJ,8CAA8C,GAC9C,gCAAgC;AAC5C,UACUH,cAAc,GAAG,oCAAoC,GAAG,EAAE;AACpE,OAAQ;IAAA9C,QAAA,gBAGFjC,OAAA;MAAK0B,SAAS,EAAC,wCAAwC;MAAAO,QAAA,gBACrDjC,OAAA;QAAK0B,SAAS,EAAC,6BAA6B;QAAAO,QAAA,gBAC1CjC,OAAA;UAAM0B,SAAS,EAAC,SAAS;UAAAO,QAAA,EAAEV;QAAI;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvCtC,OAAA;UAAAiC,QAAA,gBACEjC,OAAA;YAAI0B,SAAS,EAAC,2BAA2B;YAAAO,QAAA,EAAE0C;UAAK;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EACrDwC,QAAQ,iBAAI9E,OAAA;YAAG0B,SAAS,EAAC,uBAAuB;YAAAO,QAAA,EAAE6C;UAAQ;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACLyC,cAAc,IAAIC,OAAO,iBACxBhF,OAAA;QACE0C,OAAO,EAAEsC,OAAQ;QACjBtD,SAAS,EAAC,oDAAoD;QAC9DiD,KAAK,EAAC,WAAW;QAAA1C,QAAA,EAClB;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNtC,OAAA;MAAK0B,SAAS,EAAC,+CAA+C;MAAAO,QAAA,EAC3D8C,cAAc,gBACb/E,OAAA;QAAK0B,SAAS,EAAC,QAAQ;QAAAO,QAAA,eACrBjC,OAAA;UAAK0B,SAAS,EAAC,sFAAsF;UAAAO,QAAA,gBACnGjC,OAAA;YAAM0B,SAAS,EAAC,SAAS;YAAAO,QAAA,EACtB8C,cAAc,CAACxC,IAAI,CAACgC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,IACjDO,cAAc,CAACxC,IAAI,CAACgC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,SAAS,CAAC,IACrDO,cAAc,CAACxC,IAAI,CAACgC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,GAAG,IAAI,GAC1DO,cAAc,CAACxC,IAAI,CAACgC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG;UAAI;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACPtC,OAAA;YAAK0B,SAAS,EAAC,gBAAgB;YAAAO,QAAA,gBAC7BjC,OAAA;cAAG0B,SAAS,EAAC,sCAAsC;cAAAO,QAAA,EAAE8C,cAAc,CAAC7C;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7EtC,OAAA;cAAG0B,SAAS,EAAC,yBAAyB;cAAAO,QAAA,EAAE8C,cAAc,CAACxC;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC/DyC,cAAc,CAACnC,KAAK,iBACnB5C,OAAA;cAAG0B,SAAS,EAAC,8BAA8B;cAAAO,QAAA,GAAC,eAAG,EAAC8C,cAAc,CAACnC,KAAK;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACzE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAENtC,OAAA;QAAK0B,SAAS,EAAC,aAAa;QAAAO,QAAA,eAC1BjC,OAAA;UAAG0B,SAAS,EAAC,uBAAuB;UAAAO,QAAA,EACjCiD,MAAM,GAAG,cAAc,GAAG;QAAyB;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELL,QAAQ;EAAA;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AAAA2C,GAAA,CA3EaJ,QAAQ;EAAA,QACYhF,YAAY;AAAA;AAAAuF,GAAA,GADhCP,QAAQ;AA4ErB,OAAO,MAAMQ,YAAY,GAAGA,CAAC;EAAEC,OAAO;EAAEX,KAAK;EAAEnE,WAAW;EAAEC;AAAc,CAAC,KAAK;EAC9E,IAAI,CAAC6E,OAAO,IAAIA,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;IACpC,oBACEvF,OAAA;MAAK0B,SAAS,EAAC,kBAAkB;MAAAO,QAAA,gBAC/BjC,OAAA;QAAK0B,SAAS,EAAC,iFAAiF;QAAAO,QAAA,eAC9FjC,OAAA;UAAM0B,SAAS,EAAC,UAAU;UAAAO,QAAA,EAAC;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACNtC,OAAA;QAAG0B,SAAS,EAAC,eAAe;QAAAO,QAAA,EAAC;MAAyB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD,CAAC;EAEV;EAEA,oBACEtC,OAAA;IAAAiC,QAAA,gBACEjC,OAAA;MAAK0B,SAAS,EAAC,wCAAwC;MAAAO,QAAA,gBACrDjC,OAAA;QAAI0B,SAAS,EAAC,qCAAqC;QAAAO,QAAA,EAAE0C;MAAK;QAAAxC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAChEtC,OAAA,CAACF,SAAS;QAAC0F,OAAO,EAAC,MAAM;QAAAvD,QAAA,EAAEqD,OAAO,CAACC;MAAM;QAAApD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC,eAENtC,OAAA;MAAK0B,SAAS,EAAC,yCAAyC;MAAAO,QAAA,EACrDqD,OAAO,CAAC9C,GAAG,CAAC,CAACjC,MAAM,EAAEkF,KAAK,kBACzBzF,OAAA,CAAC8C,eAAe;QAEdzB,EAAE,EAAE,UAAUd,MAAM,CAAC2B,IAAI,EAAG;QAC5B3B,MAAM,EAAEA,MAAO;QACfC,WAAW,EAAEA,WAAY;QACzBC,aAAa,EAAEA;MAAc,GAJxB,GAAGF,MAAM,CAAC2B,IAAI,IAAIuD,KAAK,EAAE;QAAAtD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAK/B,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAoD,GAAA,GAlCaL,YAAY;AAmCzB,OAAO,MAAMM,cAAc,GAAGA,CAAC;EAC7BC,KAAK;EACLC,KAAK;EACLC,MAAM;EACNC,MAAM;EACNC,QAAQ;EACRC,QAAQ;EACRC,aAAa;EACbC;AACF,CAAC,KAAK;EACJ,oBACEnG,OAAA;IAAK0B,SAAS,EAAC,uCAAuC;IAAAO,QAAA,gBACpDjC,OAAA,CAAC6E,QAAQ;MACPxD,EAAE,EAAC,QAAQ;MACXsD,KAAK,EAAC,OAAO;MACbG,QAAQ,EAAC,0BAAuB;MAChCvD,IAAI,EAAC,cAAI;MACTwD,cAAc,EAAEa,KAAM;MACtBZ,OAAO,EAAEgB;IAAS;MAAA7D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eAEFtC,OAAA,CAAC6E,QAAQ;MACPxD,EAAE,EAAC,QAAQ;MACXsD,KAAK,EAAC,OAAO;MACbG,QAAQ,EAAC,mCAAgC;MACzCvD,IAAI,EAAC,cAAI;MACTwD,cAAc,EAAEc,KAAM;MACtBb,OAAO,EAAEiB;IAAS;MAAA9D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eAEFtC,OAAA,CAAC6E,QAAQ;MACPxD,EAAE,EAAC,QAAQ;MACXsD,KAAK,EAAC,YAAS;MACfG,QAAQ,EAAC,gCAA6B;MACtCvD,IAAI,EAAC,oBAAK;MACVwD,cAAc,EAAEe,MAAO;MACvBd,OAAO,EAAEkB;IAAc;MAAA/D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAEFtC,OAAA,CAAC6E,QAAQ;MACPxD,EAAE,EAAC,QAAQ;MACXsD,KAAK,EAAC,SAAS;MACfG,QAAQ,EAAC,4BAAmB;MAC5BvD,IAAI,EAAC,cAAI;MACTwD,cAAc,EAAEgB,MAAO;MACvBf,OAAO,EAAEmB;IAAc;MAAAhE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;;AAED;AAAA8D,GAAA,GAnDaT,cAAc;AAoD3B,OAAO,MAAMU,eAAe,GAAGA,CAAC;EAAEhF,EAAE;EAAEsD,KAAK;EAAEpD,IAAI;EAAEwD,cAAc;EAAEC,OAAO;EAAEE;AAAO,CAAC,KAAK;EACvF,oBACElF,OAAA;IACE0B,SAAS,EAAE;AACjB;AACA;AACA,UAAUwD,MAAM,GACJ,6EAA6E,GAC7E,2EAA2E;AACvF,UACUH,cAAc,GAAG,mEAAmE,GAAG,EAAE;AACnG;AACA,OAAQ;IAAA9C,QAAA,gBAEFjC,OAAA;MAAK0B,SAAS,EAAC,4CAA4C;MAAAO,QAAA,gBACzDjC,OAAA;QAAM0B,SAAS,EAAE,6CAA6CwD,MAAM,GAAG,WAAW,GAAG,EAAE,EAAG;QAAAjD,QAAA,EACvFV;MAAI;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACPtC,OAAA;QAAK0B,SAAS,EAAC,gBAAgB;QAAAO,QAAA,gBAC7BjC,OAAA;UAAI0B,SAAS,EAAE,+DACbwD,MAAM,GAAG,iBAAiB,GAAG,eAAe,EAC3C;UAAAjD,QAAA,EACA0C;QAAK;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EACJyC,cAAc,gBACb/E,OAAA;UAAK0B,SAAS,EAAC,kCAAkC;UAAAO,QAAA,gBAC/CjC,OAAA;YAAM0B,SAAS,EAAC,8CAA8C;YAAAO,QAAA,EAC3D8C,cAAc,CAAC7C;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,EACNyC,cAAc,CAACnC,KAAK,iBACnB5C,OAAA;YAAM0B,SAAS,EAAC,uBAAuB;YAAAO,QAAA,GAAC,GACrC,EAAC8C,cAAc,CAACnC,KAAK,EAAC,GACzB;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAENtC,OAAA;UAAG0B,SAAS,EAAE,0CACZwD,MAAM,GAAG,iBAAiB,GAAG,eAAe,EAC3C;UAAAjD,QAAA,EACAiD,MAAM,GAAG,cAAc,GAAG;QAAa;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EACLyC,cAAc,IAAIC,OAAO,iBACxBhF,OAAA;MACE0C,OAAO,EAAEsC,OAAQ;MACjBtD,SAAS,EAAC,mIAC6D;MACvEiD,KAAK,EAAC,WAAW;MAAA1C,QAAA,EAClB;IAED;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CACT;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAgE,GAAA,GA1DaD,eAAe;AA2D5B,OAAO,MAAME,YAAY,GAAGA,CAAC;EAC3BX,KAAK;EACLC,KAAK;EACLC,MAAM;EACNC,MAAM;EACNC,QAAQ;EACRC,QAAQ;EACRC,aAAa;EACbC,aAAa;EACbK,SAAS,GAAG,IAAI;EAChBlG,QAAQ,GAAG,QAAQ,CAAC;AACtB,CAAC,KAAK;EAAAmG,GAAA;EACJ,MAAM;IAAEvB,MAAM,EAAEwB,OAAO;IAAEtD,UAAU,EAAEuD;EAAY,CAAC,GAAG9G,YAAY,CAAC;IAChEwB,EAAE,EAAE,cAAc;IAClBkC,IAAI,EAAE;MAAEhB,IAAI,EAAE,UAAU;MAAE4C,OAAO,EAAE,CAAC,QAAQ;IAAE;EAChD,CAAC,CAAC;EAEF,MAAM;IAAED,MAAM,EAAE0B,OAAO;IAAExD,UAAU,EAAEyD;EAAY,CAAC,GAAGhH,YAAY,CAAC;IAChEwB,EAAE,EAAE,cAAc;IAClBkC,IAAI,EAAE;MAAEhB,IAAI,EAAE,UAAU;MAAE4C,OAAO,EAAE,CAAC,QAAQ;IAAE;EAChD,CAAC,CAAC;EAEF,MAAM;IAAED,MAAM,EAAE4B,YAAY;IAAE1D,UAAU,EAAE2D;EAAiB,CAAC,GAAGlH,YAAY,CAAC;IAC1EwB,EAAE,EAAE,cAAc;IAClBkC,IAAI,EAAE;MAAEhB,IAAI,EAAE,UAAU;MAAE4C,OAAO,EAAE,CAAC,QAAQ;IAAE;EAChD,CAAC,CAAC;EAEF,MAAM;IAAED,MAAM,EAAE8B,YAAY;IAAE5D,UAAU,EAAE6D;EAAiB,CAAC,GAAGpH,YAAY,CAAC;IAC1EwB,EAAE,EAAE,cAAc;IAClBkC,IAAI,EAAE;MAAEhB,IAAI,EAAE,UAAU;MAAE4C,OAAO,EAAE,CAAC,QAAQ;IAAE;EAChD,CAAC,CAAC;EAEF,IAAI,CAACqB,SAAS,EAAE,OAAO,IAAI;EAE3B,MAAMU,eAAe,GAAG5G,QAAQ,KAAK,KAAK,GACtC,gBAAgB,GAChB,mBAAmB;EAEvB,MAAM6G,YAAY,GAAGvB,KAAK,IAAIC,KAAK,IAAIC,MAAM,IAAIC,MAAM;EAEvD,oBACE/F,OAAA;IAAK0B,SAAS,EAAE;AACpB,kEAAkEwF,eAAe;AACjF;AACA,QAAQV,SAAS,GAAG,2BAA2B,GAAG,4BAA4B;AAC9E,KAAM;IAAAvE,QAAA,eACAjC,OAAA;MAAK0B,SAAS,EAAC,6CAA6C;MAAAO,QAAA,gBAC1DjC,OAAA;QAAK0B,SAAS,EAAC,wCAAwC;QAAAO,QAAA,gBACrDjC,OAAA;UAAK0B,SAAS,EAAC,6BAA6B;UAAAO,QAAA,gBAC1CjC,OAAA;YAAM0B,SAAS,EAAC,uBAAuB;YAAAO,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjDtC,OAAA;YAAAiC,QAAA,gBACEjC,OAAA;cAAI0B,SAAS,EAAC,qCAAqC;cAAAO,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACrE6E,YAAY,iBACXnH,OAAA;cAAG0B,SAAS,EAAC,yBAAyB;cAAAO,QAAA,GACnC,CAAC2D,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,CAAC,CAACqB,MAAM,CAACC,OAAO,CAAC,CAAC9B,MAAM,EAAC,4BACzD;YAAA;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtC,OAAA;UAAK0B,SAAS,EAAC,uCAAuC;UAAAO,QAAA,EAAC;QAEvD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtC,OAAA;QAAK0B,SAAS,EAAC,gDAAgD;QAAAO,QAAA,gBAC7DjC,OAAA;UAAKyB,GAAG,EAAEkF,WAAY;UAAA1E,QAAA,eACpBjC,OAAA,CAACqG,eAAe;YACdhF,EAAE,EAAC,cAAc;YACjBsD,KAAK,EAAC,OAAO;YACbpD,IAAI,EAAC,cAAI;YACTwD,cAAc,EAAEa,KAAM;YACtBZ,OAAO,EAAEgB,QAAS;YAClBd,MAAM,EAAEwB;UAAQ;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtC,OAAA;UAAKyB,GAAG,EAAEoF,WAAY;UAAA5E,QAAA,eACpBjC,OAAA,CAACqG,eAAe;YACdhF,EAAE,EAAC,cAAc;YACjBsD,KAAK,EAAC,OAAO;YACbpD,IAAI,EAAC,cAAI;YACTwD,cAAc,EAAEc,KAAM;YACtBb,OAAO,EAAEiB,QAAS;YAClBf,MAAM,EAAE0B;UAAQ;YAAAzE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtC,OAAA;UAAKyB,GAAG,EAAEsF,gBAAiB;UAAA9E,QAAA,eACzBjC,OAAA,CAACqG,eAAe;YACdhF,EAAE,EAAC,cAAc;YACjBsD,KAAK,EAAC,YAAS;YACfpD,IAAI,EAAC,oBAAK;YACVwD,cAAc,EAAEe,MAAO;YACvBd,OAAO,EAAEkB,aAAc;YACvBhB,MAAM,EAAE4B;UAAa;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtC,OAAA;UAAKyB,GAAG,EAAEwF,gBAAiB;UAAAhF,QAAA,eACzBjC,OAAA,CAACqG,eAAe;YACdhF,EAAE,EAAC,cAAc;YACjBsD,KAAK,EAAC,SAAS;YACfpD,IAAI,EAAC,cAAI;YACTwD,cAAc,EAAEgB,MAAO;YACvBf,OAAO,EAAEmB,aAAc;YACvBjB,MAAM,EAAE8B;UAAa;YAAA7E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtC,OAAA;QAAK0B,SAAS,EAAC,4BAA4B;QAAAO,QAAA,eACzCjC,OAAA;UAAG0B,SAAS,EAAC,uBAAuB;UAAAO,QAAA,EAAC;QAErC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAmE,GAAA,CAzHaF,YAAY;EAAA,QAY8B1G,YAAY,EAKZA,YAAY,EAKFA,YAAY,EAKZA,YAAY;AAAA;AAAAyH,GAAA,GA3BhEf,YAAY;AA0HzB,OAAO,MAAMgB,mBAAmB,GAAGA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EACpE,MAAMC,YAAY,GAAG,CACnB;IAAEH,KAAK,EAAE,KAAK;IAAElG,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,GAAG;IAAEqG,WAAW,EAAE;EAAiC,CAAC,EAC1F;IAAEJ,KAAK,EAAE,KAAK;IAAElG,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,IAAI;IAAEqG,WAAW,EAAE;EAAiB,CAAC,EAC7E;IAAEJ,KAAK,EAAE,OAAO;IAAElG,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE,IAAI;IAAEqG,WAAW,EAAE;EAAwB,CAAC,EACrF;IAAEJ,KAAK,EAAE,KAAK;IAAElG,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,IAAI;IAAEqG,WAAW,EAAE;EAAkB,CAAC,EAC9E;IAAEJ,KAAK,EAAE,KAAK;IAAElG,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,IAAI;IAAEqG,WAAW,EAAE;EAAkB,CAAC,CAC/E;EAED,oBACE5H,OAAA;IAAAiC,QAAA,gBACEjC,OAAA;MAAO0B,SAAS,EAAC,8CAA8C;MAAAO,QAAA,EAAC;IAEhE;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACRtC,OAAA;MACEwH,KAAK,EAAEA,KAAM;MACbC,QAAQ,EAAG7D,CAAC,IAAK6D,QAAQ,CAAC7D,CAAC,CAAC5C,MAAM,CAACwG,KAAK,CAAE;MAC1CE,QAAQ,EAAEA,QAAS;MACnBhG,SAAS,EAAC,mKAAmK;MAAAO,QAAA,EAE5K0F,YAAY,CAACnF,GAAG,CAAEqF,GAAG,iBACpB7H,OAAA;QAAwBwH,KAAK,EAAEK,GAAG,CAACL,KAAM;QAAAvF,QAAA,GACtC4F,GAAG,CAACtG,IAAI,EAAC,GAAC,EAACsG,GAAG,CAACvG,KAAK,EAAC,KAAG,EAACuG,GAAG,CAACD,WAAW;MAAA,GAD9BC,GAAG,CAACL,KAAK;QAAArF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEd,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AAAAwF,GAAA,GA9BaP,mBAAmB;AA+BhC,OAAO,MAAMQ,iBAAiB,GAAGA,CAAC;EAAEP,KAAK;EAAEC;AAAS,CAAC,KAAK;EACxD,MAAMO,UAAU,GAAG,CACjB;IAAER,KAAK,EAAE,KAAK;IAAElG,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE,IAAI;IAAEqG,WAAW,EAAE;EAAsB,CAAC,EACjF;IAAEJ,KAAK,EAAE,MAAM;IAAElG,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,IAAI;IAAEqG,WAAW,EAAE;EAAqB,CAAC,EAChF;IAAEJ,KAAK,EAAE,KAAK;IAAElG,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE,IAAI;IAAEqG,WAAW,EAAE;EAAuB,CAAC,EACtF;IAAEJ,KAAK,EAAE,SAAS;IAAElG,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,GAAG;IAAEqG,WAAW,EAAE;EAAkB,CAAC,EAC/E;IAAEJ,KAAK,EAAE,aAAa;IAAElG,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,IAAI;IAAEqG,WAAW,EAAE;EAAkB,CAAC,CAC/F;EAED,oBACE5H,OAAA;IAAAiC,QAAA,gBACEjC,OAAA;MAAO0B,SAAS,EAAC,8CAA8C;MAAAO,QAAA,EAAC;IAEhE;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACRtC,OAAA;MAAK0B,SAAS,EAAC,uCAAuC;MAAAO,QAAA,EACnD+F,UAAU,CAACxF,GAAG,CAAED,IAAI,iBACnBvC,OAAA;QAEE0C,OAAO,EAAEA,CAAA,KAAM+E,QAAQ,CAAClF,IAAI,CAACiF,KAAK,CAAE;QACpC9F,SAAS,EAAE,uDACT8F,KAAK,KAAKjF,IAAI,CAACiF,KAAK,GAChB,oDAAoD,GACpD,oEAAoE,EACvE;QAAAvF,QAAA,gBAEHjC,OAAA;UAAK0B,SAAS,EAAC,cAAc;UAAAO,QAAA,EAAEM,IAAI,CAAChB;QAAI;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/CtC,OAAA;UAAK0B,SAAS,EAAC,qBAAqB;UAAAO,QAAA,EAAEM,IAAI,CAACjB;QAAK;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,GATlDC,IAAI,CAACiF,KAAK;QAAArF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUT,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC2F,GAAA,GAhCWF,iBAAiB;AAAA,IAAAlF,EAAA,EAAA+B,GAAA,EAAAQ,GAAA,EAAAM,GAAA,EAAAU,GAAA,EAAAE,GAAA,EAAAgB,GAAA,EAAAQ,GAAA,EAAAG,GAAA;AAAAC,YAAA,CAAArF,EAAA;AAAAqF,YAAA,CAAAtD,GAAA;AAAAsD,YAAA,CAAA9C,GAAA;AAAA8C,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAA9B,GAAA;AAAA8B,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}