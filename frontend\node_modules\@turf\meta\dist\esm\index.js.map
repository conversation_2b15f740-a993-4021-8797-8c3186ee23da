{"version": 3, "sources": ["../../index.js"], "sourcesContent": ["import { feature, point, lineString, isObject } from \"@turf/helpers\";\n\n/**\n * Callback for coordEach\n *\n * @callback coordEachCallback\n * @param {number[]} currentCoord The current coordinate being processed.\n * @param {number} coordIndex The current index of the coordinate being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @param {number} multiFeatureIndex The current index of the Multi-Feature being processed.\n * @param {number} geometryIndex The current index of the Geometry being processed.\n * @returns {void}\n */\n\n/**\n * Iterate over coordinates in any GeoJSON object, similar to Array.forEach()\n *\n * @function\n * @param {AllGeoJSON} geojson any GeoJSON object\n * @param {coordEachCallback} callback a method that takes (currentCoord, coordIndex, featureIndex, multiFeatureIndex)\n * @param {boolean} [excludeWrapCoord=false] whether or not to include the final coordinate of LinearRings that wraps the ring in its iteration.\n * @returns {void}\n * @example\n * var features = turf.featureCollection([\n *   turf.point([26, 37], {\"foo\": \"bar\"}),\n *   turf.point([36, 53], {\"hello\": \"world\"})\n * ]);\n *\n * turf.coordEach(features, function (currentCoord, coordIndex, featureIndex, multiFeatureIndex, geometryIndex) {\n *   //=currentCoord\n *   //=coordIndex\n *   //=featureIndex\n *   //=multiFeatureIndex\n *   //=geometryIndex\n * });\n */\nfunction coordEach(geojson, callback, excludeWrapCoord) {\n  // Handles null Geometry -- Skips this GeoJSON\n  if (geojson === null) return;\n  var j,\n    k,\n    l,\n    geometry,\n    stopG,\n    coords,\n    geometryMaybeCollection,\n    wrapShrink = 0,\n    coordIndex = 0,\n    isGeometryCollection,\n    type = geojson.type,\n    isFeatureCollection = type === \"FeatureCollection\",\n    isFeature = type === \"Feature\",\n    stop = isFeatureCollection ? geojson.features.length : 1;\n\n  // This logic may look a little weird. The reason why it is that way\n  // is because it's trying to be fast. GeoJSON supports multiple kinds\n  // of objects at its root: FeatureCollection, Features, Geometries.\n  // This function has the responsibility of handling all of them, and that\n  // means that some of the `for` loops you see below actually just don't apply\n  // to certain inputs. For instance, if you give this just a\n  // Point geometry, then both loops are short-circuited and all we do\n  // is gradually rename the input until it's called 'geometry'.\n  //\n  // This also aims to allocate as few resources as possible: just a\n  // few numbers and booleans, rather than any temporary arrays as would\n  // be required with the normalization approach.\n  for (var featureIndex = 0; featureIndex < stop; featureIndex++) {\n    geometryMaybeCollection = isFeatureCollection\n      ? geojson.features[featureIndex].geometry\n      : isFeature\n        ? geojson.geometry\n        : geojson;\n    isGeometryCollection = geometryMaybeCollection\n      ? geometryMaybeCollection.type === \"GeometryCollection\"\n      : false;\n    stopG = isGeometryCollection\n      ? geometryMaybeCollection.geometries.length\n      : 1;\n\n    for (var geomIndex = 0; geomIndex < stopG; geomIndex++) {\n      var multiFeatureIndex = 0;\n      var geometryIndex = 0;\n      geometry = isGeometryCollection\n        ? geometryMaybeCollection.geometries[geomIndex]\n        : geometryMaybeCollection;\n\n      // Handles null Geometry -- Skips this geometry\n      if (geometry === null) continue;\n      coords = geometry.coordinates;\n      var geomType = geometry.type;\n\n      wrapShrink =\n        excludeWrapCoord &&\n        (geomType === \"Polygon\" || geomType === \"MultiPolygon\")\n          ? 1\n          : 0;\n\n      switch (geomType) {\n        case null:\n          break;\n        case \"Point\":\n          if (\n            callback(\n              coords,\n              coordIndex,\n              featureIndex,\n              multiFeatureIndex,\n              geometryIndex\n            ) === false\n          )\n            return false;\n          coordIndex++;\n          multiFeatureIndex++;\n          break;\n        case \"LineString\":\n        case \"MultiPoint\":\n          for (j = 0; j < coords.length; j++) {\n            if (\n              callback(\n                coords[j],\n                coordIndex,\n                featureIndex,\n                multiFeatureIndex,\n                geometryIndex\n              ) === false\n            )\n              return false;\n            coordIndex++;\n            if (geomType === \"MultiPoint\") multiFeatureIndex++;\n          }\n          if (geomType === \"LineString\") multiFeatureIndex++;\n          break;\n        case \"Polygon\":\n        case \"MultiLineString\":\n          for (j = 0; j < coords.length; j++) {\n            for (k = 0; k < coords[j].length - wrapShrink; k++) {\n              if (\n                callback(\n                  coords[j][k],\n                  coordIndex,\n                  featureIndex,\n                  multiFeatureIndex,\n                  geometryIndex\n                ) === false\n              )\n                return false;\n              coordIndex++;\n            }\n            if (geomType === \"MultiLineString\") multiFeatureIndex++;\n            if (geomType === \"Polygon\") geometryIndex++;\n          }\n          if (geomType === \"Polygon\") multiFeatureIndex++;\n          break;\n        case \"MultiPolygon\":\n          for (j = 0; j < coords.length; j++) {\n            geometryIndex = 0;\n            for (k = 0; k < coords[j].length; k++) {\n              for (l = 0; l < coords[j][k].length - wrapShrink; l++) {\n                if (\n                  callback(\n                    coords[j][k][l],\n                    coordIndex,\n                    featureIndex,\n                    multiFeatureIndex,\n                    geometryIndex\n                  ) === false\n                )\n                  return false;\n                coordIndex++;\n              }\n              geometryIndex++;\n            }\n            multiFeatureIndex++;\n          }\n          break;\n        case \"GeometryCollection\":\n          for (j = 0; j < geometry.geometries.length; j++)\n            if (\n              coordEach(geometry.geometries[j], callback, excludeWrapCoord) ===\n              false\n            )\n              return false;\n          break;\n        default:\n          throw new Error(\"Unknown Geometry Type\");\n      }\n    }\n  }\n}\n\n/**\n * Callback for coordReduce\n *\n * The first time the callback function is called, the values provided as arguments depend\n * on whether the reduce method has an initialValue argument.\n *\n * If an initialValue is provided to the reduce method:\n *  - The previousValue argument is initialValue.\n *  - The currentValue argument is the value of the first element present in the array.\n *\n * If an initialValue is not provided:\n *  - The previousValue argument is the value of the first element present in the array.\n *  - The currentValue argument is the value of the second element present in the array.\n *\n * @callback coordReduceCallback\n * @param {Reducer} previousValue The accumulated value previously returned in the last invocation\n * of the callback, or initialValue, if supplied.\n * @param {number[]} currentCoord The current coordinate being processed.\n * @param {number} coordIndex The current index of the coordinate being processed.\n * Starts at index 0, if an initialValue is provided, and at index 1 otherwise.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @param {number} multiFeatureIndex The current index of the Multi-Feature being processed.\n * @param {number} geometryIndex The current index of the Geometry being processed.\n * @returns {Reducer}\n */\n\n/**\n * Reduce coordinates in any GeoJSON object, similar to Array.reduce()\n *\n * @function\n * @param {AllGeoJSON} geojson any GeoJSON object\n * @param {coordReduceCallback} callback a method that takes (previousValue, currentCoord, coordIndex)\n * @param {Reducer} [initialValue] Value to use as the first argument to the first call of the callback.\n * @param {boolean} [excludeWrapCoord=false] whether or not to include the final coordinate of LinearRings that wraps the ring in its iteration.\n * @returns {Reducer} The value that results from the reduction.\n * @example\n * var features = turf.featureCollection([\n *   turf.point([26, 37], {\"foo\": \"bar\"}),\n *   turf.point([36, 53], {\"hello\": \"world\"})\n * ]);\n *\n * turf.coordReduce(features, function (previousValue, currentCoord, coordIndex, featureIndex, multiFeatureIndex, geometryIndex) {\n *   //=previousValue\n *   //=currentCoord\n *   //=coordIndex\n *   //=featureIndex\n *   //=multiFeatureIndex\n *   //=geometryIndex\n *   return currentCoord;\n * });\n */\nfunction coordReduce(geojson, callback, initialValue, excludeWrapCoord) {\n  var previousValue = initialValue;\n  coordEach(\n    geojson,\n    function (\n      currentCoord,\n      coordIndex,\n      featureIndex,\n      multiFeatureIndex,\n      geometryIndex\n    ) {\n      if (coordIndex === 0 && initialValue === undefined)\n        previousValue = currentCoord;\n      else\n        previousValue = callback(\n          previousValue,\n          currentCoord,\n          coordIndex,\n          featureIndex,\n          multiFeatureIndex,\n          geometryIndex\n        );\n    },\n    excludeWrapCoord\n  );\n  return previousValue;\n}\n\n/**\n * Callback for propEach\n *\n * @callback propEachCallback\n * @param {GeoJsonProperties} currentProperties The current Properties being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @returns {void}\n */\n\n/**\n * Iterate over properties in any GeoJSON object, similar to Array.forEach()\n *\n * @function\n * @param {FeatureCollection|Feature} geojson any GeoJSON object\n * @param {propEachCallback} callback a method that takes (currentProperties, featureIndex)\n * @returns {void}\n * @example\n * var features = turf.featureCollection([\n *     turf.point([26, 37], {foo: 'bar'}),\n *     turf.point([36, 53], {hello: 'world'})\n * ]);\n *\n * turf.propEach(features, function (currentProperties, featureIndex) {\n *   //=currentProperties\n *   //=featureIndex\n * });\n */\nfunction propEach(geojson, callback) {\n  var i;\n  switch (geojson.type) {\n    case \"FeatureCollection\":\n      for (i = 0; i < geojson.features.length; i++) {\n        if (callback(geojson.features[i].properties, i) === false) break;\n      }\n      break;\n    case \"Feature\":\n      callback(geojson.properties, 0);\n      break;\n  }\n}\n\n/**\n * Callback for propReduce\n *\n * The first time the callback function is called, the values provided as arguments depend\n * on whether the reduce method has an initialValue argument.\n *\n * If an initialValue is provided to the reduce method:\n *  - The previousValue argument is initialValue.\n *  - The currentValue argument is the value of the first element present in the array.\n *\n * If an initialValue is not provided:\n *  - The previousValue argument is the value of the first element present in the array.\n *  - The currentValue argument is the value of the second element present in the array.\n *\n * @callback propReduceCallback\n * @param {Reducer} previousValue The accumulated value previously returned in the last invocation\n * of the callback, or initialValue, if supplied.\n * @param {GeoJsonProperties} currentProperties The current Properties being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @returns {Reducer}\n */\n\n/**\n * Reduce properties in any GeoJSON object into a single value,\n * similar to how Array.reduce works. However, in this case we lazily run\n * the reduction, so an array of all properties is unnecessary.\n *\n * @function\n * @param {FeatureCollection|Feature|Geometry} geojson any GeoJSON object\n * @param {propReduceCallback} callback a method that takes (previousValue, currentProperties, featureIndex)\n * @param {Reducer} [initialValue] Value to use as the first argument to the first call of the callback.\n * @returns {Reducer} The value that results from the reduction.\n * @example\n * var features = turf.featureCollection([\n *     turf.point([26, 37], {foo: 'bar'}),\n *     turf.point([36, 53], {hello: 'world'})\n * ]);\n *\n * turf.propReduce(features, function (previousValue, currentProperties, featureIndex) {\n *   //=previousValue\n *   //=currentProperties\n *   //=featureIndex\n *   return currentProperties\n * });\n */\nfunction propReduce(geojson, callback, initialValue) {\n  var previousValue = initialValue;\n  propEach(geojson, function (currentProperties, featureIndex) {\n    if (featureIndex === 0 && initialValue === undefined)\n      previousValue = currentProperties;\n    else\n      previousValue = callback(previousValue, currentProperties, featureIndex);\n  });\n  return previousValue;\n}\n\n/**\n * Callback for featureEach\n *\n * @callback featureEachCallback\n * @param {Feature<any>} currentFeature The current Feature being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @returns {void}\n */\n\n/**\n * Iterate over features in any GeoJSON object, similar to\n * Array.forEach.\n *\n * @function\n * @param {FeatureCollection|Feature|Feature<GeometryCollection>} geojson any GeoJSON object\n * @param {featureEachCallback} callback a method that takes (currentFeature, featureIndex)\n * @returns {void}\n * @example\n * var features = turf.featureCollection([\n *   turf.point([26, 37], {foo: 'bar'}),\n *   turf.point([36, 53], {hello: 'world'})\n * ]);\n *\n * turf.featureEach(features, function (currentFeature, featureIndex) {\n *   //=currentFeature\n *   //=featureIndex\n * });\n */\nfunction featureEach(geojson, callback) {\n  if (geojson.type === \"Feature\") {\n    callback(geojson, 0);\n  } else if (geojson.type === \"FeatureCollection\") {\n    for (var i = 0; i < geojson.features.length; i++) {\n      if (callback(geojson.features[i], i) === false) break;\n    }\n  }\n}\n\n/**\n * Callback for featureReduce\n *\n * The first time the callback function is called, the values provided as arguments depend\n * on whether the reduce method has an initialValue argument.\n *\n * If an initialValue is provided to the reduce method:\n *  - The previousValue argument is initialValue.\n *  - The currentValue argument is the value of the first element present in the array.\n *\n * If an initialValue is not provided:\n *  - The previousValue argument is the value of the first element present in the array.\n *  - The currentValue argument is the value of the second element present in the array.\n *\n * @callback featureReduceCallback\n * @param {Reducer} previousValue The accumulated value previously returned in the last invocation\n * of the callback, or initialValue, if supplied.\n * @param {Feature} currentFeature The current Feature being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @returns {Reducer}\n */\n\n/**\n * Reduce features in any GeoJSON object, similar to Array.reduce().\n *\n * @function\n * @param {FeatureCollection|Feature|Feature<GeometryCollection>} geojson any GeoJSON object\n * @param {featureReduceCallback} callback a method that takes (previousValue, currentFeature, featureIndex)\n * @param {Reducer} [initialValue] Value to use as the first argument to the first call of the callback.\n * @returns {Reducer} The value that results from the reduction.\n * @example\n * var features = turf.featureCollection([\n *   turf.point([26, 37], {\"foo\": \"bar\"}),\n *   turf.point([36, 53], {\"hello\": \"world\"})\n * ]);\n *\n * turf.featureReduce(features, function (previousValue, currentFeature, featureIndex) {\n *   //=previousValue\n *   //=currentFeature\n *   //=featureIndex\n *   return currentFeature\n * });\n */\nfunction featureReduce(geojson, callback, initialValue) {\n  var previousValue = initialValue;\n  featureEach(geojson, function (currentFeature, featureIndex) {\n    if (featureIndex === 0 && initialValue === undefined)\n      previousValue = currentFeature;\n    else previousValue = callback(previousValue, currentFeature, featureIndex);\n  });\n  return previousValue;\n}\n\n/**\n * Get all coordinates from any GeoJSON object.\n *\n * @function\n * @param {AllGeoJSON} geojson any GeoJSON object\n * @returns {Array<Array<number>>} coordinate position array\n * @example\n * var features = turf.featureCollection([\n *   turf.point([26, 37], {foo: 'bar'}),\n *   turf.point([36, 53], {hello: 'world'})\n * ]);\n *\n * var coords = turf.coordAll(features);\n * //= [[26, 37], [36, 53]]\n */\nfunction coordAll(geojson) {\n  var coords = [];\n  coordEach(geojson, function (coord) {\n    coords.push(coord);\n  });\n  return coords;\n}\n\n/**\n * Callback for geomEach\n *\n * @callback geomEachCallback\n * @param {GeometryObject} currentGeometry The current Geometry being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @param {GeoJsonProperties} featureProperties The current Feature Properties being processed.\n * @param {BBox} featureBBox The current Feature BBox being processed.\n * @param {Id} featureId The current Feature Id being processed.\n * @returns {void}\n */\n\n/**\n * Iterate over each geometry in any GeoJSON object, similar to Array.forEach()\n *\n * @function\n * @param {FeatureCollection|Feature|Geometry|GeometryObject|Feature<GeometryCollection>} geojson any GeoJSON object\n * @param {geomEachCallback} callback a method that takes (currentGeometry, featureIndex, featureProperties, featureBBox, featureId)\n * @returns {void}\n * @example\n * var features = turf.featureCollection([\n *     turf.point([26, 37], {foo: 'bar'}),\n *     turf.point([36, 53], {hello: 'world'})\n * ]);\n *\n * turf.geomEach(features, function (currentGeometry, featureIndex, featureProperties, featureBBox, featureId) {\n *   //=currentGeometry\n *   //=featureIndex\n *   //=featureProperties\n *   //=featureBBox\n *   //=featureId\n * });\n */\nfunction geomEach(geojson, callback) {\n  var i,\n    j,\n    g,\n    geometry,\n    stopG,\n    geometryMaybeCollection,\n    isGeometryCollection,\n    featureProperties,\n    featureBBox,\n    featureId,\n    featureIndex = 0,\n    isFeatureCollection = geojson.type === \"FeatureCollection\",\n    isFeature = geojson.type === \"Feature\",\n    stop = isFeatureCollection ? geojson.features.length : 1;\n\n  // This logic may look a little weird. The reason why it is that way\n  // is because it's trying to be fast. GeoJSON supports multiple kinds\n  // of objects at its root: FeatureCollection, Features, Geometries.\n  // This function has the responsibility of handling all of them, and that\n  // means that some of the `for` loops you see below actually just don't apply\n  // to certain inputs. For instance, if you give this just a\n  // Point geometry, then both loops are short-circuited and all we do\n  // is gradually rename the input until it's called 'geometry'.\n  //\n  // This also aims to allocate as few resources as possible: just a\n  // few numbers and booleans, rather than any temporary arrays as would\n  // be required with the normalization approach.\n  for (i = 0; i < stop; i++) {\n    geometryMaybeCollection = isFeatureCollection\n      ? geojson.features[i].geometry\n      : isFeature\n        ? geojson.geometry\n        : geojson;\n    featureProperties = isFeatureCollection\n      ? geojson.features[i].properties\n      : isFeature\n        ? geojson.properties\n        : {};\n    featureBBox = isFeatureCollection\n      ? geojson.features[i].bbox\n      : isFeature\n        ? geojson.bbox\n        : undefined;\n    featureId = isFeatureCollection\n      ? geojson.features[i].id\n      : isFeature\n        ? geojson.id\n        : undefined;\n    isGeometryCollection = geometryMaybeCollection\n      ? geometryMaybeCollection.type === \"GeometryCollection\"\n      : false;\n    stopG = isGeometryCollection\n      ? geometryMaybeCollection.geometries.length\n      : 1;\n\n    for (g = 0; g < stopG; g++) {\n      geometry = isGeometryCollection\n        ? geometryMaybeCollection.geometries[g]\n        : geometryMaybeCollection;\n\n      // Handle null Geometry\n      if (geometry === null) {\n        if (\n          callback(\n            null,\n            featureIndex,\n            featureProperties,\n            featureBBox,\n            featureId\n          ) === false\n        )\n          return false;\n        continue;\n      }\n      switch (geometry.type) {\n        case \"Point\":\n        case \"LineString\":\n        case \"MultiPoint\":\n        case \"Polygon\":\n        case \"MultiLineString\":\n        case \"MultiPolygon\": {\n          if (\n            callback(\n              geometry,\n              featureIndex,\n              featureProperties,\n              featureBBox,\n              featureId\n            ) === false\n          )\n            return false;\n          break;\n        }\n        case \"GeometryCollection\": {\n          for (j = 0; j < geometry.geometries.length; j++) {\n            if (\n              callback(\n                geometry.geometries[j],\n                featureIndex,\n                featureProperties,\n                featureBBox,\n                featureId\n              ) === false\n            )\n              return false;\n          }\n          break;\n        }\n        default:\n          throw new Error(\"Unknown Geometry Type\");\n      }\n    }\n    // Only increase `featureIndex` per each feature\n    featureIndex++;\n  }\n}\n\n/**\n * Callback for geomReduce\n *\n * The first time the callback function is called, the values provided as arguments depend\n * on whether the reduce method has an initialValue argument.\n *\n * If an initialValue is provided to the reduce method:\n *  - The previousValue argument is initialValue.\n *  - The currentValue argument is the value of the first element present in the array.\n *\n * If an initialValue is not provided:\n *  - The previousValue argument is the value of the first element present in the array.\n *  - The currentValue argument is the value of the second element present in the array.\n *\n * @callback geomReduceCallback\n * @param {Reducer} previousValue The accumulated value previously returned in the last invocation\n * of the callback, or initialValue, if supplied.\n * @param {GeometryObject} currentGeometry The current Geometry being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @param {GeoJsonProperties} featureProperties The current Feature Properties being processed.\n * @param {BBox} featureBBox The current Feature BBox being processed.\n * @param {Id} featureId The current Feature Id being processed.\n * @returns {Reducer}\n */\n\n/**\n * Reduce geometry in any GeoJSON object, similar to Array.reduce().\n *\n * @function\n * @param {FeatureCollection|Feature|GeometryObject|GeometryCollection|Feature<GeometryCollection>} geojson any GeoJSON object\n * @param {geomReduceCallback} callback a method that takes (previousValue, currentGeometry, featureIndex, featureProperties, featureBBox, featureId)\n * @param {Reducer} [initialValue] Value to use as the first argument to the first call of the callback.\n * @returns {Reducer} The value that results from the reduction.\n * @example\n * var features = turf.featureCollection([\n *     turf.point([26, 37], {foo: 'bar'}),\n *     turf.point([36, 53], {hello: 'world'})\n * ]);\n *\n * turf.geomReduce(features, function (previousValue, currentGeometry, featureIndex, featureProperties, featureBBox, featureId) {\n *   //=previousValue\n *   //=currentGeometry\n *   //=featureIndex\n *   //=featureProperties\n *   //=featureBBox\n *   //=featureId\n *   return currentGeometry\n * });\n */\nfunction geomReduce(geojson, callback, initialValue) {\n  var previousValue = initialValue;\n  geomEach(\n    geojson,\n    function (\n      currentGeometry,\n      featureIndex,\n      featureProperties,\n      featureBBox,\n      featureId\n    ) {\n      if (featureIndex === 0 && initialValue === undefined)\n        previousValue = currentGeometry;\n      else\n        previousValue = callback(\n          previousValue,\n          currentGeometry,\n          featureIndex,\n          featureProperties,\n          featureBBox,\n          featureId\n        );\n    }\n  );\n  return previousValue;\n}\n\n/**\n * Callback for flattenEach\n *\n * @callback flattenEachCallback\n * @param {Feature} currentFeature The current flattened feature being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @param {number} multiFeatureIndex The current index of the Multi-Feature being processed.\n * @returns {void}\n */\n\n/**\n * Iterate over flattened features in any GeoJSON object, similar to\n * Array.forEach.\n *\n * @function\n * @param {FeatureCollection|Feature|GeometryObject|GeometryCollection|Feature<GeometryCollection>} geojson any GeoJSON object\n * @param {flattenEachCallback} callback a method that takes (currentFeature, featureIndex, multiFeatureIndex)\n * @returns {void}\n * @example\n * var features = turf.featureCollection([\n *     turf.point([26, 37], {foo: 'bar'}),\n *     turf.multiPoint([[40, 30], [36, 53]], {hello: 'world'})\n * ]);\n *\n * turf.flattenEach(features, function (currentFeature, featureIndex, multiFeatureIndex) {\n *   //=currentFeature\n *   //=featureIndex\n *   //=multiFeatureIndex\n * });\n */\nfunction flattenEach(geojson, callback) {\n  geomEach(geojson, function (geometry, featureIndex, properties, bbox, id) {\n    // Callback for single geometry\n    var type = geometry === null ? null : geometry.type;\n    switch (type) {\n      case null:\n      case \"Point\":\n      case \"LineString\":\n      case \"Polygon\":\n        if (\n          callback(\n            feature(geometry, properties, { bbox: bbox, id: id }),\n            featureIndex,\n            0\n          ) === false\n        )\n          return false;\n        return;\n    }\n\n    var geomType;\n\n    // Callback for multi-geometry\n    switch (type) {\n      case \"MultiPoint\":\n        geomType = \"Point\";\n        break;\n      case \"MultiLineString\":\n        geomType = \"LineString\";\n        break;\n      case \"MultiPolygon\":\n        geomType = \"Polygon\";\n        break;\n    }\n\n    for (\n      var multiFeatureIndex = 0;\n      multiFeatureIndex < geometry.coordinates.length;\n      multiFeatureIndex++\n    ) {\n      var coordinate = geometry.coordinates[multiFeatureIndex];\n      var geom = {\n        type: geomType,\n        coordinates: coordinate,\n      };\n      if (\n        callback(feature(geom, properties), featureIndex, multiFeatureIndex) ===\n        false\n      )\n        return false;\n    }\n  });\n}\n\n/**\n * Callback for flattenReduce\n *\n * The first time the callback function is called, the values provided as arguments depend\n * on whether the reduce method has an initialValue argument.\n *\n * If an initialValue is provided to the reduce method:\n *  - The previousValue argument is initialValue.\n *  - The currentValue argument is the value of the first element present in the array.\n *\n * If an initialValue is not provided:\n *  - The previousValue argument is the value of the first element present in the array.\n *  - The currentValue argument is the value of the second element present in the array.\n *\n * @callback flattenReduceCallback\n * @param {Reducer} previousValue The accumulated value previously returned in the last invocation\n * of the callback, or initialValue, if supplied.\n * @param {Feature} currentFeature The current Feature being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @param {number} multiFeatureIndex The current index of the Multi-Feature being processed.\n * @returns {Reducer}\n */\n\n/**\n * Reduce flattened features in any GeoJSON object, similar to Array.reduce().\n *\n * @function\n * @param {FeatureCollection|Feature|GeometryObject|GeometryCollection|Feature<GeometryCollection>} geojson any GeoJSON object\n * @param {flattenReduceCallback} callback a method that takes (previousValue, currentFeature, featureIndex, multiFeatureIndex)\n * @param {Reducer} [initialValue] Value to use as the first argument to the first call of the callback.\n * @returns {Reducer} The value that results from the reduction.\n * @example\n * var features = turf.featureCollection([\n *     turf.point([26, 37], {foo: 'bar'}),\n *     turf.multiPoint([[40, 30], [36, 53]], {hello: 'world'})\n * ]);\n *\n * turf.flattenReduce(features, function (previousValue, currentFeature, featureIndex, multiFeatureIndex) {\n *   //=previousValue\n *   //=currentFeature\n *   //=featureIndex\n *   //=multiFeatureIndex\n *   return currentFeature\n * });\n */\nfunction flattenReduce(geojson, callback, initialValue) {\n  var previousValue = initialValue;\n  flattenEach(\n    geojson,\n    function (currentFeature, featureIndex, multiFeatureIndex) {\n      if (\n        featureIndex === 0 &&\n        multiFeatureIndex === 0 &&\n        initialValue === undefined\n      )\n        previousValue = currentFeature;\n      else\n        previousValue = callback(\n          previousValue,\n          currentFeature,\n          featureIndex,\n          multiFeatureIndex\n        );\n    }\n  );\n  return previousValue;\n}\n\n/**\n * Callback for segmentEach\n *\n * @callback segmentEachCallback\n * @param {Feature<LineString>} currentSegment The current Segment being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @param {number} multiFeatureIndex The current index of the Multi-Feature being processed.\n * @param {number} geometryIndex The current index of the Geometry being processed.\n * @param {number} segmentIndex The current index of the Segment being processed.\n * @returns {void}\n */\n\n/**\n * Iterate over 2-vertex line segment in any GeoJSON object, similar to Array.forEach()\n * (Multi)Point geometries do not contain segments therefore they are ignored during this operation.\n *\n * @param {AllGeoJSON} geojson any GeoJSON\n * @param {segmentEachCallback} callback a method that takes (currentSegment, featureIndex, multiFeatureIndex, geometryIndex, segmentIndex)\n * @returns {void}\n * @example\n * var polygon = turf.polygon([[[-50, 5], [-40, -10], [-50, -10], [-40, 5], [-50, 5]]]);\n *\n * // Iterate over GeoJSON by 2-vertex segments\n * turf.segmentEach(polygon, function (currentSegment, featureIndex, multiFeatureIndex, geometryIndex, segmentIndex) {\n *   //=currentSegment\n *   //=featureIndex\n *   //=multiFeatureIndex\n *   //=geometryIndex\n *   //=segmentIndex\n * });\n *\n * // Calculate the total number of segments\n * var total = 0;\n * turf.segmentEach(polygon, function () {\n *     total++;\n * });\n */\nfunction segmentEach(geojson, callback) {\n  flattenEach(geojson, function (feature, featureIndex, multiFeatureIndex) {\n    var segmentIndex = 0;\n\n    // Exclude null Geometries\n    if (!feature.geometry) return;\n    // (Multi)Point geometries do not contain segments therefore they are ignored during this operation.\n    var type = feature.geometry.type;\n    if (type === \"Point\" || type === \"MultiPoint\") return;\n\n    // Generate 2-vertex line segments\n    var previousCoords;\n    var previousFeatureIndex = 0;\n    var previousMultiIndex = 0;\n    var prevGeomIndex = 0;\n    if (\n      coordEach(\n        feature,\n        function (\n          currentCoord,\n          coordIndex,\n          featureIndexCoord,\n          multiPartIndexCoord,\n          geometryIndex\n        ) {\n          // Simulating a meta.coordReduce() since `reduce` operations cannot be stopped by returning `false`\n          if (\n            previousCoords === undefined ||\n            featureIndex > previousFeatureIndex ||\n            multiPartIndexCoord > previousMultiIndex ||\n            geometryIndex > prevGeomIndex\n          ) {\n            previousCoords = currentCoord;\n            previousFeatureIndex = featureIndex;\n            previousMultiIndex = multiPartIndexCoord;\n            prevGeomIndex = geometryIndex;\n            segmentIndex = 0;\n            return;\n          }\n          var currentSegment = lineString(\n            [previousCoords, currentCoord],\n            feature.properties\n          );\n          if (\n            callback(\n              currentSegment,\n              featureIndex,\n              multiFeatureIndex,\n              geometryIndex,\n              segmentIndex\n            ) === false\n          )\n            return false;\n          segmentIndex++;\n          previousCoords = currentCoord;\n        }\n      ) === false\n    )\n      return false;\n  });\n}\n\n/**\n * Callback for segmentReduce\n *\n * The first time the callback function is called, the values provided as arguments depend\n * on whether the reduce method has an initialValue argument.\n *\n * If an initialValue is provided to the reduce method:\n *  - The previousValue argument is initialValue.\n *  - The currentValue argument is the value of the first element present in the array.\n *\n * If an initialValue is not provided:\n *  - The previousValue argument is the value of the first element present in the array.\n *  - The currentValue argument is the value of the second element present in the array.\n *\n * @callback segmentReduceCallback\n * @param {Reducer} previousValue The accumulated value previously returned in the last invocation\n * of the callback, or initialValue, if supplied.\n * @param {Feature<LineString>} currentSegment The current Segment being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @param {number} multiFeatureIndex The current index of the Multi-Feature being processed.\n * @param {number} geometryIndex The current index of the Geometry being processed.\n * @param {number} segmentIndex The current index of the Segment being processed.\n * @returns {Reducer}\n */\n\n/**\n * Reduce 2-vertex line segment in any GeoJSON object, similar to Array.reduce()\n * (Multi)Point geometries do not contain segments therefore they are ignored during this operation.\n *\n * @param {FeatureCollection|Feature|Geometry} geojson any GeoJSON\n * @param {segmentReduceCallback} callback a method that takes (previousValue, currentSegment, currentIndex)\n * @param {Reducer} [initialValue] Value to use as the first argument to the first call of the callback.\n * @returns {Reducer}\n * @example\n * var polygon = turf.polygon([[[-50, 5], [-40, -10], [-50, -10], [-40, 5], [-50, 5]]]);\n *\n * // Iterate over GeoJSON by 2-vertex segments\n * turf.segmentReduce(polygon, function (previousSegment, currentSegment, featureIndex, multiFeatureIndex, geometryIndex, segmentIndex) {\n *   //= previousSegment\n *   //= currentSegment\n *   //= featureIndex\n *   //= multiFeatureIndex\n *   //= geometryIndex\n *   //= segmentIndex\n *   return currentSegment\n * });\n *\n * // Calculate the total number of segments\n * var initialValue = 0\n * var total = turf.segmentReduce(polygon, function (previousValue) {\n *     previousValue++;\n *     return previousValue;\n * }, initialValue);\n */\nfunction segmentReduce(geojson, callback, initialValue) {\n  var previousValue = initialValue;\n  var started = false;\n  segmentEach(\n    geojson,\n    function (\n      currentSegment,\n      featureIndex,\n      multiFeatureIndex,\n      geometryIndex,\n      segmentIndex\n    ) {\n      if (started === false && initialValue === undefined)\n        previousValue = currentSegment;\n      else\n        previousValue = callback(\n          previousValue,\n          currentSegment,\n          featureIndex,\n          multiFeatureIndex,\n          geometryIndex,\n          segmentIndex\n        );\n      started = true;\n    }\n  );\n  return previousValue;\n}\n\n/**\n * Callback for lineEach\n *\n * @callback lineEachCallback\n * @param {Feature<LineString>} currentLine The current LineString|LinearRing being processed\n * @param {number} featureIndex The current index of the Feature being processed\n * @param {number} multiFeatureIndex The current index of the Multi-Feature being processed\n * @param {number} geometryIndex The current index of the Geometry being processed\n * @returns {void}\n */\n\n/**\n * Iterate over line or ring coordinates in LineString, Polygon, MultiLineString, MultiPolygon Features or Geometries,\n * similar to Array.forEach.\n *\n * @function\n * @param {FeatureCollection<Lines>|Feature<Lines>|Lines|Feature<GeometryCollection>|GeometryCollection} geojson object\n * @param {lineEachCallback} callback a method that takes (currentLine, featureIndex, multiFeatureIndex, geometryIndex)\n * @returns {void}\n * @example\n * var multiLine = turf.multiLineString([\n *   [[26, 37], [35, 45]],\n *   [[36, 53], [38, 50], [41, 55]]\n * ]);\n *\n * turf.lineEach(multiLine, function (currentLine, featureIndex, multiFeatureIndex, geometryIndex) {\n *   //=currentLine\n *   //=featureIndex\n *   //=multiFeatureIndex\n *   //=geometryIndex\n * });\n */\nfunction lineEach(geojson, callback) {\n  // validation\n  if (!geojson) throw new Error(\"geojson is required\");\n\n  flattenEach(geojson, function (feature, featureIndex, multiFeatureIndex) {\n    if (feature.geometry === null) return;\n    var type = feature.geometry.type;\n    var coords = feature.geometry.coordinates;\n    switch (type) {\n      case \"LineString\":\n        if (callback(feature, featureIndex, multiFeatureIndex, 0, 0) === false)\n          return false;\n        break;\n      case \"Polygon\":\n        for (\n          var geometryIndex = 0;\n          geometryIndex < coords.length;\n          geometryIndex++\n        ) {\n          if (\n            callback(\n              lineString(coords[geometryIndex], feature.properties),\n              featureIndex,\n              multiFeatureIndex,\n              geometryIndex\n            ) === false\n          )\n            return false;\n        }\n        break;\n    }\n  });\n}\n\n/**\n * Callback for lineReduce\n *\n * The first time the callback function is called, the values provided as arguments depend\n * on whether the reduce method has an initialValue argument.\n *\n * If an initialValue is provided to the reduce method:\n *  - The previousValue argument is initialValue.\n *  - The currentValue argument is the value of the first element present in the array.\n *\n * If an initialValue is not provided:\n *  - The previousValue argument is the value of the first element present in the array.\n *  - The currentValue argument is the value of the second element present in the array.\n *\n * @callback lineReduceCallback\n * @param {Reducer} previousValue The accumulated value previously returned in the last invocation\n * of the callback, or initialValue, if supplied.\n * @param {Feature<LineString>} currentLine The current LineString|LinearRing being processed.\n * @param {number} featureIndex The current index of the Feature being processed\n * @param {number} multiFeatureIndex The current index of the Multi-Feature being processed\n * @param {number} geometryIndex The current index of the Geometry being processed\n * @returns {Reducer}\n */\n\n/**\n * Reduce features in any GeoJSON object, similar to Array.reduce().\n *\n * @function\n * @param {FeatureCollection<Lines>|Feature<Lines>|Lines|Feature<GeometryCollection>|GeometryCollection} geojson object\n * @param {Function} callback a method that takes (previousValue, currentLine, featureIndex, multiFeatureIndex, geometryIndex)\n * @param {Reducer} [initialValue] Value to use as the first argument to the first call of the callback.\n * @returns {Reducer} The value that results from the reduction.\n * @example\n * var multiPoly = turf.multiPolygon([\n *   turf.polygon([[[12,48],[2,41],[24,38],[12,48]], [[9,44],[13,41],[13,45],[9,44]]]),\n *   turf.polygon([[[5, 5], [0, 0], [2, 2], [4, 4], [5, 5]]])\n * ]);\n *\n * turf.lineReduce(multiPoly, function (previousValue, currentLine, featureIndex, multiFeatureIndex, geometryIndex) {\n *   //=previousValue\n *   //=currentLine\n *   //=featureIndex\n *   //=multiFeatureIndex\n *   //=geometryIndex\n *   return currentLine\n * });\n */\nfunction lineReduce(geojson, callback, initialValue) {\n  var previousValue = initialValue;\n  lineEach(\n    geojson,\n    function (currentLine, featureIndex, multiFeatureIndex, geometryIndex) {\n      if (featureIndex === 0 && initialValue === undefined)\n        previousValue = currentLine;\n      else\n        previousValue = callback(\n          previousValue,\n          currentLine,\n          featureIndex,\n          multiFeatureIndex,\n          geometryIndex\n        );\n    }\n  );\n  return previousValue;\n}\n\n/**\n * Finds a particular 2-vertex LineString Segment from a GeoJSON using `@turf/meta` indexes.\n *\n * Negative indexes are permitted.\n * Point & MultiPoint will always return null.\n *\n * @param {FeatureCollection|Feature|Geometry} geojson Any GeoJSON Feature or Geometry\n * @param {Object} [options={}] Optional parameters\n * @param {number} [options.featureIndex=0] Feature Index\n * @param {number} [options.multiFeatureIndex=0] Multi-Feature Index\n * @param {number} [options.geometryIndex=0] Geometry Index\n * @param {number} [options.segmentIndex=0] Segment Index\n * @param {Object} [options.properties={}] Translate Properties to output LineString\n * @param {BBox} [options.bbox={}] Translate BBox to output LineString\n * @param {number|string} [options.id={}] Translate Id to output LineString\n * @returns {Feature<LineString>} 2-vertex GeoJSON Feature LineString\n * @example\n * var multiLine = turf.multiLineString([\n *     [[10, 10], [50, 30], [30, 40]],\n *     [[-10, -10], [-50, -30], [-30, -40]]\n * ]);\n *\n * // First Segment (defaults are 0)\n * turf.findSegment(multiLine);\n * // => Feature<LineString<[[10, 10], [50, 30]]>>\n *\n * // First Segment of 2nd Multi Feature\n * turf.findSegment(multiLine, {multiFeatureIndex: 1});\n * // => Feature<LineString<[[-10, -10], [-50, -30]]>>\n *\n * // Last Segment of Last Multi Feature\n * turf.findSegment(multiLine, {multiFeatureIndex: -1, segmentIndex: -1});\n * // => Feature<LineString<[[-50, -30], [-30, -40]]>>\n */\nfunction findSegment(geojson, options) {\n  // Optional Parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n  var featureIndex = options.featureIndex || 0;\n  var multiFeatureIndex = options.multiFeatureIndex || 0;\n  var geometryIndex = options.geometryIndex || 0;\n  var segmentIndex = options.segmentIndex || 0;\n\n  // Find FeatureIndex\n  var properties = options.properties;\n  var geometry;\n\n  switch (geojson.type) {\n    case \"FeatureCollection\":\n      if (featureIndex < 0)\n        featureIndex = geojson.features.length + featureIndex;\n      properties = properties || geojson.features[featureIndex].properties;\n      geometry = geojson.features[featureIndex].geometry;\n      break;\n    case \"Feature\":\n      properties = properties || geojson.properties;\n      geometry = geojson.geometry;\n      break;\n    case \"Point\":\n    case \"MultiPoint\":\n      return null;\n    case \"LineString\":\n    case \"Polygon\":\n    case \"MultiLineString\":\n    case \"MultiPolygon\":\n      geometry = geojson;\n      break;\n    default:\n      throw new Error(\"geojson is invalid\");\n  }\n\n  // Find SegmentIndex\n  if (geometry === null) return null;\n  var coords = geometry.coordinates;\n  switch (geometry.type) {\n    case \"Point\":\n    case \"MultiPoint\":\n      return null;\n    case \"LineString\":\n      if (segmentIndex < 0) segmentIndex = coords.length + segmentIndex - 1;\n      return lineString(\n        [coords[segmentIndex], coords[segmentIndex + 1]],\n        properties,\n        options\n      );\n    case \"Polygon\":\n      if (geometryIndex < 0) geometryIndex = coords.length + geometryIndex;\n      if (segmentIndex < 0)\n        segmentIndex = coords[geometryIndex].length + segmentIndex - 1;\n      return lineString(\n        [\n          coords[geometryIndex][segmentIndex],\n          coords[geometryIndex][segmentIndex + 1],\n        ],\n        properties,\n        options\n      );\n    case \"MultiLineString\":\n      if (multiFeatureIndex < 0)\n        multiFeatureIndex = coords.length + multiFeatureIndex;\n      if (segmentIndex < 0)\n        segmentIndex = coords[multiFeatureIndex].length + segmentIndex - 1;\n      return lineString(\n        [\n          coords[multiFeatureIndex][segmentIndex],\n          coords[multiFeatureIndex][segmentIndex + 1],\n        ],\n        properties,\n        options\n      );\n    case \"MultiPolygon\":\n      if (multiFeatureIndex < 0)\n        multiFeatureIndex = coords.length + multiFeatureIndex;\n      if (geometryIndex < 0)\n        geometryIndex = coords[multiFeatureIndex].length + geometryIndex;\n      if (segmentIndex < 0)\n        segmentIndex =\n          coords[multiFeatureIndex][geometryIndex].length - segmentIndex - 1;\n      return lineString(\n        [\n          coords[multiFeatureIndex][geometryIndex][segmentIndex],\n          coords[multiFeatureIndex][geometryIndex][segmentIndex + 1],\n        ],\n        properties,\n        options\n      );\n  }\n  throw new Error(\"geojson is invalid\");\n}\n\n/**\n * Finds a particular Point from a GeoJSON using `@turf/meta` indexes.\n *\n * Negative indexes are permitted.\n *\n * @param {FeatureCollection|Feature|Geometry} geojson Any GeoJSON Feature or Geometry\n * @param {Object} [options={}] Optional parameters\n * @param {number} [options.featureIndex=0] Feature Index\n * @param {number} [options.multiFeatureIndex=0] Multi-Feature Index\n * @param {number} [options.geometryIndex=0] Geometry Index\n * @param {number} [options.coordIndex=0] Coord Index\n * @param {Object} [options.properties={}] Translate Properties to output Point\n * @param {BBox} [options.bbox={}] Translate BBox to output Point\n * @param {number|string} [options.id={}] Translate Id to output Point\n * @returns {Feature<Point>} 2-vertex GeoJSON Feature Point\n * @example\n * var multiLine = turf.multiLineString([\n *     [[10, 10], [50, 30], [30, 40]],\n *     [[-10, -10], [-50, -30], [-30, -40]]\n * ]);\n *\n * // First Segment (defaults are 0)\n * turf.findPoint(multiLine);\n * // => Feature<Point<[10, 10]>>\n *\n * // First Segment of the 2nd Multi-Feature\n * turf.findPoint(multiLine, {multiFeatureIndex: 1});\n * // => Feature<Point<[-10, -10]>>\n *\n * // Last Segment of last Multi-Feature\n * turf.findPoint(multiLine, {multiFeatureIndex: -1, coordIndex: -1});\n * // => Feature<Point<[-30, -40]>>\n */\nfunction findPoint(geojson, options) {\n  // Optional Parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n  var featureIndex = options.featureIndex || 0;\n  var multiFeatureIndex = options.multiFeatureIndex || 0;\n  var geometryIndex = options.geometryIndex || 0;\n  var coordIndex = options.coordIndex || 0;\n\n  // Find FeatureIndex\n  var properties = options.properties;\n  var geometry;\n\n  switch (geojson.type) {\n    case \"FeatureCollection\":\n      if (featureIndex < 0)\n        featureIndex = geojson.features.length + featureIndex;\n      properties = properties || geojson.features[featureIndex].properties;\n      geometry = geojson.features[featureIndex].geometry;\n      break;\n    case \"Feature\":\n      properties = properties || geojson.properties;\n      geometry = geojson.geometry;\n      break;\n    case \"Point\":\n    case \"MultiPoint\":\n      return null;\n    case \"LineString\":\n    case \"Polygon\":\n    case \"MultiLineString\":\n    case \"MultiPolygon\":\n      geometry = geojson;\n      break;\n    default:\n      throw new Error(\"geojson is invalid\");\n  }\n\n  // Find Coord Index\n  if (geometry === null) return null;\n  var coords = geometry.coordinates;\n  switch (geometry.type) {\n    case \"Point\":\n      return point(coords, properties, options);\n    case \"MultiPoint\":\n      if (multiFeatureIndex < 0)\n        multiFeatureIndex = coords.length + multiFeatureIndex;\n      return point(coords[multiFeatureIndex], properties, options);\n    case \"LineString\":\n      if (coordIndex < 0) coordIndex = coords.length + coordIndex;\n      return point(coords[coordIndex], properties, options);\n    case \"Polygon\":\n      if (geometryIndex < 0) geometryIndex = coords.length + geometryIndex;\n      if (coordIndex < 0)\n        coordIndex = coords[geometryIndex].length + coordIndex;\n      return point(coords[geometryIndex][coordIndex], properties, options);\n    case \"MultiLineString\":\n      if (multiFeatureIndex < 0)\n        multiFeatureIndex = coords.length + multiFeatureIndex;\n      if (coordIndex < 0)\n        coordIndex = coords[multiFeatureIndex].length + coordIndex;\n      return point(coords[multiFeatureIndex][coordIndex], properties, options);\n    case \"MultiPolygon\":\n      if (multiFeatureIndex < 0)\n        multiFeatureIndex = coords.length + multiFeatureIndex;\n      if (geometryIndex < 0)\n        geometryIndex = coords[multiFeatureIndex].length + geometryIndex;\n      if (coordIndex < 0)\n        coordIndex =\n          coords[multiFeatureIndex][geometryIndex].length - coordIndex;\n      return point(\n        coords[multiFeatureIndex][geometryIndex][coordIndex],\n        properties,\n        options\n      );\n  }\n  throw new Error(\"geojson is invalid\");\n}\n\nexport {\n  coordReduce,\n  coordEach,\n  propEach,\n  propReduce,\n  featureReduce,\n  featureEach,\n  coordAll,\n  geomReduce,\n  geomEach,\n  flattenReduce,\n  flattenEach,\n  segmentReduce,\n  segmentEach,\n  lineReduce,\n  lineEach,\n  findSegment,\n  findPoint,\n};\n"], "mappings": ";AAAA,SAAS,SAAS,OAAO,YAAY,gBAAgB;AAoCrD,SAAS,UAAU,SAAS,UAAU,kBAAkB;AAEtD,MAAI,YAAY,KAAM;AACtB,MAAI,GACF,GACA,GACA,UACA,OACA,QACA,yBACA,aAAa,GACb,aAAa,GACb,sBACA,OAAO,QAAQ,MACf,sBAAsB,SAAS,qBAC/B,YAAY,SAAS,WACrB,OAAO,sBAAsB,QAAQ,SAAS,SAAS;AAczD,WAAS,eAAe,GAAG,eAAe,MAAM,gBAAgB;AAC9D,8BAA0B,sBACtB,QAAQ,SAAS,YAAY,EAAE,WAC/B,YACE,QAAQ,WACR;AACN,2BAAuB,0BACnB,wBAAwB,SAAS,uBACjC;AACJ,YAAQ,uBACJ,wBAAwB,WAAW,SACnC;AAEJ,aAAS,YAAY,GAAG,YAAY,OAAO,aAAa;AACtD,UAAI,oBAAoB;AACxB,UAAI,gBAAgB;AACpB,iBAAW,uBACP,wBAAwB,WAAW,SAAS,IAC5C;AAGJ,UAAI,aAAa,KAAM;AACvB,eAAS,SAAS;AAClB,UAAI,WAAW,SAAS;AAExB,mBACE,qBACC,aAAa,aAAa,aAAa,kBACpC,IACA;AAEN,cAAQ,UAAU;AAAA,QAChB,KAAK;AACH;AAAA,QACF,KAAK;AACH,cACE;AAAA,YACE;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,MAAM;AAEN,mBAAO;AACT;AACA;AACA;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,eAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAClC,gBACE;AAAA,cACE,OAAO,CAAC;AAAA,cACR;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF,MAAM;AAEN,qBAAO;AACT;AACA,gBAAI,aAAa,aAAc;AAAA,UACjC;AACA,cAAI,aAAa,aAAc;AAC/B;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,eAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAClC,iBAAK,IAAI,GAAG,IAAI,OAAO,CAAC,EAAE,SAAS,YAAY,KAAK;AAClD,kBACE;AAAA,gBACE,OAAO,CAAC,EAAE,CAAC;AAAA,gBACX;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACF,MAAM;AAEN,uBAAO;AACT;AAAA,YACF;AACA,gBAAI,aAAa,kBAAmB;AACpC,gBAAI,aAAa,UAAW;AAAA,UAC9B;AACA,cAAI,aAAa,UAAW;AAC5B;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAClC,4BAAgB;AAChB,iBAAK,IAAI,GAAG,IAAI,OAAO,CAAC,EAAE,QAAQ,KAAK;AACrC,mBAAK,IAAI,GAAG,IAAI,OAAO,CAAC,EAAE,CAAC,EAAE,SAAS,YAAY,KAAK;AACrD,oBACE;AAAA,kBACE,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,kBACd;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF,MAAM;AAEN,yBAAO;AACT;AAAA,cACF;AACA;AAAA,YACF;AACA;AAAA,UACF;AACA;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,IAAI,SAAS,WAAW,QAAQ;AAC1C,gBACE,UAAU,SAAS,WAAW,CAAC,GAAG,UAAU,gBAAgB,MAC5D;AAEA,qBAAO;AACX;AAAA,QACF;AACE,gBAAM,IAAI,MAAM,uBAAuB;AAAA,MAC3C;AAAA,IACF;AAAA,EACF;AACF;AAqDA,SAAS,YAAY,SAAS,UAAU,cAAc,kBAAkB;AACtE,MAAI,gBAAgB;AACpB;AAAA,IACE;AAAA,IACA,SACE,cACA,YACA,cACA,mBACA,eACA;AACA,UAAI,eAAe,KAAK,iBAAiB;AACvC,wBAAgB;AAAA;AAEhB,wBAAgB;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AACA,SAAO;AACT;AA6BA,SAAS,SAAS,SAAS,UAAU;AACnC,MAAI;AACJ,UAAQ,QAAQ,MAAM;AAAA,IACpB,KAAK;AACH,WAAK,IAAI,GAAG,IAAI,QAAQ,SAAS,QAAQ,KAAK;AAC5C,YAAI,SAAS,QAAQ,SAAS,CAAC,EAAE,YAAY,CAAC,MAAM,MAAO;AAAA,MAC7D;AACA;AAAA,IACF,KAAK;AACH,eAAS,QAAQ,YAAY,CAAC;AAC9B;AAAA,EACJ;AACF;AA+CA,SAAS,WAAW,SAAS,UAAU,cAAc;AACnD,MAAI,gBAAgB;AACpB,WAAS,SAAS,SAAU,mBAAmB,cAAc;AAC3D,QAAI,iBAAiB,KAAK,iBAAiB;AACzC,sBAAgB;AAAA;AAEhB,sBAAgB,SAAS,eAAe,mBAAmB,YAAY;AAAA,EAC3E,CAAC;AACD,SAAO;AACT;AA8BA,SAAS,YAAY,SAAS,UAAU;AACtC,MAAI,QAAQ,SAAS,WAAW;AAC9B,aAAS,SAAS,CAAC;AAAA,EACrB,WAAW,QAAQ,SAAS,qBAAqB;AAC/C,aAAS,IAAI,GAAG,IAAI,QAAQ,SAAS,QAAQ,KAAK;AAChD,UAAI,SAAS,QAAQ,SAAS,CAAC,GAAG,CAAC,MAAM,MAAO;AAAA,IAClD;AAAA,EACF;AACF;AA6CA,SAAS,cAAc,SAAS,UAAU,cAAc;AACtD,MAAI,gBAAgB;AACpB,cAAY,SAAS,SAAU,gBAAgB,cAAc;AAC3D,QAAI,iBAAiB,KAAK,iBAAiB;AACzC,sBAAgB;AAAA,QACb,iBAAgB,SAAS,eAAe,gBAAgB,YAAY;AAAA,EAC3E,CAAC;AACD,SAAO;AACT;AAiBA,SAAS,SAAS,SAAS;AACzB,MAAI,SAAS,CAAC;AACd,YAAU,SAAS,SAAU,OAAO;AAClC,WAAO,KAAK,KAAK;AAAA,EACnB,CAAC;AACD,SAAO;AACT;AAmCA,SAAS,SAAS,SAAS,UAAU;AACnC,MAAI,GACF,GACA,GACA,UACA,OACA,yBACA,sBACA,mBACA,aACA,WACA,eAAe,GACf,sBAAsB,QAAQ,SAAS,qBACvC,YAAY,QAAQ,SAAS,WAC7B,OAAO,sBAAsB,QAAQ,SAAS,SAAS;AAczD,OAAK,IAAI,GAAG,IAAI,MAAM,KAAK;AACzB,8BAA0B,sBACtB,QAAQ,SAAS,CAAC,EAAE,WACpB,YACE,QAAQ,WACR;AACN,wBAAoB,sBAChB,QAAQ,SAAS,CAAC,EAAE,aACpB,YACE,QAAQ,aACR,CAAC;AACP,kBAAc,sBACV,QAAQ,SAAS,CAAC,EAAE,OACpB,YACE,QAAQ,OACR;AACN,gBAAY,sBACR,QAAQ,SAAS,CAAC,EAAE,KACpB,YACE,QAAQ,KACR;AACN,2BAAuB,0BACnB,wBAAwB,SAAS,uBACjC;AACJ,YAAQ,uBACJ,wBAAwB,WAAW,SACnC;AAEJ,SAAK,IAAI,GAAG,IAAI,OAAO,KAAK;AAC1B,iBAAW,uBACP,wBAAwB,WAAW,CAAC,IACpC;AAGJ,UAAI,aAAa,MAAM;AACrB,YACE;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM;AAEN,iBAAO;AACT;AAAA,MACF;AACA,cAAQ,SAAS,MAAM;AAAA,QACrB,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,gBAAgB;AACnB,cACE;AAAA,YACE;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,MAAM;AAEN,mBAAO;AACT;AAAA,QACF;AAAA,QACA,KAAK,sBAAsB;AACzB,eAAK,IAAI,GAAG,IAAI,SAAS,WAAW,QAAQ,KAAK;AAC/C,gBACE;AAAA,cACE,SAAS,WAAW,CAAC;AAAA,cACrB;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF,MAAM;AAEN,qBAAO;AAAA,UACX;AACA;AAAA,QACF;AAAA,QACA;AACE,gBAAM,IAAI,MAAM,uBAAuB;AAAA,MAC3C;AAAA,IACF;AAEA;AAAA,EACF;AACF;AAmDA,SAAS,WAAW,SAAS,UAAU,cAAc;AACnD,MAAI,gBAAgB;AACpB;AAAA,IACE;AAAA,IACA,SACE,iBACA,cACA,mBACA,aACA,WACA;AACA,UAAI,iBAAiB,KAAK,iBAAiB;AACzC,wBAAgB;AAAA;AAEhB,wBAAgB;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,IACJ;AAAA,EACF;AACA,SAAO;AACT;AAgCA,SAAS,YAAY,SAAS,UAAU;AACtC,WAAS,SAAS,SAAU,UAAU,cAAc,YAAY,MAAM,IAAI;AAExE,QAAI,OAAO,aAAa,OAAO,OAAO,SAAS;AAC/C,YAAQ,MAAM;AAAA,MACZ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,YACE;AAAA,UACE,QAAQ,UAAU,YAAY,EAAE,MAAY,GAAO,CAAC;AAAA,UACpD;AAAA,UACA;AAAA,QACF,MAAM;AAEN,iBAAO;AACT;AAAA,IACJ;AAEA,QAAI;AAGJ,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,mBAAW;AACX;AAAA,MACF,KAAK;AACH,mBAAW;AACX;AAAA,MACF,KAAK;AACH,mBAAW;AACX;AAAA,IACJ;AAEA,aACM,oBAAoB,GACxB,oBAAoB,SAAS,YAAY,QACzC,qBACA;AACA,UAAI,aAAa,SAAS,YAAY,iBAAiB;AACvD,UAAI,OAAO;AAAA,QACT,MAAM;AAAA,QACN,aAAa;AAAA,MACf;AACA,UACE,SAAS,QAAQ,MAAM,UAAU,GAAG,cAAc,iBAAiB,MACnE;AAEA,eAAO;AAAA,IACX;AAAA,EACF,CAAC;AACH;AA+CA,SAAS,cAAc,SAAS,UAAU,cAAc;AACtD,MAAI,gBAAgB;AACpB;AAAA,IACE;AAAA,IACA,SAAU,gBAAgB,cAAc,mBAAmB;AACzD,UACE,iBAAiB,KACjB,sBAAsB,KACtB,iBAAiB;AAEjB,wBAAgB;AAAA;AAEhB,wBAAgB;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,IACJ;AAAA,EACF;AACA,SAAO;AACT;AAuCA,SAAS,YAAY,SAAS,UAAU;AACtC,cAAY,SAAS,SAAUA,UAAS,cAAc,mBAAmB;AACvE,QAAI,eAAe;AAGnB,QAAI,CAACA,SAAQ,SAAU;AAEvB,QAAI,OAAOA,SAAQ,SAAS;AAC5B,QAAI,SAAS,WAAW,SAAS,aAAc;AAG/C,QAAI;AACJ,QAAI,uBAAuB;AAC3B,QAAI,qBAAqB;AACzB,QAAI,gBAAgB;AACpB,QACE;AAAA,MACEA;AAAA,MACA,SACE,cACA,YACA,mBACA,qBACA,eACA;AAEA,YACE,mBAAmB,UACnB,eAAe,wBACf,sBAAsB,sBACtB,gBAAgB,eAChB;AACA,2BAAiB;AACjB,iCAAuB;AACvB,+BAAqB;AACrB,0BAAgB;AAChB,yBAAe;AACf;AAAA,QACF;AACA,YAAI,iBAAiB;AAAA,UACnB,CAAC,gBAAgB,YAAY;AAAA,UAC7BA,SAAQ;AAAA,QACV;AACA,YACE;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM;AAEN,iBAAO;AACT;AACA,yBAAiB;AAAA,MACnB;AAAA,IACF,MAAM;AAEN,aAAO;AAAA,EACX,CAAC;AACH;AAwDA,SAAS,cAAc,SAAS,UAAU,cAAc;AACtD,MAAI,gBAAgB;AACpB,MAAI,UAAU;AACd;AAAA,IACE;AAAA,IACA,SACE,gBACA,cACA,mBACA,eACA,cACA;AACA,UAAI,YAAY,SAAS,iBAAiB;AACxC,wBAAgB;AAAA;AAEhB,wBAAgB;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACF,gBAAU;AAAA,IACZ;AAAA,EACF;AACA,SAAO;AACT;AAkCA,SAAS,SAAS,SAAS,UAAU;AAEnC,MAAI,CAAC,QAAS,OAAM,IAAI,MAAM,qBAAqB;AAEnD,cAAY,SAAS,SAAUA,UAAS,cAAc,mBAAmB;AACvE,QAAIA,SAAQ,aAAa,KAAM;AAC/B,QAAI,OAAOA,SAAQ,SAAS;AAC5B,QAAI,SAASA,SAAQ,SAAS;AAC9B,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,YAAI,SAASA,UAAS,cAAc,mBAAmB,GAAG,CAAC,MAAM;AAC/D,iBAAO;AACT;AAAA,MACF,KAAK;AACH,iBACM,gBAAgB,GACpB,gBAAgB,OAAO,QACvB,iBACA;AACA,cACE;AAAA,YACE,WAAW,OAAO,aAAa,GAAGA,SAAQ,UAAU;AAAA,YACpD;AAAA,YACA;AAAA,YACA;AAAA,UACF,MAAM;AAEN,mBAAO;AAAA,QACX;AACA;AAAA,IACJ;AAAA,EACF,CAAC;AACH;AAiDA,SAAS,WAAW,SAAS,UAAU,cAAc;AACnD,MAAI,gBAAgB;AACpB;AAAA,IACE;AAAA,IACA,SAAU,aAAa,cAAc,mBAAmB,eAAe;AACrE,UAAI,iBAAiB,KAAK,iBAAiB;AACzC,wBAAgB;AAAA;AAEhB,wBAAgB;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,IACJ;AAAA,EACF;AACA,SAAO;AACT;AAoCA,SAAS,YAAY,SAAS,SAAS;AAErC,YAAU,WAAW,CAAC;AACtB,MAAI,CAAC,SAAS,OAAO,EAAG,OAAM,IAAI,MAAM,oBAAoB;AAC5D,MAAI,eAAe,QAAQ,gBAAgB;AAC3C,MAAI,oBAAoB,QAAQ,qBAAqB;AACrD,MAAI,gBAAgB,QAAQ,iBAAiB;AAC7C,MAAI,eAAe,QAAQ,gBAAgB;AAG3C,MAAI,aAAa,QAAQ;AACzB,MAAI;AAEJ,UAAQ,QAAQ,MAAM;AAAA,IACpB,KAAK;AACH,UAAI,eAAe;AACjB,uBAAe,QAAQ,SAAS,SAAS;AAC3C,mBAAa,cAAc,QAAQ,SAAS,YAAY,EAAE;AAC1D,iBAAW,QAAQ,SAAS,YAAY,EAAE;AAC1C;AAAA,IACF,KAAK;AACH,mBAAa,cAAc,QAAQ;AACnC,iBAAW,QAAQ;AACnB;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,iBAAW;AACX;AAAA,IACF;AACE,YAAM,IAAI,MAAM,oBAAoB;AAAA,EACxC;AAGA,MAAI,aAAa,KAAM,QAAO;AAC9B,MAAI,SAAS,SAAS;AACtB,UAAQ,SAAS,MAAM;AAAA,IACrB,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,UAAI,eAAe,EAAG,gBAAe,OAAO,SAAS,eAAe;AACpE,aAAO;AAAA,QACL,CAAC,OAAO,YAAY,GAAG,OAAO,eAAe,CAAC,CAAC;AAAA,QAC/C;AAAA,QACA;AAAA,MACF;AAAA,IACF,KAAK;AACH,UAAI,gBAAgB,EAAG,iBAAgB,OAAO,SAAS;AACvD,UAAI,eAAe;AACjB,uBAAe,OAAO,aAAa,EAAE,SAAS,eAAe;AAC/D,aAAO;AAAA,QACL;AAAA,UACE,OAAO,aAAa,EAAE,YAAY;AAAA,UAClC,OAAO,aAAa,EAAE,eAAe,CAAC;AAAA,QACxC;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,KAAK;AACH,UAAI,oBAAoB;AACtB,4BAAoB,OAAO,SAAS;AACtC,UAAI,eAAe;AACjB,uBAAe,OAAO,iBAAiB,EAAE,SAAS,eAAe;AACnE,aAAO;AAAA,QACL;AAAA,UACE,OAAO,iBAAiB,EAAE,YAAY;AAAA,UACtC,OAAO,iBAAiB,EAAE,eAAe,CAAC;AAAA,QAC5C;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,KAAK;AACH,UAAI,oBAAoB;AACtB,4BAAoB,OAAO,SAAS;AACtC,UAAI,gBAAgB;AAClB,wBAAgB,OAAO,iBAAiB,EAAE,SAAS;AACrD,UAAI,eAAe;AACjB,uBACE,OAAO,iBAAiB,EAAE,aAAa,EAAE,SAAS,eAAe;AACrE,aAAO;AAAA,QACL;AAAA,UACE,OAAO,iBAAiB,EAAE,aAAa,EAAE,YAAY;AAAA,UACrD,OAAO,iBAAiB,EAAE,aAAa,EAAE,eAAe,CAAC;AAAA,QAC3D;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,EACJ;AACA,QAAM,IAAI,MAAM,oBAAoB;AACtC;AAmCA,SAAS,UAAU,SAAS,SAAS;AAEnC,YAAU,WAAW,CAAC;AACtB,MAAI,CAAC,SAAS,OAAO,EAAG,OAAM,IAAI,MAAM,oBAAoB;AAC5D,MAAI,eAAe,QAAQ,gBAAgB;AAC3C,MAAI,oBAAoB,QAAQ,qBAAqB;AACrD,MAAI,gBAAgB,QAAQ,iBAAiB;AAC7C,MAAI,aAAa,QAAQ,cAAc;AAGvC,MAAI,aAAa,QAAQ;AACzB,MAAI;AAEJ,UAAQ,QAAQ,MAAM;AAAA,IACpB,KAAK;AACH,UAAI,eAAe;AACjB,uBAAe,QAAQ,SAAS,SAAS;AAC3C,mBAAa,cAAc,QAAQ,SAAS,YAAY,EAAE;AAC1D,iBAAW,QAAQ,SAAS,YAAY,EAAE;AAC1C;AAAA,IACF,KAAK;AACH,mBAAa,cAAc,QAAQ;AACnC,iBAAW,QAAQ;AACnB;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,iBAAW;AACX;AAAA,IACF;AACE,YAAM,IAAI,MAAM,oBAAoB;AAAA,EACxC;AAGA,MAAI,aAAa,KAAM,QAAO;AAC9B,MAAI,SAAS,SAAS;AACtB,UAAQ,SAAS,MAAM;AAAA,IACrB,KAAK;AACH,aAAO,MAAM,QAAQ,YAAY,OAAO;AAAA,IAC1C,KAAK;AACH,UAAI,oBAAoB;AACtB,4BAAoB,OAAO,SAAS;AACtC,aAAO,MAAM,OAAO,iBAAiB,GAAG,YAAY,OAAO;AAAA,IAC7D,KAAK;AACH,UAAI,aAAa,EAAG,cAAa,OAAO,SAAS;AACjD,aAAO,MAAM,OAAO,UAAU,GAAG,YAAY,OAAO;AAAA,IACtD,KAAK;AACH,UAAI,gBAAgB,EAAG,iBAAgB,OAAO,SAAS;AACvD,UAAI,aAAa;AACf,qBAAa,OAAO,aAAa,EAAE,SAAS;AAC9C,aAAO,MAAM,OAAO,aAAa,EAAE,UAAU,GAAG,YAAY,OAAO;AAAA,IACrE,KAAK;AACH,UAAI,oBAAoB;AACtB,4BAAoB,OAAO,SAAS;AACtC,UAAI,aAAa;AACf,qBAAa,OAAO,iBAAiB,EAAE,SAAS;AAClD,aAAO,MAAM,OAAO,iBAAiB,EAAE,UAAU,GAAG,YAAY,OAAO;AAAA,IACzE,KAAK;AACH,UAAI,oBAAoB;AACtB,4BAAoB,OAAO,SAAS;AACtC,UAAI,gBAAgB;AAClB,wBAAgB,OAAO,iBAAiB,EAAE,SAAS;AACrD,UAAI,aAAa;AACf,qBACE,OAAO,iBAAiB,EAAE,aAAa,EAAE,SAAS;AACtD,aAAO;AAAA,QACL,OAAO,iBAAiB,EAAE,aAAa,EAAE,UAAU;AAAA,QACnD;AAAA,QACA;AAAA,MACF;AAAA,EACJ;AACA,QAAM,IAAI,MAAM,oBAAoB;AACtC;", "names": ["feature"]}