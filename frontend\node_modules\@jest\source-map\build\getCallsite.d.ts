/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import callsites = require('callsites');
import type { SourceMapRegistry } from './types';
export default function getCallsite(level: number, sourceMaps?: SourceMapRegistry | null): callsites.CallSite;
