{"name": "@mapbox/mapbox-gl-supported", "version": "1.5.0", "description": "A library to determine if a browser supports Mapbox GL JS", "main": "index.js", "scripts": {"test": "eslint index.js", "build": "mkdir -p dist && uglifyjs index.js -c -m -o dist/mapbox-gl-supported.js"}, "repository": {"type": "git", "url": "git+https://github.com/mapbox/mapbox-gl-supported.git"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/mapbox/mapbox-gl-supported/issues"}, "homepage": "https://github.com/mapbox/mapbox-gl-supported#readme", "devDependencies": {"browserify": "^16.2.3", "eslint": "^2.8.0", "eslint-config-mourner": "^2.0.1", "uglify-js": "^2.4.24"}, "peerDependencies": {"mapbox-gl": ">=0.32.1 <2.0.0"}}