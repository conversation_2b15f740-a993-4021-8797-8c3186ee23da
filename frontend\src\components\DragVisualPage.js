/**
 * DragVisualPage - Interface drag & drop pour créer des visualisations
 * Permet de glisser-déposer des colonnes pour construire des graphiques
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { DndContext, DragOverlay, closestCenter } from '@dnd-kit/core';
import axios from 'axios';
import Plot from 'react-plotly.js';
import {
  BrAInBIHeader,
  DarkCard,
  SectionHeader,
  PrimaryButton,
  SecondaryButton,
  DarkSpinner,
  DarkBadge
} from './YellowMindUI';
import {
  ColumnsPanel,
  DropZonesPanel,
  FixedDropBar,
  AggregationSelector,
  ChartTypeSelector,
  DraggableColumn
} from './DragDropComponents';
import { FiltersSection } from './FilterComponents';
import {
  NarrationSettings,
  DataStorytellingPanel,
  NetworkDiagnostic,
  useDataStorytelling
} from './DataStorytellingComponents';

const API_BASE_URL = 'http://localhost:8000';

const DragVisualPage = () => {
  const navigate = useNavigate();
  
  // États pour les données
  const [tables, setTables] = useState([]);
  const [columns, setColumns] = useState({});
  const [chartData, setChartData] = useState(null);
  
  // États pour les sélections
  const [selectedTable, setSelectedTable] = useState('');
  const [xAxis, setXAxis] = useState(null);
  const [yAxis, setYAxis] = useState(null);
  const [legend, setLegend] = useState(null);
  const [values, setValues] = useState(null);
  const [aggFunction, setAggFunction] = useState('SUM');
  const [chartType, setChartType] = useState('bar');
  
  // États pour l'interface
  const [loading, setLoading] = useState(false);
  const [loadingTables, setLoadingTables] = useState(true);
  const [loadingColumns, setLoadingColumns] = useState(false);
  const [error, setError] = useState('');
  const [activeId, setActiveId] = useState(null);
  const [showDebugJson, setShowDebugJson] = useState(false);
  const [filters, setFilters] = useState([]);
  const [showNetworkDiagnostic, setShowNetworkDiagnostic] = useState(false);
  const [showFixedDropBar, setShowFixedDropBar] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [autoHideBar, setAutoHideBar] = useState(false);
  const [toast, setToast] = useState({ show: false, message: '', type: 'success' });

  // Hook pour la narration intelligente
  const {
    narrative,
    loading: narrativeLoading,
    error: narrativeError,
    debugInfo,
    generateNarrative,
    copyToClipboard,
    exportNarrative
  } = useDataStorytelling();

  // Charger les tables au démarrage
  useEffect(() => {
    loadTables();
  }, []);

  // Gestion du scroll pour auto-masquer la barre fixe
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      if (autoHideBar) {
        if (currentScrollY > lastScrollY && currentScrollY > 100) {
          // Scroll vers le bas - masquer la barre
          setShowFixedDropBar(false);
        } else if (currentScrollY < lastScrollY) {
          // Scroll vers le haut - montrer la barre
          setShowFixedDropBar(true);
        }
      }

      setLastScrollY(currentScrollY);
    };

    if (autoHideBar) {
      window.addEventListener('scroll', handleScroll, { passive: true });
      return () => window.removeEventListener('scroll', handleScroll);
    }
  }, [lastScrollY, autoHideBar]);

  // Raccourci clavier pour basculer la barre fixe (Ctrl/Cmd + B)
  useEffect(() => {
    const handleKeyDown = (e) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
        e.preventDefault();
        setShowFixedDropBar(!showFixedDropBar);
        if (!showFixedDropBar) setAutoHideBar(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [showFixedDropBar]);

  // Charger les colonnes quand une table est sélectionnée
  useEffect(() => {
    if (selectedTable) {
      loadColumns(selectedTable);
      // NE PAS reset les sélections pour permettre l'analyse multi-tables
      // Les utilisateurs peuvent analyser des colonnes de différentes tables
      setChartData(null); // Reset seulement le graphique
    }
  }, [selectedTable]);

  const loadTables = async () => {
    try {
      setLoadingTables(true);
      setError('');
      const response = await axios.get(`${API_BASE_URL}/schema/tables`);
      setTables(response.data.tables || []);
    } catch (err) {
      setError('Erreur lors du chargement des tables: ' + (err.response?.data?.detail || err.message));
    } finally {
      setLoadingTables(false);
    }
  };

  const loadColumns = async (tableName) => {
    try {
      setLoadingColumns(true);
      const response = await axios.get(`${API_BASE_URL}/tables/${tableName}/columns`);
      setColumns(response.data);
    } catch (err) {
      setError('Erreur lors du chargement des colonnes: ' + (err.response?.data?.detail || err.message));
    } finally {
      setLoadingColumns(false);
    }
  };

  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over) return;

    const columnId = active.id;
    const dropZoneId = over.id;

    // Trouver la colonne correspondante
    const allColumns = [
      ...(columns.all_columns || []),
      ...(columns.numeric_columns || []),
      ...(columns.categorical_columns || []),
      ...(columns.date_columns || [])
    ];

    const column = allColumns.find(col => `column-${col.name}` === columnId);
    if (!column) return;

    // Ajouter la table source à la colonne
    const columnWithTable = { ...column, table: selectedTable };

    // Assigner la colonne à la zone appropriée
    assignColumnToAxis(dropZoneId, columnWithTable);
  };

  // Fonction pour assigner une colonne à un axe (utilisée par drag & drop et menu contextuel)
  const assignColumnToAxis = (axisId, columnWithTable) => {
    switch (axisId) {
      case 'x-axis':
      case 'x-axis-fixed':
        setXAxis(columnWithTable);
        break;
      case 'y-axis':
      case 'y-axis-fixed':
        setYAxis(columnWithTable);
        break;
      case 'legend':
      case 'legend-fixed':
        setLegend(columnWithTable);
        break;
      case 'values':
      case 'values-fixed':
        setValues(columnWithTable);
        break;
      default:
        break;
    }
  };

  // Fonction pour le menu contextuel
  const handleAddToAxis = (axisId, columnWithTable) => {
    assignColumnToAxis(axisId, columnWithTable);

    // Afficher un toast de confirmation
    const axisNames = {
      'x-axis': 'Axe X',
      'y-axis': 'Axe Y',
      'legend': 'Légende',
      'values': 'Valeurs'
    };

    setToast({
      show: true,
      message: `${columnWithTable.name} ajouté à ${axisNames[axisId]}`,
      type: 'success'
    });

    // Masquer le toast après 3 secondes
    setTimeout(() => {
      setToast({ show: false, message: '', type: 'success' });
    }, 3000);
  };

  const generateVisualization = async () => {
    if (!selectedTable) {
      setError('Veuillez sélectionner une table');
      return;
    }

    if (!xAxis && !values) {
      setError('Veuillez glisser au moins une colonne dans Axe X ou Valeurs');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      const requestData = {
        table: selectedTable, // Table principale pour compatibilité
        x_axis: xAxis?.name || null,
        y_axis: yAxis?.name || null,
        legend: legend?.name || null,
        values: values?.name || null,
        agg_function: aggFunction,
        chart_type: chartType,
        // Nouvelles propriétés pour multi-tables
        x_axis_table: xAxis?.table || null,
        y_axis_table: yAxis?.table || null,
        legend_table: legend?.table || null,
        values_table: values?.table || null,
        // Filtres
        filters: filters.filter(f =>
          (f.filter_type === 'range' && (f.min_value || f.max_value)) ||
          (f.filter_type !== 'range' && f.values && f.values.length > 0)
        ).map(f => ({
          column_name: f.column_name,
          table_name: f.table_name,
          filter_type: f.filter_type,
          values: f.values || [],
          min_value: f.min_value || null,
          max_value: f.max_value || null
        }))
      };

      const response = await axios.post(`${API_BASE_URL}/drag-visual`, requestData);
      setChartData(response.data);
    } catch (err) {
      setError('Erreur lors de la génération: ' + (err.response?.data?.detail || err.message));
    } finally {
      setLoading(false);
    }
  };

  const clearAll = () => {
    setXAxis(null);
    setYAxis(null);
    setLegend(null);
    setValues(null);
    setChartData(null);
  };

  const renderChart = () => {
    if (!chartData) return null;

    const plotData = [];

    // Construire le titre avec les filtres actifs
    let title = chartData.title;
    if (activeFiltersCount > 0) {
      title += ` (${activeFiltersCount} filtre${activeFiltersCount > 1 ? 's' : ''} appliqué${activeFiltersCount > 1 ? 's' : ''})`;
    }

    const layout = {
      title: title,
      autosize: true,
      height: 500,
      margin: { l: 50, r: 50, t: 80, b: 50 },
      paper_bgcolor: 'rgba(0,0,0,0)',
      plot_bgcolor: 'rgba(0,0,0,0)',
      font: { color: '#F3F4F6' }
    };

    switch (chartData.type) {
      case 'bar':
        if (chartData.legend && Array.isArray(chartData.y[0])) {
          // Graphique avec légende
          chartData.legend.forEach((legendItem, index) => {
            plotData.push({
              x: chartData.x,
              y: chartData.y.map(row => row[index] || 0),
              type: 'bar',
              name: legendItem,
              marker: { 
                color: `hsl(${(index * 60) % 360}, 70%, 60%)`
              }
            });
          });
        } else {
          plotData.push({
            x: chartData.x,
            y: chartData.y,
            type: 'bar',
            marker: { color: '#a74eff' }
          });
        }
        layout.xaxis = { title: chartData.xlabel };
        layout.yaxis = { title: chartData.ylabel };
        break;

      case 'line':
        plotData.push({
          x: chartData.x,
          y: chartData.y,
          type: 'scatter',
          mode: 'lines+markers',
          line: { color: '#a74eff', width: 3 },
          marker: { color: '#3db5ff', size: 8 }
        });
        layout.xaxis = { title: chartData.xlabel };
        layout.yaxis = { title: chartData.ylabel };
        break;

      case 'pie':
        plotData.push({
          labels: chartData.x,
          values: chartData.y,
          type: 'pie',
          marker: { 
            colors: ['#a74eff', '#3db5ff', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#43e97b']
          },
          textinfo: 'label+percent',
          textposition: 'outside'
        });
        break;

      case 'scatter':
        plotData.push({
          x: chartData.x,
          y: chartData.y,
          type: 'scatter',
          mode: 'markers',
          marker: { 
            color: '#a74eff',
            size: 10,
            line: { color: '#3db5ff', width: 1 }
          }
        });
        layout.xaxis = { title: chartData.xlabel };
        layout.yaxis = { title: chartData.ylabel };
        break;

      default:
        return <p className="text-red-400">Type de graphique non supporté: {chartData.type}</p>;
    }

    return (
      <Plot
        data={plotData}
        layout={layout}
        config={{ responsive: true, displayModeBar: true }}
        style={{ width: '100%' }}
      />
    );
  };

  const canGenerate = selectedTable && (xAxis || values);

  // Détecter si c'est une analyse multi-tables
  const isMultiTable = () => {
    const tables = new Set();
    [xAxis, yAxis, legend, values].forEach(col => {
      if (col?.table) tables.add(col.table);
    });
    return tables.size > 1;
  };

  // Compter les filtres actifs
  const activeFiltersCount = filters.filter(f =>
    (f.filter_type === 'range' && (f.min_value || f.max_value)) ||
    (f.filter_type !== 'range' && f.values && f.values.length > 0)
  ).length;

  return (
    <DndContext
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="min-h-screen bg-gray-950 text-gray-100">
        <BrAInBIHeader hasData={true} />
        
        <div className={`max-w-7xl mx-auto px-6 py-8 ${showFixedDropBar ? 'pb-32' : 'pb-8'}`}>
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <div>
                <div className="flex items-center space-x-3 mb-2">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center">
                    <span className="text-xl">🎯</span>
                  </div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                    Créateur Drag & Drop
                  </h1>
                </div>
                <p className="text-gray-400">
                  Glissez-déposez vos colonnes pour créer des visualisations interactives
                </p>
                <p className="text-sm text-purple-400 mt-1">
                  💡 Jointures automatiques : analysez des colonnes de différentes tables
                </p>
                <p className="text-sm text-blue-400 mt-1">
                  📖 Narration IA : générez des histoires intelligentes à partir de vos données
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  💡 Astuce : Utilisez Ctrl+B pour basculer la barre fixe
                </p>
              </div>
              <div className="flex space-x-3">
                <SecondaryButton
                  onClick={() => {
                    setShowFixedDropBar(!showFixedDropBar);
                    if (!showFixedDropBar) setAutoHideBar(false);
                  }}
                  icon={showFixedDropBar ? "👁️" : "👁️‍🗨️"}
                  className={showFixedDropBar ? "!border-purple-500 !text-purple-400" : ""}
                >
                  Barre fixe
                </SecondaryButton>
                <SecondaryButton
                  onClick={() => setAutoHideBar(!autoHideBar)}
                  icon={autoHideBar ? "🔄" : "📌"}
                  className={autoHideBar ? "!border-blue-500 !text-blue-400" : ""}
                  disabled={!showFixedDropBar}
                >
                  Auto-masquer
                </SecondaryButton>
                <SecondaryButton onClick={() => navigate('/visual-builder')} icon="📊">
                  Visual Builder
                </SecondaryButton>
                <SecondaryButton onClick={() => navigate('/tables')} icon="📋">
                  Tables
                </SecondaryButton>
                <SecondaryButton onClick={() => navigate('/')} icon="🏠">
                  Accueil
                </SecondaryButton>
              </div>
            </div>
          </div>

          {/* Sélection de table */}
          <div className="mb-8">
            <DarkCard>
              <SectionHeader
                title="1. Sélectionner la table"
                subtitle="Choisissez la source de données"
                icon="🗂️"
              />
              
              {loadingTables ? (
                <div className="flex items-center justify-center py-4">
                  <DarkSpinner />
                  <span className="ml-2 text-gray-400">Chargement...</span>
                </div>
              ) : (
                <select
                  value={selectedTable}
                  onChange={(e) => setSelectedTable(e.target.value)}
                  className="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-gray-100 focus:border-purple-500 focus:outline-none"
                >
                  <option value="">-- Sélectionner une table --</option>
                  {tables.map((table, index) => (
                    <option key={index} value={table.TABLE_NAME}>
                      {table.TABLE_NAME} ({table.COLUMN_COUNT} colonnes)
                    </option>
                  ))}
                </select>
              )}
            </DarkCard>
          </div>

          {selectedTable && (
            <div className="grid grid-cols-1 xl:grid-cols-5 gap-8">
              {/* Panneau des colonnes - Plus large */}
              <div className="xl:col-span-2">
                <DarkCard className="h-fit">
                  {loadingColumns ? (
                    <div className="flex items-center justify-center py-8">
                      <DarkSpinner />
                      <span className="ml-2 text-gray-400">Chargement des colonnes...</span>
                    </div>
                  ) : (
                    <ColumnsPanel
                      columns={columns.all_columns || []}
                      title="Colonnes disponibles"
                      onAddToAxis={handleAddToAxis}
                      selectedTable={selectedTable}
                    />
                  )}
                </DarkCard>
              </div>

              {/* Zone principale - Plus compacte */}
              <div className="xl:col-span-3 space-y-6">
                {/* Section des filtres */}
                <FiltersSection
                  filters={filters}
                  onFiltersChange={setFilters}
                />

                {/* Zones de drop */}
                <DarkCard>
                  <div className="flex items-center justify-between mb-6">
                    <SectionHeader
                      title="2. Configurer les axes"
                      subtitle="Glissez les colonnes dans les zones appropriées"
                      icon="🎯"
                    />
                    <div className="flex space-x-3">
                      <SecondaryButton
                        onClick={() => generateNarrative({ focus: null, time_period: '12_months', include_recommendations: true })}
                        icon="📖"
                        disabled={narrativeLoading}
                        className="!text-blue-400 !border-blue-600 hover:!bg-blue-600/20"
                      >
                        {narrativeLoading ? 'Génération...' : 'Narration rapide'}
                      </SecondaryButton>
                      <SecondaryButton onClick={clearAll} icon="🗑️" className="!text-red-400 !border-red-600 hover:!bg-red-600/20">
                        Tout effacer
                      </SecondaryButton>
                    </div>
                  </div>
                  
                  <DropZonesPanel
                    xAxis={xAxis}
                    yAxis={yAxis}
                    legend={legend}
                    values={values}
                    onClearX={() => setXAxis(null)}
                    onClearY={() => setYAxis(null)}
                    onClearLegend={() => setLegend(null)}
                    onClearValues={() => setValues(null)}
                  />
                </DarkCard>

                {/* Configuration */}
                <DarkCard>
                  <SectionHeader
                    title="3. Configuration"
                    subtitle="Choisissez l'agrégation et le type de graphique"
                    icon="⚙️"
                  />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <AggregationSelector
                      value={aggFunction}
                      onChange={setAggFunction}
                      disabled={!values && aggFunction !== 'COUNT'}
                    />
                    
                    <ChartTypeSelector
                      value={chartType}
                      onChange={setChartType}
                    />
                  </div>
                  
                  <div className="mt-6 flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-3">
                        <PrimaryButton
                          onClick={generateVisualization}
                          disabled={!canGenerate || loading}
                          loading={loading}
                          icon={loading ? null : "🚀"}
                          className="!py-3 !px-6"
                        >
                          {loading ? 'Génération...' : 'Générer la visualisation'}
                        </PrimaryButton>

                        {isMultiTable() && (
                          <div className="flex items-center space-x-2 px-3 py-2 bg-blue-600/20 border border-blue-500 rounded-lg">
                            <span className="text-blue-400">🔗</span>
                            <span className="text-sm text-blue-300 font-medium">Analyse multi-tables</span>
                          </div>
                        )}

                        {activeFiltersCount > 0 && (
                          <div className="flex items-center space-x-2 px-3 py-2 bg-green-600/20 border border-green-500 rounded-lg">
                            <span className="text-green-400">🧩</span>
                            <span className="text-sm text-green-300 font-medium">
                              {activeFiltersCount} filtre{activeFiltersCount > 1 ? 's' : ''} actif{activeFiltersCount > 1 ? 's' : ''}
                            </span>
                          </div>
                        )}
                      </div>
                      
                      {chartData && (
                        <SecondaryButton
                          onClick={() => setShowDebugJson(!showDebugJson)}
                          icon="🔍"
                          className="!py-3 !px-4"
                        >
                          {showDebugJson ? 'Masquer JSON' : 'Voir JSON'}
                        </SecondaryButton>
                      )}
                    </div>
                    
                    {!canGenerate && selectedTable && (
                      <p className="text-gray-500 text-sm">
                        Glissez au moins une colonne dans Axe X ou Valeurs
                      </p>
                    )}
                  </div>
                </DarkCard>

                {/* Affichage du graphique */}
                <DarkCard>
                  {!chartData ? (
                    <div className="flex flex-col items-center justify-center py-16 text-center">
                      <div className="w-24 h-24 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl mb-6 flex items-center justify-center">
                        <span className="text-4xl">🎯</span>
                      </div>
                      <h3 className="text-xl font-semibold text-gray-100 mb-4">
                        Prêt à créer votre visualisation
                      </h3>
                      <p className="text-gray-400 max-w-md">
                        Glissez vos colonnes dans les zones appropriées et cliquez sur "Générer" pour créer votre graphique
                      </p>
                    </div>
                  ) : (
                    <div>
                      <SectionHeader
                        title="Visualisation générée"
                        subtitle={`${chartData.type.toUpperCase()} - ${chartData.data_points} points de données`}
                        icon="📊"
                      />
                      
                      <div className="mt-6">
                        {renderChart()}
                      </div>
                      
                      {chartData.success && (
                        <div className="mt-4 p-3 bg-green-900/20 border border-green-700 rounded-lg">
                          <p className="text-green-400 text-sm">
                            ✅ {chartData.message}
                          </p>
                        </div>
                      )}
                      
                      {/* Debug JSON */}
                      {showDebugJson && (
                        <div className="mt-4 p-4 bg-gray-900 border border-gray-700 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-300 mb-2">JSON de réponse :</h4>
                          <pre className="text-xs text-gray-400 overflow-x-auto">
                            {JSON.stringify(chartData, null, 2)}
                          </pre>
                        </div>
                      )}
                    </div>
                  )}
                  
                  {error && (
                    <div className="mt-4 p-4 bg-red-900/20 border border-red-700 rounded-lg">
                      <p className="text-red-400">
                        ❌ {error}
                      </p>
                    </div>
                  )}
                </DarkCard>

                {/* Section Narration intelligente */}
                <div className="mt-8 space-y-6">
                  <NarrationSettings
                    onGenerate={generateNarrative}
                    loading={narrativeLoading}
                  />

                  {narrativeError && (
                    <DarkCard className="border-red-500">
                      <div className="space-y-4">
                        <div className="flex items-center space-x-3 text-red-400">
                          <span className="text-xl">⚠️</span>
                          <div>
                            <p className="font-medium">Erreur de génération de narration</p>
                            <p className="text-sm text-red-300">
                              La génération automatique de l'histoire des données a échoué
                            </p>
                          </div>
                        </div>

                        <div className="bg-red-900/20 p-4 rounded border border-red-700">
                          <pre className="text-sm text-red-300 whitespace-pre-wrap font-mono leading-relaxed">
                            {narrativeError}
                          </pre>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                          <SecondaryButton
                            onClick={() => generateNarrative({ focus: null, time_period: '12_months', include_recommendations: true })}
                            icon="🔄"
                            className="!py-2 !px-3 !text-sm"
                            disabled={narrativeLoading}
                          >
                            Réessayer
                          </SecondaryButton>
                          <SecondaryButton
                            onClick={() => generateNarrative({ focus: null, time_period: '6_months', include_recommendations: false })}
                            icon="⚡"
                            className="!py-2 !px-3 !text-sm"
                            disabled={narrativeLoading}
                          >
                            Mode simplifié
                          </SecondaryButton>
                          <SecondaryButton
                            onClick={() => window.open('http://localhost:8000/health', '_blank')}
                            icon="🔍"
                            className="!py-2 !px-3 !text-sm !text-blue-400 !border-blue-600"
                          >
                            Test backend
                          </SecondaryButton>
                          <SecondaryButton
                            onClick={() => window.open('http://localhost:8000/narrate/test', '_blank')}
                            icon="🧪"
                            className="!py-2 !px-3 !text-sm !text-green-400 !border-green-600"
                          >
                            Test narration
                          </SecondaryButton>
                          <SecondaryButton
                            onClick={() => setShowNetworkDiagnostic(true)}
                            icon="🔧"
                            className="!py-2 !px-3 !text-sm !text-purple-400 !border-purple-600"
                          >
                            Diagnostic réseau
                          </SecondaryButton>
                        </div>

                        {debugInfo?.error_type === 'connection' && (
                          <div className="bg-yellow-900/20 p-3 rounded border border-yellow-700">
                            <div className="flex items-start space-x-2">
                              <span className="text-yellow-400 mt-1">💡</span>
                              <div className="text-sm text-yellow-300">
                                <p className="font-medium mb-2">Problème de connexion détecté</p>
                                <ul className="space-y-1 text-xs">
                                  <li>• Vérifiez que le backend est démarré sur le port 8000</li>
                                  <li>• Testez l'URL : <code className="bg-yellow-800/30 px-1 rounded">http://localhost:8000</code></li>
                                  <li>• Consultez les logs du serveur backend</li>
                                </ul>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </DarkCard>
                  )}

                  {/* Debug info pour diagnostiquer les problèmes */}
                  {debugInfo && (debugInfo.status === 'error' || process.env.NODE_ENV === 'development') && (
                    <DarkCard className="border-yellow-500">
                      <div className="space-y-3">
                        <div className="flex items-center space-x-3 text-yellow-400">
                          <span className="text-lg">🔧</span>
                          <h4 className="font-medium">Informations de debug</h4>
                        </div>

                        <div className="bg-yellow-900/20 p-3 rounded border border-yellow-700">
                          <pre className="text-xs text-yellow-300 whitespace-pre-wrap font-mono">
                            {JSON.stringify(debugInfo, null, 2)}
                          </pre>
                        </div>
                      </div>
                    </DarkCard>
                  )}

                  {/* Diagnostic réseau */}
                  {showNetworkDiagnostic && (
                    <NetworkDiagnostic
                      onClose={() => setShowNetworkDiagnostic(false)}
                    />
                  )}

                  <DataStorytellingPanel
                    narrative={narrative}
                    dataSummary={narrative?.data_summary || {}}
                    onCopy={copyToClipboard}
                    onExport={exportNarrative}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Toast de notification */}
      {toast.show && (
        <div className="fixed top-4 right-4 z-50 bg-green-600 text-white px-4 py-2 rounded-lg shadow-lg
                        animate-pulse backdrop-blur-sm border border-green-500">
          <div className="flex items-center space-x-2">
            <span>✅</span>
            <span className="text-sm font-medium">{toast.message}</span>
          </div>
        </div>
      )}

      {/* Barre fixe avec zones de drop */}
      <FixedDropBar
        xAxis={xAxis}
        yAxis={yAxis}
        legend={legend}
        values={values}
        onClearX={() => setXAxis(null)}
        onClearY={() => setYAxis(null)}
        onClearLegend={() => setLegend(null)}
        onClearValues={() => setValues(null)}
        isVisible={showFixedDropBar}
        position="bottom"
      />

      {/* Drag Overlay */}
      <DragOverlay>
        {activeId ? (
          <div className="transform rotate-3 scale-105">
            <DraggableColumn
              id={activeId}
              column={{ name: 'Glissement...', type: 'unknown' }}
            />
          </div>
        ) : null}
      </DragOverlay>
    </DndContext>
  );
};

export default DragVisualPage;
