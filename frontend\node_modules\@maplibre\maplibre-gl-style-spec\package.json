{"name": "@maplibre/maplibre-gl-style-spec", "description": "a specification for maplibre styles", "version": "20.4.0", "author": "MapLibre", "keywords": ["mapbox", "mapbox-gl", "mapbox-gl-js", "maplibre", "maplibre-gl", "maplibre-gl-js"], "license": "ISC", "homepage": "https://maplibre.org/maplibre-style-spec/", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "type": "module", "scripts": {"build": "rollup --configPlugin @rollup/plugin-typescript -c rollup.config.ts && cp ./src/reference/v8.json ./dist/latest.json", "generate-style-spec": "node --no-warnings --loader ts-node/esm build/generate-style-spec.ts", "generate-typings": "dts-bundle-generator -o ./dist/index.d.ts ./src/index.ts", "generate-docs": "node ${WATCH+--watch} --no-warnings --loader ts-node/esm build/generate-docs.ts", "mkdocs": "docker run --rm -it -p 8000:8000 -v ${PWD}:/docs squidfunk/mkdocs-material", "mkdocs-build": "npm run generate-docs && docker run --rm -v ${PWD}:/docs squidfunk/mkdocs-material build --strict", "test-build": "jest --selectProjects=build", "test-integration": "jest --selectProjects=integration", "test-unit": "jest --selectProjects=unit", "jest": "jest", "jest-ci": "jest --reporters=github-actions --reporters=summary", "compile": "tsc", "lint": "eslint --cache --ext .ts,.tsx --ignore-path .gitignore .", "typecheck": "tsc --noEmit", "prepare": "npm run generate-style-spec"}, "repository": {"type": "git", "url": "**************:maplibre/maplibre-gl-style-spec.git"}, "bin": {"gl-style-migrate": "dist/gl-style-migrate.mjs", "gl-style-validate": "dist/gl-style-validate.mjs", "gl-style-format": "dist/gl-style-format.mjs"}, "files": ["dist", "src", "bin"], "dependencies": {"@mapbox/jsonlint-lines-primitives": "~2.0.2", "@mapbox/unitbezier": "^0.0.1", "json-stringify-pretty-compact": "^4.0.0", "minimist": "^1.2.8", "quickselect": "^2.0.0", "rw": "^1.3.3", "tinyqueue": "^3.0.0"}, "sideEffects": false, "devDependencies": {"@rollup/plugin-commonjs": "^28.0.0", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-replace": "^6.0.1", "@rollup/plugin-strip": "^3.0.4", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.0", "@types/eslint": "^8.56.6", "@types/geojson": "^7946.0.14", "@types/jest": "^29.5.13", "@types/node": "^22.7.5", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.18.0", "dts-bundle-generator": "^9.5.1", "eslint": "^8.57.0", "eslint-config-mourner": "^3.0.0", "eslint-plugin-html": "^8.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.8.3", "eslint-plugin-jsdoc": "^50.3.2", "eslint-plugin-react": "^7.37.1", "glob": "^11.0.0", "jest": "^29.7.0", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^29.7.0", "rollup": "^4.24.0", "rollup-plugin-preserve-shebang": "^1.0.1", "semver": "^7.6.3", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tslib": "^2.7.0", "typescript": "^5.6.3"}}