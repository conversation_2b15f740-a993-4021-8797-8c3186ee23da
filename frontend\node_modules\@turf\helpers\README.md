# @turf/helpers

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## helpers

## Units

Linear measurement units.

⚠️ Warning. Be aware of the implications of using radian or degree units to
measure distance. The distance represented by a degree of longitude *varies*
depending on latitude.

See [https://www.thoughtco.com/degree-of-latitude-and-longitude-distance-4070616][1]
for an illustration of this behaviour.

Type: (`"meters"` | `"metres"` | `"millimeters"` | `"millimetres"` | `"centimeters"` | `"centimetres"` | `"kilometers"` | `"kilometres"` | `"miles"` | `"nauticalmiles"` | `"inches"` | `"yards"` | `"feet"` | `"radians"` | `"degrees"`)

## AreaUnits

Area measurement units.

Type: (Exclude<[Units][2], (`"radians"` | `"degrees"`)> | `"acres"` | `"hectares"`)

## Grid

Grid types.

Type: (`"point"` | `"square"` | `"hex"` | `"triangle"`)

## Corners

Shorthand corner identifiers.

Type: (`"sw"` | `"se"` | `"nw"` | `"ne"` | `"center"` | `"centroid"`)

## Lines

Geometries made up of lines i.e. lines and polygons.

Type: ([LineString][3] | [MultiLineString][4] | [Polygon][5] | [MultiPolygon][6])

## AllGeoJSON

Convenience type for all possible GeoJSON.

Type: ([Feature][7] | [FeatureCollection][8] | [Geometry][9] | [GeometryCollection][10])

## earthRadius

The Earth radius in kilometers. Used by Turf modules that model the Earth as a sphere. The [mean radius][11] was selected because it is [recommended ][12] by the Haversine formula (used by turf/distance) to reduce error.

Type: [number][13]

## factors

Unit of measurement factors based on earthRadius.

Keys are the name of the unit, values are the number of that unit in a single radian

Type: Record<[Units][2], [number][13]>

## areaFactors

Area of measurement factors based on 1 square meter.

Type: Record<[AreaUnits][14], [number][13]>

## feature

Wraps a GeoJSON [Geometry][9] in a GeoJSON [Feature][7].

### Parameters

*   `geom` **(G | null)**&#x20;
*   `properties` **[GeoJsonProperties][7]** an Object of key-value pairs to add as properties (optional, default `{}`)
*   `options` **[Object][15]** Optional Parameters (optional, default `{}`)

    *   `options.bbox` **[BBox][16]?** Bounding Box Array \[west, south, east, north] associated with the Feature
    *   `options.id` **Id?** Identifier associated with the Feature
*   `geometry` **[GeometryObject][9]** input geometry

### Examples

```javascript
var geometry = {
  "type": "Point",
  "coordinates": [110, 50]
};

var feature = turf.feature(geometry);

//=feature
```

Returns **[Feature][7]<[GeometryObject][9], [GeoJsonProperties][7]>** a GeoJSON Feature

## geometry

Creates a GeoJSON [Geometry][9] from a Geometry string type & coordinates.
For GeometryCollection type use `helpers.geometryCollection`

### Parameters

*   `type` **(`"Point"` | `"LineString"` | `"Polygon"` | `"MultiPoint"` | `"MultiLineString"` | `"MultiPolygon"`)** Geometry Type
*   `coordinates` **[Array][17]\<any>** Coordinates
*   `_options` **Record<[string][18], never>**  (optional, default `{}`)
*   `options` **[Object][15]** Optional Parameters (optional, default `{}`)

### Examples

```javascript
var type = "Point";
var coordinates = [110, 50];
var geometry = turf.geometry(type, coordinates);
// => geometry
```

Returns **[Geometry][9]** a GeoJSON Geometry

## point

Creates a [Point][19] [Feature][7] from a Position.

### Parameters

*   `coordinates` **[Position][20]** longitude, latitude position (each in decimal degrees)
*   `properties` **[GeoJsonProperties][7]** an Object of key-value pairs to add as properties (optional, default `{}`)
*   `options` **[Object][15]** Optional Parameters (optional, default `{}`)

    *   `options.bbox` **[BBox][16]?** Bounding Box Array \[west, south, east, north] associated with the Feature
    *   `options.id` **Id?** Identifier associated with the Feature

### Examples

```javascript
var point = turf.point([-75.343, 39.984]);

//=point
```

Returns **[Feature][7]<[Point][19], [GeoJsonProperties][7]>** a Point feature

## points

Creates a [Point][19] [FeatureCollection][8] from an Array of Point coordinates.

### Parameters

*   `coordinates` **[Array][17]<[Position][20]>** an array of Points
*   `properties` **[GeoJsonProperties][7]** Translate these properties to each Feature (optional, default `{}`)
*   `options` **[Object][15]** Optional Parameters (optional, default `{}`)

    *   `options.bbox` **[BBox][16]?** Bounding Box Array \[west, south, east, north]
        associated with the FeatureCollection
    *   `options.id` **Id?** Identifier associated with the FeatureCollection

### Examples

```javascript
var points = turf.points([
  [-75, 39],
  [-80, 45],
  [-78, 50]
]);

//=points
```

Returns **[FeatureCollection][8]<[Point][19]>** Point Feature

## polygon

Creates a [Polygon][5] [Feature][7] from an Array of LinearRings.

### Parameters

*   `coordinates` **[Array][17]<[Array][17]<[Position][20]>>**&#x20;
*   `properties` **[GeoJsonProperties][7]** an Object of key-value pairs to add as properties (optional, default `{}`)
*   `options` **[Object][15]** Optional Parameters (optional, default `{}`)

    *   `options.bbox` **[BBox][16]?** Bounding Box Array \[west, south, east, north] associated with the Feature
    *   `options.id` **Id?** Identifier associated with the Feature

### Examples

```javascript
var polygon = turf.polygon([[[-5, 52], [-4, 56], [-2, 51], [-7, 54], [-5, 52]]], { name: 'poly1' });

//=polygon
```

Returns **[Feature][7]<[Polygon][5], [GeoJsonProperties][7]>** Polygon Feature

## polygons

Creates a [Polygon][5] [FeatureCollection][8] from an Array of Polygon coordinates.

### Parameters

*   `coordinates` **[Array][17]<[Array][17]<[Array][17]<[Position][20]>>>**&#x20;
*   `properties` **[GeoJsonProperties][7]** an Object of key-value pairs to add as properties (optional, default `{}`)
*   `options` **[Object][15]** Optional Parameters (optional, default `{}`)

    *   `options.bbox` **[BBox][16]?** Bounding Box Array \[west, south, east, north] associated with the Feature
    *   `options.id` **Id?** Identifier associated with the FeatureCollection

### Examples

```javascript
var polygons = turf.polygons([
  [[[-5, 52], [-4, 56], [-2, 51], [-7, 54], [-5, 52]]],
  [[[-15, 42], [-14, 46], [-12, 41], [-17, 44], [-15, 42]]],
]);

//=polygons
```

Returns **[FeatureCollection][8]<[Polygon][5], [GeoJsonProperties][7]>** Polygon FeatureCollection

## lineString

Creates a [LineString][3] [Feature][7] from an Array of Positions.

### Parameters

*   `coordinates` **[Array][17]<[Position][20]>** an array of Positions
*   `properties` **[GeoJsonProperties][7]** an Object of key-value pairs to add as properties (optional, default `{}`)
*   `options` **[Object][15]** Optional Parameters (optional, default `{}`)

    *   `options.bbox` **[BBox][16]?** Bounding Box Array \[west, south, east, north] associated with the Feature
    *   `options.id` **Id?** Identifier associated with the Feature

### Examples

```javascript
var linestring1 = turf.lineString([[-24, 63], [-23, 60], [-25, 65], [-20, 69]], {name: 'line 1'});
var linestring2 = turf.lineString([[-14, 43], [-13, 40], [-15, 45], [-10, 49]], {name: 'line 2'});

//=linestring1
//=linestring2
```

Returns **[Feature][7]<[LineString][3], [GeoJsonProperties][7]>** LineString Feature

## lineStrings

Creates a [LineString][3] [FeatureCollection][8] from an Array of LineString coordinates.

### Parameters

*   `coordinates` **[Array][17]<[Array][17]<[Position][20]>>**&#x20;
*   `properties` **[GeoJsonProperties][7]** an Object of key-value pairs to add as properties (optional, default `{}`)
*   `options` **[Object][15]** Optional Parameters (optional, default `{}`)

    *   `options.bbox` **[BBox][16]?** Bounding Box Array \[west, south, east, north]
        associated with the FeatureCollection
    *   `options.id` **Id?** Identifier associated with the FeatureCollection

### Examples

```javascript
var linestrings = turf.lineStrings([
  [[-24, 63], [-23, 60], [-25, 65], [-20, 69]],
  [[-14, 43], [-13, 40], [-15, 45], [-10, 49]]
]);

//=linestrings
```

Returns **[FeatureCollection][8]<[LineString][3], [GeoJsonProperties][7]>** LineString FeatureCollection

## featureCollection

Takes one or more [Features][7] and creates a [FeatureCollection][8].

### Parameters

*   `features` **[Array][17]<[Feature][7]<[GeometryObject][9], [GeoJsonProperties][7]>>** input features
*   `options` **[Object][15]** Optional Parameters (optional, default `{}`)

    *   `options.bbox` **[BBox][16]?** Bounding Box Array \[west, south, east, north] associated with the Feature
    *   `options.id` **Id?** Identifier associated with the Feature

### Examples

```javascript
var locationA = turf.point([-75.343, 39.984], {name: 'Location A'});
var locationB = turf.point([-75.833, 39.284], {name: 'Location B'});
var locationC = turf.point([-75.534, 39.123], {name: 'Location C'});

var collection = turf.featureCollection([
  locationA,
  locationB,
  locationC
]);

//=collection
```

Returns **[FeatureCollection][8]<[GeometryObject][9], [GeoJsonProperties][7]>** FeatureCollection of Features

## multiLineString

Creates a [Feature][7]<[MultiLineString][4]> based on a
coordinate array. Properties can be added optionally.

### Parameters

*   `coordinates` **[Array][17]<[Array][17]<[Position][20]>>**&#x20;
*   `properties` **[GeoJsonProperties][7]** an Object of key-value pairs to add as properties (optional, default `{}`)
*   `options` **[Object][15]** Optional Parameters (optional, default `{}`)

    *   `options.bbox` **[BBox][16]?** Bounding Box Array \[west, south, east, north] associated with the Feature
    *   `options.id` **Id?** Identifier associated with the Feature

### Examples

```javascript
var multiLine = turf.multiLineString([[[0,0],[10,10]]]);

//=multiLine
```

*   Throws **[Error][21]** if no coordinates are passed

Returns **[Feature][7]<[MultiLineString][4], [GeoJsonProperties][7]>** a MultiLineString feature

## multiPoint

Creates a [Feature][7]<[MultiPoint][22]> based on a
coordinate array. Properties can be added optionally.

### Parameters

*   `coordinates` **[Array][17]<[Position][20]>** an array of Positions
*   `properties` **[GeoJsonProperties][7]** an Object of key-value pairs to add as properties (optional, default `{}`)
*   `options` **[Object][15]** Optional Parameters (optional, default `{}`)

    *   `options.bbox` **[BBox][16]?** Bounding Box Array \[west, south, east, north] associated with the Feature
    *   `options.id` **Id?** Identifier associated with the Feature

### Examples

```javascript
var multiPt = turf.multiPoint([[0,0],[10,10]]);

//=multiPt
```

*   Throws **[Error][21]** if no coordinates are passed

Returns **[Feature][7]<[MultiPoint][22], [GeoJsonProperties][7]>** a MultiPoint feature

## multiPolygon

Creates a [Feature][7]<[MultiPolygon][6]> based on a
coordinate array. Properties can be added optionally.

### Parameters

*   `coordinates` **[Array][17]<[Array][17]<[Array][17]<[Position][20]>>>**&#x20;
*   `properties` **[GeoJsonProperties][7]** an Object of key-value pairs to add as properties (optional, default `{}`)
*   `options` **[Object][15]** Optional Parameters (optional, default `{}`)

    *   `options.bbox` **[BBox][16]?** Bounding Box Array \[west, south, east, north] associated with the Feature
    *   `options.id` **Id?** Identifier associated with the Feature

### Examples

```javascript
var multiPoly = turf.multiPolygon([[[[0,0],[0,10],[10,10],[10,0],[0,0]]]]);

//=multiPoly
```

*   Throws **[Error][21]** if no coordinates are passed

Returns **[Feature][7]<[MultiPolygon][6], [GeoJsonProperties][7]>** a multipolygon feature

## geometryCollection

Creates a Feature<GeometryCollection> based on a
coordinate array. Properties can be added optionally.

### Parameters

*   `geometries` **[Array][17]<([Point][19] | [LineString][3] | [Polygon][5] | [MultiPoint][22] | [MultiLineString][4] | [MultiPolygon][6])>** an array of GeoJSON Geometries
*   `properties` **[GeoJsonProperties][7]** an Object of key-value pairs to add as properties (optional, default `{}`)
*   `options` **[Object][15]** Optional Parameters (optional, default `{}`)

    *   `options.bbox` **[BBox][16]?** Bounding Box Array \[west, south, east, north] associated with the Feature
    *   `options.id` **Id?** Identifier associated with the Feature

### Examples

```javascript
var pt = turf.geometry("Point", [100, 0]);
var line = turf.geometry("LineString", [[101, 0], [102, 1]]);
var collection = turf.geometryCollection([pt, line]);

// => collection
```

Returns **[Feature][7]<[GeometryCollection][10], [GeoJsonProperties][7]>** a GeoJSON GeometryCollection Feature

## round

Round number to precision

### Parameters

*   `num` **[number][13]** Number
*   `precision` **[number][13]** Precision (optional, default `0`)

### Examples

```javascript
turf.round(120.4321)
//=120

turf.round(120.4321, 2)
//=120.43
```

Returns **[number][13]** rounded number

## radiansToLength

Convert a distance measurement (assuming a spherical Earth) from radians to a more friendly unit.
Valid units: miles, nauticalmiles, inches, yards, meters, metres, kilometers, centimeters, feet

### Parameters

*   `radians` **[number][13]** in radians across the sphere
*   `units` **[Units][2]** can be degrees, radians, miles, inches, yards, metres,
    meters, kilometres, kilometers. (optional, default `"kilometers"`)

Returns **[number][13]** distance

## lengthToRadians

Convert a distance measurement (assuming a spherical Earth) from a real-world unit into radians
Valid units: miles, nauticalmiles, inches, yards, meters, metres, kilometers, centimeters, feet

### Parameters

*   `distance` **[number][13]** in real units
*   `units` **[Units][2]** can be degrees, radians, miles, inches, yards, metres,
    meters, kilometres, kilometers. (optional, default `"kilometers"`)

Returns **[number][13]** radians

## lengthToDegrees

Convert a distance measurement (assuming a spherical Earth) from a real-world unit into degrees
Valid units: miles, nauticalmiles, inches, yards, meters, metres, centimeters, kilometres, feet

### Parameters

*   `distance` **[number][13]** in real units
*   `units` **[Units][2]** can be degrees, radians, miles, inches, yards, metres,
    meters, kilometres, kilometers. (optional, default `"kilometers"`)

Returns **[number][13]** degrees

## bearingToAzimuth

Converts any bearing angle from the north line direction (positive clockwise)
and returns an angle between 0-360 degrees (positive clockwise), 0 being the north line

### Parameters

*   `bearing` **[number][13]** angle, between -180 and +180 degrees

Returns **[number][13]** angle between 0 and 360 degrees

## azimuthToBearing

Converts any azimuth angle from the north line direction (positive clockwise)
and returns an angle between -180 and +180 degrees (positive clockwise), 0 being the north line

### Parameters

*   `angle` **[number][13]** between 0 and 360 degrees

Returns **[number][13]** bearing between -180 and +180 degrees

## radiansToDegrees

Converts an angle in radians to degrees

### Parameters

*   `radians` **[number][13]** angle in radians

Returns **[number][13]** degrees between 0 and 360 degrees

## degreesToRadians

Converts an angle in degrees to radians

### Parameters

*   `degrees` **[number][13]** angle between 0 and 360 degrees

Returns **[number][13]** angle in radians

## convertLength

Converts a length from one unit to another.

### Parameters

*   `length` **[number][13]** Length to be converted
*   `originalUnit` **[Units][2]** Input length unit (optional, default `"kilometers"`)
*   `finalUnit` **[Units][2]** Returned length unit (optional, default `"kilometers"`)

Returns **[number][13]** The converted length

## convertArea

Converts an area from one unit to another.

### Parameters

*   `area` **[number][13]** Area to be converted
*   `originalUnit` **[AreaUnits][14]** Input area unit (optional, default `"meters"`)
*   `finalUnit` **[AreaUnits][14]** Returned area unit (optional, default `"kilometers"`)

Returns **[number][13]** The converted length

## isNumber

isNumber

### Parameters

*   `num` **any** Number to validate

### Examples

```javascript
turf.isNumber(123)
//=true
turf.isNumber('foo')
//=false
```

Returns **[boolean][23]** true/false

## isObject

isObject

### Parameters

*   `input` **any** variable to validate

### Examples

```javascript
turf.isObject({elevation: 10})
//=true
turf.isObject('foo')
//=false
```

Returns **[boolean][23]** true/false, including false for Arrays and Functions

[1]: https://www.thoughtco.com/degree-of-latitude-and-longitude-distance-4070616

[2]: #units

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[4]: https://tools.ietf.org/html/rfc7946#section-3.1.5

[5]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[6]: https://tools.ietf.org/html/rfc7946#section-3.1.7

[7]: https://tools.ietf.org/html/rfc7946#section-3.2

[8]: https://tools.ietf.org/html/rfc7946#section-3.3

[9]: https://tools.ietf.org/html/rfc7946#section-3.1

[10]: https://tools.ietf.org/html/rfc7946#section-3.1.8

[11]: https://en.wikipedia.org/wiki/Earth_radius#Arithmetic_mean_radius

[12]: https://rosettacode.org/wiki/Haversine_formula#:~:text=This%20value%20is%20recommended

[13]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[14]: #areaunits

[15]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[16]: https://tools.ietf.org/html/rfc7946#section-5

[17]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array

[18]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[19]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[20]: https://developer.mozilla.org/docs/Web/API/Position

[21]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Error

[22]: https://tools.ietf.org/html/rfc7946#section-3.1.3

[23]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/helpers
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
