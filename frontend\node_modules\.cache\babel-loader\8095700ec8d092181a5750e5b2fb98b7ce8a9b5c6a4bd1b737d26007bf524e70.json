{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIZekri\\\\frontend\\\\src\\\\components\\\\DragDropComponents.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\n/**\n * DragDropComponents - Composants pour l'interface drag & drop\n * Utilise @dnd-kit pour créer des colonnes draggables et des zones de drop\n */\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useDraggable, useDroppable } from '@dnd-kit/core';\nimport { DarkBadge } from './YellowMindUI';\n\n// Composant de menu contextuel pour les colonnes\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const ColumnContextMenu = ({\n  isOpen,\n  onClose,\n  position,\n  column,\n  onAddToAxis,\n  selectedTable,\n  currentAssignments = {}\n}) => {\n  _s();\n  const menuRef = useRef(null);\n  const [isProcessing, setIsProcessing] = useState(false);\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (menuRef.current && !menuRef.current.contains(event.target)) {\n        onClose();\n      }\n    };\n    const handleEscape = event => {\n      if (event.key === 'Escape') {\n        onClose();\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n      document.addEventListener('keydown', handleEscape);\n      return () => {\n        document.removeEventListener('mousedown', handleClickOutside);\n        document.removeEventListener('keydown', handleEscape);\n      };\n    }\n  }, [isOpen, onClose]);\n  if (!isOpen) return null;\n  const menuItems = [{\n    id: 'x-axis',\n    label: 'Ajouter à Axe X',\n    icon: '📊',\n    color: 'text-blue-400'\n  }, {\n    id: 'y-axis',\n    label: 'Ajouter à Axe Y',\n    icon: '📈',\n    color: 'text-green-400'\n  }, {\n    id: 'legend',\n    label: 'Ajouter à Légende',\n    icon: '🏷️',\n    color: 'text-yellow-400'\n  }, {\n    id: 'values',\n    label: 'Ajouter aux Valeurs',\n    icon: '💎',\n    color: 'text-purple-400'\n  }];\n  const handleItemClick = async item => {\n    if (isProcessing) return;\n    setIsProcessing(true);\n    try {\n      const columnWithTable = {\n        ...column,\n        table: selectedTable\n      };\n      await onAddToAxis(item.id, columnWithTable);\n      onClose();\n    } catch (error) {\n      console.error('Erreur lors de l\\'ajout de la colonne:', error);\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: menuRef,\n    className: \"fixed z-50 bg-gray-800 border border-gray-600 rounded-lg shadow-xl backdrop-blur-sm animate-in fade-in-0 zoom-in-95 duration-200\",\n    style: {\n      left: Math.min(position.x, window.innerWidth - 220),\n      top: Math.min(position.y, window.innerHeight - 200),\n      minWidth: '200px'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-3 py-2 border-b border-gray-600 mb-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: getColumnIcon(column.type)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-200\",\n              children: column.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-400 capitalize\",\n              children: column.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), menuItems.map(item => {\n        var _currentAssignments$i;\n        const isAssigned = ((_currentAssignments$i = currentAssignments[item.id]) === null || _currentAssignments$i === void 0 ? void 0 : _currentAssignments$i.name) === column.name;\n        return /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleItemClick(item),\n          disabled: isProcessing,\n          className: `\n                w-full flex items-center justify-between px-3 py-2 rounded-md text-left\n                transition-all duration-200 group\n                ${isAssigned ? 'bg-purple-600/20 border border-purple-500/50' : 'hover:bg-gray-700'}\n                ${isProcessing ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}\n              `,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `text-sm ${isAssigned ? 'text-purple-300' : `${item.color} group-hover:text-gray-100`}`,\n              children: item.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 15\n          }, this), isAssigned && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-purple-400\",\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 17\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 13\n        }, this);\n      }), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 pt-2 border-t border-gray-600\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-500 px-3 py-1\",\n          children: \"Clic droit ou \\u26A1 pour acc\\xE8s rapide\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour une colonne draggable\n_s(ColumnContextMenu, \"YPZRw7kMNee+aNsCOPmwsOkCzdk=\");\n_c = ColumnContextMenu;\nexport const DraggableColumn = ({\n  column,\n  id,\n  onAddToAxis,\n  selectedTable,\n  currentAssignments\n}) => {\n  _s2();\n  const [contextMenu, setContextMenu] = useState({\n    isOpen: false,\n    position: {\n      x: 0,\n      y: 0\n    }\n  });\n  const {\n    attributes,\n    listeners,\n    setNodeRef,\n    transform,\n    isDragging\n  } = useDraggable({\n    id: id,\n    data: {\n      column: column,\n      type: 'column'\n    }\n  });\n  const style = transform ? {\n    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,\n    opacity: isDragging ? 0.7 : 1,\n    zIndex: isDragging ? 1000 : 1\n  } : undefined;\n  const handleContextMenu = e => {\n    e.preventDefault();\n    setContextMenu({\n      isOpen: true,\n      position: {\n        x: e.clientX,\n        y: e.clientY\n      }\n    });\n  };\n  const handleQuickAdd = e => {\n    e.stopPropagation();\n    setContextMenu({\n      isOpen: true,\n      position: {\n        x: e.currentTarget.getBoundingClientRect().right + 10,\n        y: e.currentTarget.getBoundingClientRect().top\n      }\n    });\n  };\n  const getColumnIcon = type => {\n    const dataType = type.toLowerCase();\n    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n      return '🔢';\n    }\n    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n      return '📝';\n    }\n    if (dataType.includes('date') || dataType.includes('time')) {\n      return '📅';\n    }\n    if (dataType.includes('bit') || dataType.includes('boolean')) {\n      return '☑️';\n    }\n    return '📊';\n  };\n  const getColumnColor = type => {\n    const dataType = type.toLowerCase();\n    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n      return 'bg-blue-600/20 border-blue-500 text-blue-300';\n    }\n    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n      return 'bg-green-600/20 border-green-500 text-green-300';\n    }\n    if (dataType.includes('date') || dataType.includes('time')) {\n      return 'bg-purple-600/20 border-purple-500 text-purple-300';\n    }\n    return 'bg-gray-600/20 border-gray-500 text-gray-300';\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: setNodeRef,\n      style: style,\n      ...listeners,\n      ...attributes,\n      onContextMenu: handleContextMenu,\n      className: `\n          p-3 rounded-lg border-2 border-dashed cursor-grab active:cursor-grabbing\n          transition-all duration-300 ease-in-out transform hover:scale-105 hover:shadow-lg\n          ${getColumnColor(column.type)}\n          ${isDragging ? 'shadow-2xl column-drag-start rotate-3 scale-110' : 'hover:shadow-md hover:shadow-purple-500/10'}\n          group relative\n        `,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 flex-1 min-w-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: `text-lg transition-transform duration-200 ${isDragging ? 'scale-110' : 'group-hover:scale-105'}`,\n            children: getColumnIcon(column.type)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: `font-medium truncate transition-colors duration-200 ${isDragging ? 'text-purple-200' : 'group-hover:text-gray-100'}`,\n              children: column.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-xs transition-all duration-200 ${isDragging ? 'opacity-90 text-purple-300' : 'opacity-75 group-hover:opacity-90'}`,\n              children: column.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), onAddToAxis && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleQuickAdd,\n          className: \"opacity-0 group-hover:opacity-100 transition-all duration-200 p-1 rounded hover:bg-gray-700 text-gray-400 hover:text-purple-400 transform hover:scale-110 active:scale-95\",\n          title: \"Ajouter rapidement\",\n          children: \"\\u26A1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ColumnContextMenu, {\n      isOpen: contextMenu.isOpen,\n      onClose: () => setContextMenu({\n        ...contextMenu,\n        isOpen: false\n      }),\n      position: contextMenu.position,\n      column: column,\n      onAddToAxis: onAddToAxis,\n      selectedTable: selectedTable,\n      currentAssignments: currentAssignments\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// Composant pour une zone de drop\n_s2(DraggableColumn, \"Ug8s2XjgY4HyuI7lKr8hN/wtf3s=\", false, function () {\n  return [useDraggable];\n});\n_c2 = DraggableColumn;\nexport const DropZone = ({\n  id,\n  title,\n  subtitle,\n  icon,\n  children,\n  acceptedColumn,\n  onClear\n}) => {\n  _s3();\n  const {\n    isOver,\n    setNodeRef\n  } = useDroppable({\n    id: id,\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: setNodeRef,\n    className: `\n        p-6 rounded-lg border-2 border-dashed min-h-[140px] transition-all duration-200\n        ${isOver ? 'border-purple-400 bg-purple-600/10 scale-105' : 'border-gray-600 bg-gray-800/30'}\n        ${acceptedColumn ? 'border-purple-500 bg-purple-600/20' : ''}\n      `,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-lg\",\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium text-gray-200\",\n            children: title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), subtitle && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-400\",\n            children: subtitle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 26\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this), acceptedColumn && onClear && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClear,\n        className: \"text-gray-400 hover:text-red-400 transition-colors\",\n        title: \"Supprimer\",\n        children: \"\\u2715\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-[60px] flex items-center justify-center\",\n      children: acceptedColumn ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3 p-3 bg-purple-600/30 rounded-lg border border-purple-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xl\",\n            children: acceptedColumn.type.toLowerCase().includes('int') || acceptedColumn.type.toLowerCase().includes('decimal') || acceptedColumn.type.toLowerCase().includes('float') ? '🔢' : acceptedColumn.type.toLowerCase().includes('date') ? '📅' : '📝'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-medium text-purple-200 truncate\",\n              children: acceptedColumn.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-purple-300\",\n              children: acceptedColumn.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 17\n            }, this), acceptedColumn.table && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-purple-400 mt-1\",\n              children: [\"\\uD83D\\uDCCB \", acceptedColumn.table]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 text-sm\",\n          children: isOver ? 'Relâchez ici' : 'Glissez une colonne ici'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this), children]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 279,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour afficher les colonnes disponibles\n_s3(DropZone, \"fT702R7NW3L8KUJObOwGrnMsXMQ=\", false, function () {\n  return [useDroppable];\n});\n_c3 = DropZone;\nexport const ColumnsPanel = ({\n  columns,\n  title,\n  onAddToAxis,\n  selectedTable,\n  currentAssignments\n}) => {\n  if (!columns || columns.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-16 h-16 bg-gray-700 rounded-2xl mx-auto mb-4 flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-2xl\",\n          children: \"\\uD83D\\uDCCB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-400\",\n        children: \"Aucune colonne disponible\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-200\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DarkBadge, {\n        variant: \"info\",\n        children: columns.length\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3 max-h-[500px] overflow-y-auto\",\n      children: columns.map((column, index) => /*#__PURE__*/_jsxDEV(DraggableColumn, {\n        id: `column-${column.name}`,\n        column: column,\n        onAddToAxis: onAddToAxis,\n        selectedTable: selectedTable,\n        currentAssignments: currentAssignments\n      }, `${column.name}-${index}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 358,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour les zones de drop organisées\n_c4 = ColumnsPanel;\nexport const DropZonesPanel = ({\n  xAxis,\n  yAxis,\n  legend,\n  values,\n  onClearX,\n  onClearY,\n  onClearLegend,\n  onClearValues\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n    children: [/*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"x-axis\",\n      title: \"Axe X\",\n      subtitle: \"Cat\\xE9gories ou groupes\",\n      icon: \"\\uD83D\\uDCCA\",\n      acceptedColumn: xAxis,\n      onClear: onClearX\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 393,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"y-axis\",\n      title: \"Axe Y\",\n      subtitle: \"Valeurs num\\xE9riques (optionnel)\",\n      icon: \"\\uD83D\\uDCC8\",\n      acceptedColumn: yAxis,\n      onClear: onClearY\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 402,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"legend\",\n      title: \"L\\xE9gende\",\n      subtitle: \"Sous-cat\\xE9gories (optionnel)\",\n      icon: \"\\uD83C\\uDFF7\\uFE0F\",\n      acceptedColumn: legend,\n      onClear: onClearLegend\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"values\",\n      title: \"Valeurs\",\n      subtitle: \"Donn\\xE9es \\xE0 agr\\xE9ger\",\n      icon: \"\\uD83D\\uDC8E\",\n      acceptedColumn: values,\n      onClear: onClearValues\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 420,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 392,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour une zone de drop compacte dans la barre fixe\n_c5 = DropZonesPanel;\nexport const CompactDropZone = ({\n  id,\n  title,\n  icon,\n  acceptedColumn,\n  onClear,\n  isOver\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n        flex items-center justify-between p-3 rounded-lg border-2 border-dashed min-h-[60px]\n        transition-all duration-300 ease-in-out transform\n        ${isOver ? 'border-purple-400 bg-purple-600/20 scale-105 drop-zone-glow drop-zone-hover' : 'border-gray-600 bg-gray-800/50 hover:border-gray-500 hover:bg-gray-800/70'}\n        ${acceptedColumn ? 'border-purple-500 bg-purple-600/30 shadow-lg shadow-purple-500/20' : ''}\n        backdrop-blur-sm hover:backdrop-blur-md\n      `,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2 flex-1 min-w-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: `text-lg transition-transform duration-200 ${isOver ? 'scale-110' : ''}`,\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-w-0 flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: `font-medium text-sm truncate transition-colors duration-200 ${isOver ? 'text-purple-200' : 'text-gray-200'}`,\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 11\n        }, this), acceptedColumn ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1 mt-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-purple-300 truncate font-medium\",\n            children: acceptedColumn.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 15\n          }, this), acceptedColumn.table && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-400\",\n            children: [\"(\", acceptedColumn.table, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-xs transition-colors duration-200 ${isOver ? 'text-purple-300' : 'text-gray-400'}`,\n          children: isOver ? 'Relâchez ici' : 'Glissez ici'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 447,\n      columnNumber: 7\n    }, this), acceptedColumn && onClear && /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onClear,\n      className: \"text-gray-400 hover:text-red-400 transition-all duration-200 ml-2 p-1 hover:bg-red-600/20 rounded hover:scale-110 active:scale-95\",\n      title: \"Supprimer\",\n      children: \"\\u2715\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 478,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 435,\n    columnNumber: 5\n  }, this);\n};\n\n// Barre fixe avec les zones de drop\n_c6 = CompactDropZone;\nexport const FixedDropBar = ({\n  xAxis,\n  yAxis,\n  legend,\n  values,\n  onClearX,\n  onClearY,\n  onClearLegend,\n  onClearValues,\n  isVisible = true,\n  position = 'bottom' // 'top' ou 'bottom'\n}) => {\n  _s4();\n  const {\n    isOver: isOverX,\n    setNodeRef: setNodeRefX\n  } = useDroppable({\n    id: 'x-axis-fixed',\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  const {\n    isOver: isOverY,\n    setNodeRef: setNodeRefY\n  } = useDroppable({\n    id: 'y-axis-fixed',\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  const {\n    isOver: isOverLegend,\n    setNodeRef: setNodeRefLegend\n  } = useDroppable({\n    id: 'legend-fixed',\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  const {\n    isOver: isOverValues,\n    setNodeRef: setNodeRefValues\n  } = useDroppable({\n    id: 'values-fixed',\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  if (!isVisible) return null;\n  const positionClasses = position === 'top' ? 'top-0 border-b' : 'bottom-0 border-t';\n  const hasAnyColumn = xAxis || yAxis || legend || values;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n      fixed left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-md ${positionClasses} border-gray-700\n      transition-all duration-300 ease-in-out shadow-2xl\n      ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'}\n    `,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 py-3 sm:py-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg animate-pulse\",\n            children: \"\\uD83C\\uDFAF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-semibold text-gray-200\",\n              children: \"Zones de Drop\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this), hasAnyColumn && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-purple-400\",\n              children: [[xAxis, yAxis, legend, values].filter(Boolean).length, \" colonne(s) assign\\xE9e(s)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden sm:block text-xs text-gray-400\",\n          children: \"Glissez vos colonnes ici pour cr\\xE9er votre visualisation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 539,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          ref: setNodeRefX,\n          children: /*#__PURE__*/_jsxDEV(CompactDropZone, {\n            id: \"x-axis-fixed\",\n            title: \"Axe X\",\n            icon: \"\\uD83D\\uDCCA\",\n            acceptedColumn: xAxis,\n            onClear: onClearX,\n            isOver: isOverX\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: setNodeRefY,\n          children: /*#__PURE__*/_jsxDEV(CompactDropZone, {\n            id: \"y-axis-fixed\",\n            title: \"Axe Y\",\n            icon: \"\\uD83D\\uDCC8\",\n            acceptedColumn: yAxis,\n            onClear: onClearY,\n            isOver: isOverY\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 568,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: setNodeRefLegend,\n          children: /*#__PURE__*/_jsxDEV(CompactDropZone, {\n            id: \"legend-fixed\",\n            title: \"L\\xE9gende\",\n            icon: \"\\uD83C\\uDFF7\\uFE0F\",\n            acceptedColumn: legend,\n            onClear: onClearLegend,\n            isOver: isOverLegend\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: setNodeRefValues,\n          children: /*#__PURE__*/_jsxDEV(CompactDropZone, {\n            id: \"values-fixed\",\n            title: \"Valeurs\",\n            icon: \"\\uD83D\\uDC8E\",\n            acceptedColumn: values,\n            onClear: onClearValues,\n            isOver: isOverValues\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 556,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sm:hidden mt-2 text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-500\",\n          children: \"Glissez ou utilisez le menu \\u26A1 des colonnes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 604,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 603,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 538,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 533,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour le sélecteur de fonction d'agrégation\n_s4(FixedDropBar, \"iDE4Z2x6jYrF9tSCiODbcpzg8qI=\", false, function () {\n  return [useDroppable, useDroppable, useDroppable, useDroppable];\n});\n_c7 = FixedDropBar;\nexport const AggregationSelector = ({\n  value,\n  onChange,\n  disabled\n}) => {\n  const aggregations = [{\n    value: 'SUM',\n    label: 'Somme',\n    icon: '➕',\n    description: 'Addition de toutes les valeurs'\n  }, {\n    value: 'AVG',\n    label: 'Moyenne',\n    icon: '📊',\n    description: 'Valeur moyenne'\n  }, {\n    value: 'COUNT',\n    label: 'Nombre',\n    icon: '🔢',\n    description: 'Nombre d\\'occurrences'\n  }, {\n    value: 'MIN',\n    label: 'Minimum',\n    icon: '⬇️',\n    description: 'Valeur minimale'\n  }, {\n    value: 'MAX',\n    label: 'Maximum',\n    icon: '⬆️',\n    description: 'Valeur maximale'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"block text-sm font-medium text-gray-300 mb-2\",\n      children: \"Fonction d'agr\\xE9gation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 625,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n      value: value,\n      onChange: e => onChange(e.target.value),\n      disabled: disabled,\n      className: \"w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-gray-100 focus:border-purple-500 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed\",\n      children: aggregations.map(agg => /*#__PURE__*/_jsxDEV(\"option\", {\n        value: agg.value,\n        children: [agg.icon, \" \", agg.label, \" - \", agg.description]\n      }, agg.value, true, {\n        fileName: _jsxFileName,\n        lineNumber: 635,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 628,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 624,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour le sélecteur de type de graphique\n_c8 = AggregationSelector;\nexport const ChartTypeSelector = ({\n  value,\n  onChange\n}) => {\n  const chartTypes = [{\n    value: 'bar',\n    label: 'Barres',\n    icon: '📊',\n    description: 'Graphique en barres'\n  }, {\n    value: 'line',\n    label: 'Ligne',\n    icon: '📈',\n    description: 'Graphique linéaire'\n  }, {\n    value: 'pie',\n    label: 'Circulaire',\n    icon: '🥧',\n    description: 'Graphique circulaire'\n  }, {\n    value: 'scatter',\n    label: 'Nuage',\n    icon: '⚫',\n    description: 'Nuage de points'\n  }, {\n    value: 'stacked_bar',\n    label: 'Barres empilées',\n    icon: '📚',\n    description: 'Barres empilées'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"block text-sm font-medium text-gray-300 mb-2\",\n      children: \"Type de graphique\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 656,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 md:grid-cols-3 gap-2\",\n      children: chartTypes.map(type => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onChange(type.value),\n        className: `p-3 rounded-lg border transition-colors text-center ${value === type.value ? 'border-purple-500 bg-purple-600/20 text-purple-300' : 'border-gray-700 bg-gray-800/50 text-gray-300 hover:border-gray-600'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg mb-1\",\n          children: type.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs font-medium\",\n          children: type.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 671,\n          columnNumber: 13\n        }, this)]\n      }, type.value, true, {\n        fileName: _jsxFileName,\n        lineNumber: 661,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 659,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 655,\n    columnNumber: 5\n  }, this);\n};\n_c9 = ChartTypeSelector;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"ColumnContextMenu\");\n$RefreshReg$(_c2, \"DraggableColumn\");\n$RefreshReg$(_c3, \"DropZone\");\n$RefreshReg$(_c4, \"ColumnsPanel\");\n$RefreshReg$(_c5, \"DropZonesPanel\");\n$RefreshReg$(_c6, \"CompactDropZone\");\n$RefreshReg$(_c7, \"FixedDropBar\");\n$RefreshReg$(_c8, \"AggregationSelector\");\n$RefreshReg$(_c9, \"ChartTypeSelector\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useDraggable", "useDroppable", "DarkBadge", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ColumnContextMenu", "isOpen", "onClose", "position", "column", "onAddToAxis", "selectedTable", "currentAssignments", "_s", "menuRef", "isProcessing", "setIsProcessing", "handleClickOutside", "event", "current", "contains", "target", "handleEscape", "key", "document", "addEventListener", "removeEventListener", "menuItems", "id", "label", "icon", "color", "handleItemClick", "item", "columnWithTable", "table", "error", "console", "ref", "className", "style", "left", "Math", "min", "x", "window", "innerWidth", "top", "y", "innerHeight", "min<PERSON><PERSON><PERSON>", "children", "getColumnIcon", "type", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "map", "_currentAssignments$i", "isAssigned", "onClick", "disabled", "_c", "DraggableColumn", "_s2", "contextMenu", "setContextMenu", "attributes", "listeners", "setNodeRef", "transform", "isDragging", "data", "opacity", "zIndex", "undefined", "handleContextMenu", "e", "preventDefault", "clientX", "clientY", "handleQuickAdd", "stopPropagation", "currentTarget", "getBoundingClientRect", "right", "dataType", "toLowerCase", "includes", "getColumnColor", "onContextMenu", "title", "_c2", "DropZone", "subtitle", "acceptedColumn", "onClear", "_s3", "isOver", "accepts", "_c3", "ColumnsPanel", "columns", "length", "variant", "index", "_c4", "DropZonesPanel", "xAxis", "yAxis", "legend", "values", "onClearX", "onClearY", "onClearLegend", "onClearValues", "_c5", "CompactDropZone", "_c6", "FixedDropBar", "isVisible", "_s4", "isOverX", "setNodeRefX", "isOverY", "setNodeRefY", "isOverLegend", "setNodeRefLegend", "isOverValues", "setNodeRefValues", "positionClasses", "hasAnyColumn", "filter", "Boolean", "_c7", "AggregationSelector", "value", "onChange", "aggregations", "description", "agg", "_c8", "ChartTypeSelector", "chartTypes", "_c9", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/AIZekri/frontend/src/components/DragDropComponents.js"], "sourcesContent": ["/**\n * DragDropComponents - Composants pour l'interface drag & drop\n * Utilise @dnd-kit pour créer des colonnes draggables et des zones de drop\n */\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useDraggable, useDroppable } from '@dnd-kit/core';\nimport { DarkBadge } from './YellowMindUI';\n\n// Composant de menu contextuel pour les colonnes\nexport const ColumnContextMenu = ({\n  isOpen,\n  onClose,\n  position,\n  column,\n  onAddToAxis,\n  selectedTable,\n  currentAssignments = {}\n}) => {\n  const menuRef = useRef(null);\n  const [isProcessing, setIsProcessing] = useState(false);\n\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (menuRef.current && !menuRef.current.contains(event.target)) {\n        onClose();\n      }\n    };\n\n    const handleEscape = (event) => {\n      if (event.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n      document.addEventListener('keydown', handleEscape);\n      return () => {\n        document.removeEventListener('mousedown', handleClickOutside);\n        document.removeEventListener('keydown', handleEscape);\n      };\n    }\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  const menuItems = [\n    { id: 'x-axis', label: 'Ajouter à Axe X', icon: '📊', color: 'text-blue-400' },\n    { id: 'y-axis', label: 'Ajouter à Axe Y', icon: '📈', color: 'text-green-400' },\n    { id: 'legend', label: 'Ajouter à Légende', icon: '🏷️', color: 'text-yellow-400' },\n    { id: 'values', label: 'Ajouter aux Valeurs', icon: '💎', color: 'text-purple-400' }\n  ];\n\n  const handleItemClick = async (item) => {\n    if (isProcessing) return;\n\n    setIsProcessing(true);\n    try {\n      const columnWithTable = { ...column, table: selectedTable };\n      await onAddToAxis(item.id, columnWithTable);\n      onClose();\n    } catch (error) {\n      console.error('Erreur lors de l\\'ajout de la colonne:', error);\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  return (\n    <div\n      ref={menuRef}\n      className=\"fixed z-50 bg-gray-800 border border-gray-600 rounded-lg shadow-xl backdrop-blur-sm\n                 animate-in fade-in-0 zoom-in-95 duration-200\"\n      style={{\n        left: Math.min(position.x, window.innerWidth - 220),\n        top: Math.min(position.y, window.innerHeight - 200),\n        minWidth: '200px'\n      }}\n    >\n      <div className=\"p-2\">\n        <div className=\"px-3 py-2 border-b border-gray-600 mb-2\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-lg\">{getColumnIcon(column.type)}</span>\n            <div>\n              <p className=\"text-sm font-medium text-gray-200\">{column.name}</p>\n              <p className=\"text-xs text-gray-400 capitalize\">{column.type}</p>\n            </div>\n          </div>\n        </div>\n\n        {menuItems.map((item) => {\n          const isAssigned = currentAssignments[item.id]?.name === column.name;\n          return (\n            <button\n              key={item.id}\n              onClick={() => handleItemClick(item)}\n              disabled={isProcessing}\n              className={`\n                w-full flex items-center justify-between px-3 py-2 rounded-md text-left\n                transition-all duration-200 group\n                ${isAssigned\n                  ? 'bg-purple-600/20 border border-purple-500/50'\n                  : 'hover:bg-gray-700'\n                }\n                ${isProcessing ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}\n              `}\n            >\n              <div className=\"flex items-center space-x-3\">\n                <span className=\"text-lg\">{item.icon}</span>\n                <span className={`text-sm ${\n                  isAssigned ? 'text-purple-300' : `${item.color} group-hover:text-gray-100`\n                }`}>\n                  {item.label}\n                </span>\n              </div>\n              {isAssigned && (\n                <span className=\"text-xs text-purple-400\">✓</span>\n              )}\n            </button>\n          );\n        })}\n\n        <div className=\"mt-2 pt-2 border-t border-gray-600\">\n          <p className=\"text-xs text-gray-500 px-3 py-1\">\n            Clic droit ou ⚡ pour accès rapide\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Composant pour une colonne draggable\nexport const DraggableColumn = ({ column, id, onAddToAxis, selectedTable, currentAssignments }) => {\n  const [contextMenu, setContextMenu] = useState({ isOpen: false, position: { x: 0, y: 0 } });\n\n  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({\n    id: id,\n    data: {\n      column: column,\n      type: 'column'\n    }\n  });\n\n  const style = transform ? {\n    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,\n    opacity: isDragging ? 0.7 : 1,\n    zIndex: isDragging ? 1000 : 1\n  } : undefined;\n\n  const handleContextMenu = (e) => {\n    e.preventDefault();\n    setContextMenu({\n      isOpen: true,\n      position: { x: e.clientX, y: e.clientY }\n    });\n  };\n\n  const handleQuickAdd = (e) => {\n    e.stopPropagation();\n    setContextMenu({\n      isOpen: true,\n      position: {\n        x: e.currentTarget.getBoundingClientRect().right + 10,\n        y: e.currentTarget.getBoundingClientRect().top\n      }\n    });\n  };\n\n  const getColumnIcon = (type) => {\n    const dataType = type.toLowerCase();\n    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n      return '🔢';\n    }\n    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n      return '📝';\n    }\n    if (dataType.includes('date') || dataType.includes('time')) {\n      return '📅';\n    }\n    if (dataType.includes('bit') || dataType.includes('boolean')) {\n      return '☑️';\n    }\n    return '📊';\n  };\n\n  const getColumnColor = (type) => {\n    const dataType = type.toLowerCase();\n    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n      return 'bg-blue-600/20 border-blue-500 text-blue-300';\n    }\n    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n      return 'bg-green-600/20 border-green-500 text-green-300';\n    }\n    if (dataType.includes('date') || dataType.includes('time')) {\n      return 'bg-purple-600/20 border-purple-500 text-purple-300';\n    }\n    return 'bg-gray-600/20 border-gray-500 text-gray-300';\n  };\n\n  return (\n    <>\n      <div\n        ref={setNodeRef}\n        style={style}\n        {...listeners}\n        {...attributes}\n        onContextMenu={handleContextMenu}\n        className={`\n          p-3 rounded-lg border-2 border-dashed cursor-grab active:cursor-grabbing\n          transition-all duration-300 ease-in-out transform hover:scale-105 hover:shadow-lg\n          ${getColumnColor(column.type)}\n          ${isDragging ? 'shadow-2xl column-drag-start rotate-3 scale-110' : 'hover:shadow-md hover:shadow-purple-500/10'}\n          group relative\n        `}\n      >\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2 flex-1 min-w-0\">\n            <span className={`text-lg transition-transform duration-200 ${\n              isDragging ? 'scale-110' : 'group-hover:scale-105'\n            }`}>\n              {getColumnIcon(column.type)}\n            </span>\n            <div className=\"flex-1 min-w-0\">\n              <p className={`font-medium truncate transition-colors duration-200 ${\n                isDragging ? 'text-purple-200' : 'group-hover:text-gray-100'\n              }`}>\n                {column.name}\n              </p>\n              <p className={`text-xs transition-all duration-200 ${\n                isDragging ? 'opacity-90 text-purple-300' : 'opacity-75 group-hover:opacity-90'\n              }`}>\n                {column.type}\n              </p>\n            </div>\n          </div>\n\n          {/* Bouton d'ajout rapide */}\n          {onAddToAxis && (\n            <button\n              onClick={handleQuickAdd}\n              className=\"opacity-0 group-hover:opacity-100 transition-all duration-200\n                         p-1 rounded hover:bg-gray-700 text-gray-400 hover:text-purple-400\n                         transform hover:scale-110 active:scale-95\"\n              title=\"Ajouter rapidement\"\n            >\n              ⚡\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Menu contextuel */}\n      <ColumnContextMenu\n        isOpen={contextMenu.isOpen}\n        onClose={() => setContextMenu({ ...contextMenu, isOpen: false })}\n        position={contextMenu.position}\n        column={column}\n        onAddToAxis={onAddToAxis}\n        selectedTable={selectedTable}\n        currentAssignments={currentAssignments}\n      />\n    </>\n  );\n};\n\n// Composant pour une zone de drop\nexport const DropZone = ({ id, title, subtitle, icon, children, acceptedColumn, onClear }) => {\n  const { isOver, setNodeRef } = useDroppable({\n    id: id,\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n\n  return (\n    <div\n      ref={setNodeRef}\n      className={`\n        p-6 rounded-lg border-2 border-dashed min-h-[140px] transition-all duration-200\n        ${isOver\n          ? 'border-purple-400 bg-purple-600/10 scale-105'\n          : 'border-gray-600 bg-gray-800/30'\n        }\n        ${acceptedColumn ? 'border-purple-500 bg-purple-600/20' : ''}\n      `}\n    >\n      {/* Header de la zone */}\n      <div className=\"flex items-center justify-between mb-3\">\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-lg\">{icon}</span>\n          <div>\n            <h4 className=\"font-medium text-gray-200\">{title}</h4>\n            {subtitle && <p className=\"text-xs text-gray-400\">{subtitle}</p>}\n          </div>\n        </div>\n        {acceptedColumn && onClear && (\n          <button\n            onClick={onClear}\n            className=\"text-gray-400 hover:text-red-400 transition-colors\"\n            title=\"Supprimer\"\n          >\n            ✕\n          </button>\n        )}\n      </div>\n\n      {/* Contenu de la zone */}\n      <div className=\"min-h-[60px] flex items-center justify-center\">\n        {acceptedColumn ? (\n          <div className=\"w-full\">\n            <div className=\"flex items-center space-x-3 p-3 bg-purple-600/30 rounded-lg border border-purple-500\">\n              <span className=\"text-xl\">\n                {acceptedColumn.type.toLowerCase().includes('int') ||\n                 acceptedColumn.type.toLowerCase().includes('decimal') ||\n                 acceptedColumn.type.toLowerCase().includes('float') ? '🔢' :\n                 acceptedColumn.type.toLowerCase().includes('date') ? '📅' : '📝'}\n              </span>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"font-medium text-purple-200 truncate\">{acceptedColumn.name}</p>\n                <p className=\"text-xs text-purple-300\">{acceptedColumn.type}</p>\n                {acceptedColumn.table && (\n                  <p className=\"text-xs text-purple-400 mt-1\">📋 {acceptedColumn.table}</p>\n                )}\n              </div>\n            </div>\n          </div>\n        ) : (\n          <div className=\"text-center\">\n            <p className=\"text-gray-500 text-sm\">\n              {isOver ? 'Relâchez ici' : 'Glissez une colonne ici'}\n            </p>\n          </div>\n        )}\n      </div>\n\n      {children}\n    </div>\n  );\n};\n\n// Composant pour afficher les colonnes disponibles\nexport const ColumnsPanel = ({ columns, title, onAddToAxis, selectedTable, currentAssignments }) => {\n  if (!columns || columns.length === 0) {\n    return (\n      <div className=\"text-center py-8\">\n        <div className=\"w-16 h-16 bg-gray-700 rounded-2xl mx-auto mb-4 flex items-center justify-center\">\n          <span className=\"text-2xl\">📋</span>\n        </div>\n        <p className=\"text-gray-400\">Aucune colonne disponible</p>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-200\">{title}</h3>\n        <DarkBadge variant=\"info\">{columns.length}</DarkBadge>\n      </div>\n\n      <div className=\"space-y-3 max-h-[500px] overflow-y-auto\">\n        {columns.map((column, index) => (\n          <DraggableColumn\n            key={`${column.name}-${index}`}\n            id={`column-${column.name}`}\n            column={column}\n            onAddToAxis={onAddToAxis}\n            selectedTable={selectedTable}\n            currentAssignments={currentAssignments}\n          />\n        ))}\n      </div>\n    </div>\n  );\n};\n\n// Composant pour les zones de drop organisées\nexport const DropZonesPanel = ({\n  xAxis,\n  yAxis,\n  legend,\n  values,\n  onClearX,\n  onClearY,\n  onClearLegend,\n  onClearValues\n}) => {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n      <DropZone\n        id=\"x-axis\"\n        title=\"Axe X\"\n        subtitle=\"Catégories ou groupes\"\n        icon=\"📊\"\n        acceptedColumn={xAxis}\n        onClear={onClearX}\n      />\n\n      <DropZone\n        id=\"y-axis\"\n        title=\"Axe Y\"\n        subtitle=\"Valeurs numériques (optionnel)\"\n        icon=\"📈\"\n        acceptedColumn={yAxis}\n        onClear={onClearY}\n      />\n\n      <DropZone\n        id=\"legend\"\n        title=\"Légende\"\n        subtitle=\"Sous-catégories (optionnel)\"\n        icon=\"🏷️\"\n        acceptedColumn={legend}\n        onClear={onClearLegend}\n      />\n\n      <DropZone\n        id=\"values\"\n        title=\"Valeurs\"\n        subtitle=\"Données à agréger\"\n        icon=\"💎\"\n        acceptedColumn={values}\n        onClear={onClearValues}\n      />\n    </div>\n  );\n};\n\n// Composant pour une zone de drop compacte dans la barre fixe\nexport const CompactDropZone = ({ id, title, icon, acceptedColumn, onClear, isOver }) => {\n  return (\n    <div\n      className={`\n        flex items-center justify-between p-3 rounded-lg border-2 border-dashed min-h-[60px]\n        transition-all duration-300 ease-in-out transform\n        ${isOver\n          ? 'border-purple-400 bg-purple-600/20 scale-105 drop-zone-glow drop-zone-hover'\n          : 'border-gray-600 bg-gray-800/50 hover:border-gray-500 hover:bg-gray-800/70'\n        }\n        ${acceptedColumn ? 'border-purple-500 bg-purple-600/30 shadow-lg shadow-purple-500/20' : ''}\n        backdrop-blur-sm hover:backdrop-blur-md\n      `}\n    >\n      <div className=\"flex items-center space-x-2 flex-1 min-w-0\">\n        <span className={`text-lg transition-transform duration-200 ${isOver ? 'scale-110' : ''}`}>\n          {icon}\n        </span>\n        <div className=\"min-w-0 flex-1\">\n          <h4 className={`font-medium text-sm truncate transition-colors duration-200 ${\n            isOver ? 'text-purple-200' : 'text-gray-200'\n          }`}>\n            {title}\n          </h4>\n          {acceptedColumn ? (\n            <div className=\"flex items-center space-x-1 mt-1\">\n              <span className=\"text-xs text-purple-300 truncate font-medium\">\n                {acceptedColumn.name}\n              </span>\n              {acceptedColumn.table && (\n                <span className=\"text-xs text-gray-400\">\n                  ({acceptedColumn.table})\n                </span>\n              )}\n            </div>\n          ) : (\n            <p className={`text-xs transition-colors duration-200 ${\n              isOver ? 'text-purple-300' : 'text-gray-400'\n            }`}>\n              {isOver ? 'Relâchez ici' : 'Glissez ici'}\n            </p>\n          )}\n        </div>\n      </div>\n      {acceptedColumn && onClear && (\n        <button\n          onClick={onClear}\n          className=\"text-gray-400 hover:text-red-400 transition-all duration-200 ml-2 p-1\n                     hover:bg-red-600/20 rounded hover:scale-110 active:scale-95\"\n          title=\"Supprimer\"\n        >\n          ✕\n        </button>\n      )}\n    </div>\n  );\n};\n\n// Barre fixe avec les zones de drop\nexport const FixedDropBar = ({\n  xAxis,\n  yAxis,\n  legend,\n  values,\n  onClearX,\n  onClearY,\n  onClearLegend,\n  onClearValues,\n  isVisible = true,\n  position = 'bottom' // 'top' ou 'bottom'\n}) => {\n  const { isOver: isOverX, setNodeRef: setNodeRefX } = useDroppable({\n    id: 'x-axis-fixed',\n    data: { type: 'dropzone', accepts: ['column'] }\n  });\n\n  const { isOver: isOverY, setNodeRef: setNodeRefY } = useDroppable({\n    id: 'y-axis-fixed',\n    data: { type: 'dropzone', accepts: ['column'] }\n  });\n\n  const { isOver: isOverLegend, setNodeRef: setNodeRefLegend } = useDroppable({\n    id: 'legend-fixed',\n    data: { type: 'dropzone', accepts: ['column'] }\n  });\n\n  const { isOver: isOverValues, setNodeRef: setNodeRefValues } = useDroppable({\n    id: 'values-fixed',\n    data: { type: 'dropzone', accepts: ['column'] }\n  });\n\n  if (!isVisible) return null;\n\n  const positionClasses = position === 'top'\n    ? 'top-0 border-b'\n    : 'bottom-0 border-t';\n\n  const hasAnyColumn = xAxis || yAxis || legend || values;\n\n  return (\n    <div className={`\n      fixed left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-md ${positionClasses} border-gray-700\n      transition-all duration-300 ease-in-out shadow-2xl\n      ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'}\n    `}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 py-3 sm:py-4\">\n        <div className=\"flex items-center justify-between mb-3\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-lg animate-pulse\">🎯</span>\n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-200\">Zones de Drop</h3>\n              {hasAnyColumn && (\n                <p className=\"text-xs text-purple-400\">\n                  {[xAxis, yAxis, legend, values].filter(Boolean).length} colonne(s) assignée(s)\n                </p>\n              )}\n            </div>\n          </div>\n          <div className=\"hidden sm:block text-xs text-gray-400\">\n            Glissez vos colonnes ici pour créer votre visualisation\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3\">\n          <div ref={setNodeRefX}>\n            <CompactDropZone\n              id=\"x-axis-fixed\"\n              title=\"Axe X\"\n              icon=\"📊\"\n              acceptedColumn={xAxis}\n              onClear={onClearX}\n              isOver={isOverX}\n            />\n          </div>\n\n          <div ref={setNodeRefY}>\n            <CompactDropZone\n              id=\"y-axis-fixed\"\n              title=\"Axe Y\"\n              icon=\"📈\"\n              acceptedColumn={yAxis}\n              onClear={onClearY}\n              isOver={isOverY}\n            />\n          </div>\n\n          <div ref={setNodeRefLegend}>\n            <CompactDropZone\n              id=\"legend-fixed\"\n              title=\"Légende\"\n              icon=\"🏷️\"\n              acceptedColumn={legend}\n              onClear={onClearLegend}\n              isOver={isOverLegend}\n            />\n          </div>\n\n          <div ref={setNodeRefValues}>\n            <CompactDropZone\n              id=\"values-fixed\"\n              title=\"Valeurs\"\n              icon=\"💎\"\n              acceptedColumn={values}\n              onClear={onClearValues}\n              isOver={isOverValues}\n            />\n          </div>\n        </div>\n\n        {/* Indicateur mobile */}\n        <div className=\"sm:hidden mt-2 text-center\">\n          <p className=\"text-xs text-gray-500\">\n            Glissez ou utilisez le menu ⚡ des colonnes\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Composant pour le sélecteur de fonction d'agrégation\nexport const AggregationSelector = ({ value, onChange, disabled }) => {\n  const aggregations = [\n    { value: 'SUM', label: 'Somme', icon: '➕', description: 'Addition de toutes les valeurs' },\n    { value: 'AVG', label: 'Moyenne', icon: '📊', description: 'Valeur moyenne' },\n    { value: 'COUNT', label: 'Nombre', icon: '🔢', description: 'Nombre d\\'occurrences' },\n    { value: 'MIN', label: 'Minimum', icon: '⬇️', description: 'Valeur minimale' },\n    { value: 'MAX', label: 'Maximum', icon: '⬆️', description: 'Valeur maximale' }\n  ];\n\n  return (\n    <div>\n      <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n        Fonction d'agrégation\n      </label>\n      <select\n        value={value}\n        onChange={(e) => onChange(e.target.value)}\n        disabled={disabled}\n        className=\"w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-gray-100 focus:border-purple-500 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed\"\n      >\n        {aggregations.map((agg) => (\n          <option key={agg.value} value={agg.value}>\n            {agg.icon} {agg.label} - {agg.description}\n          </option>\n        ))}\n      </select>\n    </div>\n  );\n};\n\n// Composant pour le sélecteur de type de graphique\nexport const ChartTypeSelector = ({ value, onChange }) => {\n  const chartTypes = [\n    { value: 'bar', label: 'Barres', icon: '📊', description: 'Graphique en barres' },\n    { value: 'line', label: 'Ligne', icon: '📈', description: 'Graphique linéaire' },\n    { value: 'pie', label: 'Circulaire', icon: '🥧', description: 'Graphique circulaire' },\n    { value: 'scatter', label: 'Nuage', icon: '⚫', description: 'Nuage de points' },\n    { value: 'stacked_bar', label: 'Barres empilées', icon: '📚', description: 'Barres empilées' }\n  ];\n\n  return (\n    <div>\n      <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n        Type de graphique\n      </label>\n      <div className=\"grid grid-cols-2 md:grid-cols-3 gap-2\">\n        {chartTypes.map((type) => (\n          <button\n            key={type.value}\n            onClick={() => onChange(type.value)}\n            className={`p-3 rounded-lg border transition-colors text-center ${\n              value === type.value\n                ? 'border-purple-500 bg-purple-600/20 text-purple-300'\n                : 'border-gray-700 bg-gray-800/50 text-gray-300 hover:border-gray-600'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">{type.icon}</div>\n            <div className=\"text-xs font-medium\">{type.label}</div>\n          </button>\n        ))}\n      </div>\n    </div>\n  );\n};\n"], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,YAAY,EAAEC,YAAY,QAAQ,eAAe;AAC1D,SAASC,SAAS,QAAQ,gBAAgB;;AAE1C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAAC;EAChCC,MAAM;EACNC,OAAO;EACPC,QAAQ;EACRC,MAAM;EACNC,WAAW;EACXC,aAAa;EACbC,kBAAkB,GAAG,CAAC;AACxB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,OAAO,GAAGlB,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAEvDE,SAAS,CAAC,MAAM;IACd,MAAMoB,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIJ,OAAO,CAACK,OAAO,IAAI,CAACL,OAAO,CAACK,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QAC9Dd,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAED,MAAMe,YAAY,GAAIJ,KAAK,IAAK;MAC9B,IAAIA,KAAK,CAACK,GAAG,KAAK,QAAQ,EAAE;QAC1BhB,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAED,IAAID,MAAM,EAAE;MACVkB,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAER,kBAAkB,CAAC;MAC1DO,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEH,YAAY,CAAC;MAClD,OAAO,MAAM;QACXE,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAET,kBAAkB,CAAC;QAC7DO,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEJ,YAAY,CAAC;MACvD,CAAC;IACH;EACF,CAAC,EAAE,CAAChB,MAAM,EAAEC,OAAO,CAAC,CAAC;EAErB,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMqB,SAAS,GAAG,CAChB;IAAEC,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAC9E;IAAEH,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAiB,CAAC,EAC/E;IAAEH,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,mBAAmB;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAkB,CAAC,EACnF;IAAEH,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,qBAAqB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAkB,CAAC,CACrF;EAED,MAAMC,eAAe,GAAG,MAAOC,IAAI,IAAK;IACtC,IAAIlB,YAAY,EAAE;IAElBC,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMkB,eAAe,GAAG;QAAE,GAAGzB,MAAM;QAAE0B,KAAK,EAAExB;MAAc,CAAC;MAC3D,MAAMD,WAAW,CAACuB,IAAI,CAACL,EAAE,EAAEM,eAAe,CAAC;MAC3C3B,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAChE,CAAC,SAAS;MACRpB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,oBACEd,OAAA;IACEoC,GAAG,EAAExB,OAAQ;IACbyB,SAAS,EAAC,kIAC8C;IACxDC,KAAK,EAAE;MACLC,IAAI,EAAEC,IAAI,CAACC,GAAG,CAACnC,QAAQ,CAACoC,CAAC,EAAEC,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC;MACnDC,GAAG,EAAEL,IAAI,CAACC,GAAG,CAACnC,QAAQ,CAACwC,CAAC,EAAEH,MAAM,CAACI,WAAW,GAAG,GAAG,CAAC;MACnDC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,eAEFjD,OAAA;MAAKqC,SAAS,EAAC,KAAK;MAAAY,QAAA,gBAClBjD,OAAA;QAAKqC,SAAS,EAAC,yCAAyC;QAAAY,QAAA,eACtDjD,OAAA;UAAKqC,SAAS,EAAC,6BAA6B;UAAAY,QAAA,gBAC1CjD,OAAA;YAAMqC,SAAS,EAAC,SAAS;YAAAY,QAAA,EAAEC,aAAa,CAAC3C,MAAM,CAAC4C,IAAI;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7DvD,OAAA;YAAAiD,QAAA,gBACEjD,OAAA;cAAGqC,SAAS,EAAC,mCAAmC;cAAAY,QAAA,EAAE1C,MAAM,CAACiD;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClEvD,OAAA;cAAGqC,SAAS,EAAC,kCAAkC;cAAAY,QAAA,EAAE1C,MAAM,CAAC4C;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL9B,SAAS,CAACgC,GAAG,CAAE1B,IAAI,IAAK;QAAA,IAAA2B,qBAAA;QACvB,MAAMC,UAAU,GAAG,EAAAD,qBAAA,GAAAhD,kBAAkB,CAACqB,IAAI,CAACL,EAAE,CAAC,cAAAgC,qBAAA,uBAA3BA,qBAAA,CAA6BF,IAAI,MAAKjD,MAAM,CAACiD,IAAI;QACpE,oBACExD,OAAA;UAEE4D,OAAO,EAAEA,CAAA,KAAM9B,eAAe,CAACC,IAAI,CAAE;UACrC8B,QAAQ,EAAEhD,YAAa;UACvBwB,SAAS,EAAE;AACzB;AACA;AACA,kBAAkBsB,UAAU,GACR,8CAA8C,GAC9C,mBAAmB;AACvC,kBACkB9C,YAAY,GAAG,+BAA+B,GAAG,gBAAgB;AACnF,eAAgB;UAAAoC,QAAA,gBAEFjD,OAAA;YAAKqC,SAAS,EAAC,6BAA6B;YAAAY,QAAA,gBAC1CjD,OAAA;cAAMqC,SAAS,EAAC,SAAS;cAAAY,QAAA,EAAElB,IAAI,CAACH;YAAI;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5CvD,OAAA;cAAMqC,SAAS,EAAE,WACfsB,UAAU,GAAG,iBAAiB,GAAG,GAAG5B,IAAI,CAACF,KAAK,4BAA4B,EACzE;cAAAoB,QAAA,EACAlB,IAAI,CAACJ;YAAK;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EACLI,UAAU,iBACT3D,OAAA;YAAMqC,SAAS,EAAC,yBAAyB;YAAAY,QAAA,EAAC;UAAC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAClD;QAAA,GAvBIxB,IAAI,CAACL,EAAE;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwBN,CAAC;MAEb,CAAC,CAAC,eAEFvD,OAAA;QAAKqC,SAAS,EAAC,oCAAoC;QAAAY,QAAA,eACjDjD,OAAA;UAAGqC,SAAS,EAAC,iCAAiC;UAAAY,QAAA,EAAC;QAE/C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA5C,EAAA,CA3HaR,iBAAiB;AAAA2D,EAAA,GAAjB3D,iBAAiB;AA4H9B,OAAO,MAAM4D,eAAe,GAAGA,CAAC;EAAExD,MAAM;EAAEmB,EAAE;EAAElB,WAAW;EAAEC,aAAa;EAAEC;AAAmB,CAAC,KAAK;EAAAsD,GAAA;EACjG,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzE,QAAQ,CAAC;IAAEW,MAAM,EAAE,KAAK;IAAEE,QAAQ,EAAE;MAAEoC,CAAC,EAAE,CAAC;MAAEI,CAAC,EAAE;IAAE;EAAE,CAAC,CAAC;EAE3F,MAAM;IAAEqB,UAAU;IAAEC,SAAS;IAAEC,UAAU;IAAEC,SAAS;IAAEC;EAAW,CAAC,GAAG3E,YAAY,CAAC;IAChF8B,EAAE,EAAEA,EAAE;IACN8C,IAAI,EAAE;MACJjE,MAAM,EAAEA,MAAM;MACd4C,IAAI,EAAE;IACR;EACF,CAAC,CAAC;EAEF,MAAMb,KAAK,GAAGgC,SAAS,GAAG;IACxBA,SAAS,EAAE,eAAeA,SAAS,CAAC5B,CAAC,OAAO4B,SAAS,CAACxB,CAAC,QAAQ;IAC/D2B,OAAO,EAAEF,UAAU,GAAG,GAAG,GAAG,CAAC;IAC7BG,MAAM,EAAEH,UAAU,GAAG,IAAI,GAAG;EAC9B,CAAC,GAAGI,SAAS;EAEb,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBZ,cAAc,CAAC;MACb9D,MAAM,EAAE,IAAI;MACZE,QAAQ,EAAE;QAAEoC,CAAC,EAAEmC,CAAC,CAACE,OAAO;QAAEjC,CAAC,EAAE+B,CAAC,CAACG;MAAQ;IACzC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAIJ,CAAC,IAAK;IAC5BA,CAAC,CAACK,eAAe,CAAC,CAAC;IACnBhB,cAAc,CAAC;MACb9D,MAAM,EAAE,IAAI;MACZE,QAAQ,EAAE;QACRoC,CAAC,EAAEmC,CAAC,CAACM,aAAa,CAACC,qBAAqB,CAAC,CAAC,CAACC,KAAK,GAAG,EAAE;QACrDvC,CAAC,EAAE+B,CAAC,CAACM,aAAa,CAACC,qBAAqB,CAAC,CAAC,CAACvC;MAC7C;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMK,aAAa,GAAIC,IAAI,IAAK;IAC9B,MAAMmC,QAAQ,GAAGnC,IAAI,CAACoC,WAAW,CAAC,CAAC;IACnC,IAAID,QAAQ,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MACxH,OAAO,IAAI;IACb;IACA,IAAIF,QAAQ,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1F,OAAO,IAAI;IACb;IACA,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1D,OAAO,IAAI;IACb;IACA,IAAIF,QAAQ,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;MAC5D,OAAO,IAAI;IACb;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMC,cAAc,GAAItC,IAAI,IAAK;IAC/B,MAAMmC,QAAQ,GAAGnC,IAAI,CAACoC,WAAW,CAAC,CAAC;IACnC,IAAID,QAAQ,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MACxH,OAAO,8CAA8C;IACvD;IACA,IAAIF,QAAQ,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1F,OAAO,iDAAiD;IAC1D;IACA,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1D,OAAO,oDAAoD;IAC7D;IACA,OAAO,8CAA8C;EACvD,CAAC;EAED,oBACExF,OAAA,CAAAE,SAAA;IAAA+C,QAAA,gBACEjD,OAAA;MACEoC,GAAG,EAAEiC,UAAW;MAChB/B,KAAK,EAAEA,KAAM;MAAA,GACT8B,SAAS;MAAA,GACTD,UAAU;MACduB,aAAa,EAAEd,iBAAkB;MACjCvC,SAAS,EAAE;AACnB;AACA;AACA,YAAYoD,cAAc,CAAClF,MAAM,CAAC4C,IAAI,CAAC;AACvC,YAAYoB,UAAU,GAAG,iDAAiD,GAAG,4CAA4C;AACzH;AACA,SAAU;MAAAtB,QAAA,eAEFjD,OAAA;QAAKqC,SAAS,EAAC,mCAAmC;QAAAY,QAAA,gBAChDjD,OAAA;UAAKqC,SAAS,EAAC,4CAA4C;UAAAY,QAAA,gBACzDjD,OAAA;YAAMqC,SAAS,EAAE,6CACfkC,UAAU,GAAG,WAAW,GAAG,uBAAuB,EACjD;YAAAtB,QAAA,EACAC,aAAa,CAAC3C,MAAM,CAAC4C,IAAI;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACPvD,OAAA;YAAKqC,SAAS,EAAC,gBAAgB;YAAAY,QAAA,gBAC7BjD,OAAA;cAAGqC,SAAS,EAAE,uDACZkC,UAAU,GAAG,iBAAiB,GAAG,2BAA2B,EAC3D;cAAAtB,QAAA,EACA1C,MAAM,CAACiD;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACJvD,OAAA;cAAGqC,SAAS,EAAE,uCACZkC,UAAU,GAAG,4BAA4B,GAAG,mCAAmC,EAC9E;cAAAtB,QAAA,EACA1C,MAAM,CAAC4C;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL/C,WAAW,iBACVR,OAAA;UACE4D,OAAO,EAAEqB,cAAe;UACxB5C,SAAS,EAAC,2KAE2C;UACrDsD,KAAK,EAAC,oBAAoB;UAAA1C,QAAA,EAC3B;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvD,OAAA,CAACG,iBAAiB;MAChBC,MAAM,EAAE6D,WAAW,CAAC7D,MAAO;MAC3BC,OAAO,EAAEA,CAAA,KAAM6D,cAAc,CAAC;QAAE,GAAGD,WAAW;QAAE7D,MAAM,EAAE;MAAM,CAAC,CAAE;MACjEE,QAAQ,EAAE2D,WAAW,CAAC3D,QAAS;MAC/BC,MAAM,EAAEA,MAAO;MACfC,WAAW,EAAEA,WAAY;MACzBC,aAAa,EAAEA,aAAc;MAC7BC,kBAAkB,EAAEA;IAAmB;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC;EAAA,eACF,CAAC;AAEP,CAAC;;AAED;AAAAS,GAAA,CArIaD,eAAe;EAAA,QAG2CnE,YAAY;AAAA;AAAAgG,GAAA,GAHtE7B,eAAe;AAsI5B,OAAO,MAAM8B,QAAQ,GAAGA,CAAC;EAAEnE,EAAE;EAAEiE,KAAK;EAAEG,QAAQ;EAAElE,IAAI;EAAEqB,QAAQ;EAAE8C,cAAc;EAAEC;AAAQ,CAAC,KAAK;EAAAC,GAAA;EAC5F,MAAM;IAAEC,MAAM;IAAE7B;EAAW,CAAC,GAAGxE,YAAY,CAAC;IAC1C6B,EAAE,EAAEA,EAAE;IACN8C,IAAI,EAAE;MACJrB,IAAI,EAAE,UAAU;MAChBgD,OAAO,EAAE,CAAC,QAAQ;IACpB;EACF,CAAC,CAAC;EAEF,oBACEnG,OAAA;IACEoC,GAAG,EAAEiC,UAAW;IAChBhC,SAAS,EAAE;AACjB;AACA,UAAU6D,MAAM,GACJ,8CAA8C,GAC9C,gCAAgC;AAC5C,UACUH,cAAc,GAAG,oCAAoC,GAAG,EAAE;AACpE,OAAQ;IAAA9C,QAAA,gBAGFjD,OAAA;MAAKqC,SAAS,EAAC,wCAAwC;MAAAY,QAAA,gBACrDjD,OAAA;QAAKqC,SAAS,EAAC,6BAA6B;QAAAY,QAAA,gBAC1CjD,OAAA;UAAMqC,SAAS,EAAC,SAAS;UAAAY,QAAA,EAAErB;QAAI;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvCvD,OAAA;UAAAiD,QAAA,gBACEjD,OAAA;YAAIqC,SAAS,EAAC,2BAA2B;YAAAY,QAAA,EAAE0C;UAAK;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EACrDuC,QAAQ,iBAAI9F,OAAA;YAAGqC,SAAS,EAAC,uBAAuB;YAAAY,QAAA,EAAE6C;UAAQ;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACLwC,cAAc,IAAIC,OAAO,iBACxBhG,OAAA;QACE4D,OAAO,EAAEoC,OAAQ;QACjB3D,SAAS,EAAC,oDAAoD;QAC9DsD,KAAK,EAAC,WAAW;QAAA1C,QAAA,EAClB;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNvD,OAAA;MAAKqC,SAAS,EAAC,+CAA+C;MAAAY,QAAA,EAC3D8C,cAAc,gBACb/F,OAAA;QAAKqC,SAAS,EAAC,QAAQ;QAAAY,QAAA,eACrBjD,OAAA;UAAKqC,SAAS,EAAC,sFAAsF;UAAAY,QAAA,gBACnGjD,OAAA;YAAMqC,SAAS,EAAC,SAAS;YAAAY,QAAA,EACtB8C,cAAc,CAAC5C,IAAI,CAACoC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,IACjDO,cAAc,CAAC5C,IAAI,CAACoC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,SAAS,CAAC,IACrDO,cAAc,CAAC5C,IAAI,CAACoC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,GAAG,IAAI,GAC1DO,cAAc,CAAC5C,IAAI,CAACoC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG;UAAI;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACPvD,OAAA;YAAKqC,SAAS,EAAC,gBAAgB;YAAAY,QAAA,gBAC7BjD,OAAA;cAAGqC,SAAS,EAAC,sCAAsC;cAAAY,QAAA,EAAE8C,cAAc,CAACvC;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7EvD,OAAA;cAAGqC,SAAS,EAAC,yBAAyB;cAAAY,QAAA,EAAE8C,cAAc,CAAC5C;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC/DwC,cAAc,CAAC9D,KAAK,iBACnBjC,OAAA;cAAGqC,SAAS,EAAC,8BAA8B;cAAAY,QAAA,GAAC,eAAG,EAAC8C,cAAc,CAAC9D,KAAK;YAAA;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACzE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAENvD,OAAA;QAAKqC,SAAS,EAAC,aAAa;QAAAY,QAAA,eAC1BjD,OAAA;UAAGqC,SAAS,EAAC,uBAAuB;UAAAY,QAAA,EACjCiD,MAAM,GAAG,cAAc,GAAG;QAAyB;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELN,QAAQ;EAAA;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AAAA0C,GAAA,CA3EaJ,QAAQ;EAAA,QACYhG,YAAY;AAAA;AAAAuG,GAAA,GADhCP,QAAQ;AA4ErB,OAAO,MAAMQ,YAAY,GAAGA,CAAC;EAAEC,OAAO;EAAEX,KAAK;EAAEnF,WAAW;EAAEC,aAAa;EAAEC;AAAmB,CAAC,KAAK;EAClG,IAAI,CAAC4F,OAAO,IAAIA,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;IACpC,oBACEvG,OAAA;MAAKqC,SAAS,EAAC,kBAAkB;MAAAY,QAAA,gBAC/BjD,OAAA;QAAKqC,SAAS,EAAC,iFAAiF;QAAAY,QAAA,eAC9FjD,OAAA;UAAMqC,SAAS,EAAC,UAAU;UAAAY,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACNvD,OAAA;QAAGqC,SAAS,EAAC,eAAe;QAAAY,QAAA,EAAC;MAAyB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD,CAAC;EAEV;EAEA,oBACEvD,OAAA;IAAAiD,QAAA,gBACEjD,OAAA;MAAKqC,SAAS,EAAC,wCAAwC;MAAAY,QAAA,gBACrDjD,OAAA;QAAIqC,SAAS,EAAC,qCAAqC;QAAAY,QAAA,EAAE0C;MAAK;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAChEvD,OAAA,CAACF,SAAS;QAAC0G,OAAO,EAAC,MAAM;QAAAvD,QAAA,EAAEqD,OAAO,CAACC;MAAM;QAAAnD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC,eAENvD,OAAA;MAAKqC,SAAS,EAAC,yCAAyC;MAAAY,QAAA,EACrDqD,OAAO,CAAC7C,GAAG,CAAC,CAAClD,MAAM,EAAEkG,KAAK,kBACzBzG,OAAA,CAAC+D,eAAe;QAEdrC,EAAE,EAAE,UAAUnB,MAAM,CAACiD,IAAI,EAAG;QAC5BjD,MAAM,EAAEA,MAAO;QACfC,WAAW,EAAEA,WAAY;QACzBC,aAAa,EAAEA,aAAc;QAC7BC,kBAAkB,EAAEA;MAAmB,GALlC,GAAGH,MAAM,CAACiD,IAAI,IAAIiD,KAAK,EAAE;QAAArD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAM/B,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAmD,GAAA,GAnCaL,YAAY;AAoCzB,OAAO,MAAMM,cAAc,GAAGA,CAAC;EAC7BC,KAAK;EACLC,KAAK;EACLC,MAAM;EACNC,MAAM;EACNC,QAAQ;EACRC,QAAQ;EACRC,aAAa;EACbC;AACF,CAAC,KAAK;EACJ,oBACEnH,OAAA;IAAKqC,SAAS,EAAC,uCAAuC;IAAAY,QAAA,gBACpDjD,OAAA,CAAC6F,QAAQ;MACPnE,EAAE,EAAC,QAAQ;MACXiE,KAAK,EAAC,OAAO;MACbG,QAAQ,EAAC,0BAAuB;MAChClE,IAAI,EAAC,cAAI;MACTmE,cAAc,EAAEa,KAAM;MACtBZ,OAAO,EAAEgB;IAAS;MAAA5D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eAEFvD,OAAA,CAAC6F,QAAQ;MACPnE,EAAE,EAAC,QAAQ;MACXiE,KAAK,EAAC,OAAO;MACbG,QAAQ,EAAC,mCAAgC;MACzClE,IAAI,EAAC,cAAI;MACTmE,cAAc,EAAEc,KAAM;MACtBb,OAAO,EAAEiB;IAAS;MAAA7D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eAEFvD,OAAA,CAAC6F,QAAQ;MACPnE,EAAE,EAAC,QAAQ;MACXiE,KAAK,EAAC,YAAS;MACfG,QAAQ,EAAC,gCAA6B;MACtClE,IAAI,EAAC,oBAAK;MACVmE,cAAc,EAAEe,MAAO;MACvBd,OAAO,EAAEkB;IAAc;MAAA9D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAEFvD,OAAA,CAAC6F,QAAQ;MACPnE,EAAE,EAAC,QAAQ;MACXiE,KAAK,EAAC,SAAS;MACfG,QAAQ,EAAC,4BAAmB;MAC5BlE,IAAI,EAAC,cAAI;MACTmE,cAAc,EAAEgB,MAAO;MACvBf,OAAO,EAAEmB;IAAc;MAAA/D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;;AAED;AAAA6D,GAAA,GAnDaT,cAAc;AAoD3B,OAAO,MAAMU,eAAe,GAAGA,CAAC;EAAE3F,EAAE;EAAEiE,KAAK;EAAE/D,IAAI;EAAEmE,cAAc;EAAEC,OAAO;EAAEE;AAAO,CAAC,KAAK;EACvF,oBACElG,OAAA;IACEqC,SAAS,EAAE;AACjB;AACA;AACA,UAAU6D,MAAM,GACJ,6EAA6E,GAC7E,2EAA2E;AACvF,UACUH,cAAc,GAAG,mEAAmE,GAAG,EAAE;AACnG;AACA,OAAQ;IAAA9C,QAAA,gBAEFjD,OAAA;MAAKqC,SAAS,EAAC,4CAA4C;MAAAY,QAAA,gBACzDjD,OAAA;QAAMqC,SAAS,EAAE,6CAA6C6D,MAAM,GAAG,WAAW,GAAG,EAAE,EAAG;QAAAjD,QAAA,EACvFrB;MAAI;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACPvD,OAAA;QAAKqC,SAAS,EAAC,gBAAgB;QAAAY,QAAA,gBAC7BjD,OAAA;UAAIqC,SAAS,EAAE,+DACb6D,MAAM,GAAG,iBAAiB,GAAG,eAAe,EAC3C;UAAAjD,QAAA,EACA0C;QAAK;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EACJwC,cAAc,gBACb/F,OAAA;UAAKqC,SAAS,EAAC,kCAAkC;UAAAY,QAAA,gBAC/CjD,OAAA;YAAMqC,SAAS,EAAC,8CAA8C;YAAAY,QAAA,EAC3D8C,cAAc,CAACvC;UAAI;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,EACNwC,cAAc,CAAC9D,KAAK,iBACnBjC,OAAA;YAAMqC,SAAS,EAAC,uBAAuB;YAAAY,QAAA,GAAC,GACrC,EAAC8C,cAAc,CAAC9D,KAAK,EAAC,GACzB;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAENvD,OAAA;UAAGqC,SAAS,EAAE,0CACZ6D,MAAM,GAAG,iBAAiB,GAAG,eAAe,EAC3C;UAAAjD,QAAA,EACAiD,MAAM,GAAG,cAAc,GAAG;QAAa;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EACLwC,cAAc,IAAIC,OAAO,iBACxBhG,OAAA;MACE4D,OAAO,EAAEoC,OAAQ;MACjB3D,SAAS,EAAC,mIAC6D;MACvEsD,KAAK,EAAC,WAAW;MAAA1C,QAAA,EAClB;IAED;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CACT;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAA+D,GAAA,GA1DaD,eAAe;AA2D5B,OAAO,MAAME,YAAY,GAAGA,CAAC;EAC3BX,KAAK;EACLC,KAAK;EACLC,MAAM;EACNC,MAAM;EACNC,QAAQ;EACRC,QAAQ;EACRC,aAAa;EACbC,aAAa;EACbK,SAAS,GAAG,IAAI;EAChBlH,QAAQ,GAAG,QAAQ,CAAC;AACtB,CAAC,KAAK;EAAAmH,GAAA;EACJ,MAAM;IAAEvB,MAAM,EAAEwB,OAAO;IAAErD,UAAU,EAAEsD;EAAY,CAAC,GAAG9H,YAAY,CAAC;IAChE6B,EAAE,EAAE,cAAc;IAClB8C,IAAI,EAAE;MAAErB,IAAI,EAAE,UAAU;MAAEgD,OAAO,EAAE,CAAC,QAAQ;IAAE;EAChD,CAAC,CAAC;EAEF,MAAM;IAAED,MAAM,EAAE0B,OAAO;IAAEvD,UAAU,EAAEwD;EAAY,CAAC,GAAGhI,YAAY,CAAC;IAChE6B,EAAE,EAAE,cAAc;IAClB8C,IAAI,EAAE;MAAErB,IAAI,EAAE,UAAU;MAAEgD,OAAO,EAAE,CAAC,QAAQ;IAAE;EAChD,CAAC,CAAC;EAEF,MAAM;IAAED,MAAM,EAAE4B,YAAY;IAAEzD,UAAU,EAAE0D;EAAiB,CAAC,GAAGlI,YAAY,CAAC;IAC1E6B,EAAE,EAAE,cAAc;IAClB8C,IAAI,EAAE;MAAErB,IAAI,EAAE,UAAU;MAAEgD,OAAO,EAAE,CAAC,QAAQ;IAAE;EAChD,CAAC,CAAC;EAEF,MAAM;IAAED,MAAM,EAAE8B,YAAY;IAAE3D,UAAU,EAAE4D;EAAiB,CAAC,GAAGpI,YAAY,CAAC;IAC1E6B,EAAE,EAAE,cAAc;IAClB8C,IAAI,EAAE;MAAErB,IAAI,EAAE,UAAU;MAAEgD,OAAO,EAAE,CAAC,QAAQ;IAAE;EAChD,CAAC,CAAC;EAEF,IAAI,CAACqB,SAAS,EAAE,OAAO,IAAI;EAE3B,MAAMU,eAAe,GAAG5H,QAAQ,KAAK,KAAK,GACtC,gBAAgB,GAChB,mBAAmB;EAEvB,MAAM6H,YAAY,GAAGvB,KAAK,IAAIC,KAAK,IAAIC,MAAM,IAAIC,MAAM;EAEvD,oBACE/G,OAAA;IAAKqC,SAAS,EAAE;AACpB,kEAAkE6F,eAAe;AACjF;AACA,QAAQV,SAAS,GAAG,2BAA2B,GAAG,4BAA4B;AAC9E,KAAM;IAAAvE,QAAA,eACAjD,OAAA;MAAKqC,SAAS,EAAC,6CAA6C;MAAAY,QAAA,gBAC1DjD,OAAA;QAAKqC,SAAS,EAAC,wCAAwC;QAAAY,QAAA,gBACrDjD,OAAA;UAAKqC,SAAS,EAAC,6BAA6B;UAAAY,QAAA,gBAC1CjD,OAAA;YAAMqC,SAAS,EAAC,uBAAuB;YAAAY,QAAA,EAAC;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjDvD,OAAA;YAAAiD,QAAA,gBACEjD,OAAA;cAAIqC,SAAS,EAAC,qCAAqC;cAAAY,QAAA,EAAC;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACrE4E,YAAY,iBACXnI,OAAA;cAAGqC,SAAS,EAAC,yBAAyB;cAAAY,QAAA,GACnC,CAAC2D,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,CAAC,CAACqB,MAAM,CAACC,OAAO,CAAC,CAAC9B,MAAM,EAAC,4BACzD;YAAA;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvD,OAAA;UAAKqC,SAAS,EAAC,uCAAuC;UAAAY,QAAA,EAAC;QAEvD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvD,OAAA;QAAKqC,SAAS,EAAC,gDAAgD;QAAAY,QAAA,gBAC7DjD,OAAA;UAAKoC,GAAG,EAAEuF,WAAY;UAAA1E,QAAA,eACpBjD,OAAA,CAACqH,eAAe;YACd3F,EAAE,EAAC,cAAc;YACjBiE,KAAK,EAAC,OAAO;YACb/D,IAAI,EAAC,cAAI;YACTmE,cAAc,EAAEa,KAAM;YACtBZ,OAAO,EAAEgB,QAAS;YAClBd,MAAM,EAAEwB;UAAQ;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENvD,OAAA;UAAKoC,GAAG,EAAEyF,WAAY;UAAA5E,QAAA,eACpBjD,OAAA,CAACqH,eAAe;YACd3F,EAAE,EAAC,cAAc;YACjBiE,KAAK,EAAC,OAAO;YACb/D,IAAI,EAAC,cAAI;YACTmE,cAAc,EAAEc,KAAM;YACtBb,OAAO,EAAEiB,QAAS;YAClBf,MAAM,EAAE0B;UAAQ;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENvD,OAAA;UAAKoC,GAAG,EAAE2F,gBAAiB;UAAA9E,QAAA,eACzBjD,OAAA,CAACqH,eAAe;YACd3F,EAAE,EAAC,cAAc;YACjBiE,KAAK,EAAC,YAAS;YACf/D,IAAI,EAAC,oBAAK;YACVmE,cAAc,EAAEe,MAAO;YACvBd,OAAO,EAAEkB,aAAc;YACvBhB,MAAM,EAAE4B;UAAa;YAAA1E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENvD,OAAA;UAAKoC,GAAG,EAAE6F,gBAAiB;UAAAhF,QAAA,eACzBjD,OAAA,CAACqH,eAAe;YACd3F,EAAE,EAAC,cAAc;YACjBiE,KAAK,EAAC,SAAS;YACf/D,IAAI,EAAC,cAAI;YACTmE,cAAc,EAAEgB,MAAO;YACvBf,OAAO,EAAEmB,aAAc;YACvBjB,MAAM,EAAE8B;UAAa;YAAA5E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvD,OAAA;QAAKqC,SAAS,EAAC,4BAA4B;QAAAY,QAAA,eACzCjD,OAAA;UAAGqC,SAAS,EAAC,uBAAuB;UAAAY,QAAA,EAAC;QAErC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAkE,GAAA,CAzHaF,YAAY;EAAA,QAY8B1H,YAAY,EAKZA,YAAY,EAKFA,YAAY,EAKZA,YAAY;AAAA;AAAAyI,GAAA,GA3BhEf,YAAY;AA0HzB,OAAO,MAAMgB,mBAAmB,GAAGA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAE5E;AAAS,CAAC,KAAK;EACpE,MAAM6E,YAAY,GAAG,CACnB;IAAEF,KAAK,EAAE,KAAK;IAAE7G,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,GAAG;IAAE+G,WAAW,EAAE;EAAiC,CAAC,EAC1F;IAAEH,KAAK,EAAE,KAAK;IAAE7G,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,IAAI;IAAE+G,WAAW,EAAE;EAAiB,CAAC,EAC7E;IAAEH,KAAK,EAAE,OAAO;IAAE7G,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE,IAAI;IAAE+G,WAAW,EAAE;EAAwB,CAAC,EACrF;IAAEH,KAAK,EAAE,KAAK;IAAE7G,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,IAAI;IAAE+G,WAAW,EAAE;EAAkB,CAAC,EAC9E;IAAEH,KAAK,EAAE,KAAK;IAAE7G,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,IAAI;IAAE+G,WAAW,EAAE;EAAkB,CAAC,CAC/E;EAED,oBACE3I,OAAA;IAAAiD,QAAA,gBACEjD,OAAA;MAAOqC,SAAS,EAAC,8CAA8C;MAAAY,QAAA,EAAC;IAEhE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACRvD,OAAA;MACEwI,KAAK,EAAEA,KAAM;MACbC,QAAQ,EAAG5D,CAAC,IAAK4D,QAAQ,CAAC5D,CAAC,CAAC1D,MAAM,CAACqH,KAAK,CAAE;MAC1C3E,QAAQ,EAAEA,QAAS;MACnBxB,SAAS,EAAC,mKAAmK;MAAAY,QAAA,EAE5KyF,YAAY,CAACjF,GAAG,CAAEmF,GAAG,iBACpB5I,OAAA;QAAwBwI,KAAK,EAAEI,GAAG,CAACJ,KAAM;QAAAvF,QAAA,GACtC2F,GAAG,CAAChH,IAAI,EAAC,GAAC,EAACgH,GAAG,CAACjH,KAAK,EAAC,KAAG,EAACiH,GAAG,CAACD,WAAW;MAAA,GAD9BC,GAAG,CAACJ,KAAK;QAAApF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEd,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AAAAsF,GAAA,GA9BaN,mBAAmB;AA+BhC,OAAO,MAAMO,iBAAiB,GAAGA,CAAC;EAAEN,KAAK;EAAEC;AAAS,CAAC,KAAK;EACxD,MAAMM,UAAU,GAAG,CACjB;IAAEP,KAAK,EAAE,KAAK;IAAE7G,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE,IAAI;IAAE+G,WAAW,EAAE;EAAsB,CAAC,EACjF;IAAEH,KAAK,EAAE,MAAM;IAAE7G,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,IAAI;IAAE+G,WAAW,EAAE;EAAqB,CAAC,EAChF;IAAEH,KAAK,EAAE,KAAK;IAAE7G,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE,IAAI;IAAE+G,WAAW,EAAE;EAAuB,CAAC,EACtF;IAAEH,KAAK,EAAE,SAAS;IAAE7G,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,GAAG;IAAE+G,WAAW,EAAE;EAAkB,CAAC,EAC/E;IAAEH,KAAK,EAAE,aAAa;IAAE7G,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,IAAI;IAAE+G,WAAW,EAAE;EAAkB,CAAC,CAC/F;EAED,oBACE3I,OAAA;IAAAiD,QAAA,gBACEjD,OAAA;MAAOqC,SAAS,EAAC,8CAA8C;MAAAY,QAAA,EAAC;IAEhE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACRvD,OAAA;MAAKqC,SAAS,EAAC,uCAAuC;MAAAY,QAAA,EACnD8F,UAAU,CAACtF,GAAG,CAAEN,IAAI,iBACnBnD,OAAA;QAEE4D,OAAO,EAAEA,CAAA,KAAM6E,QAAQ,CAACtF,IAAI,CAACqF,KAAK,CAAE;QACpCnG,SAAS,EAAE,uDACTmG,KAAK,KAAKrF,IAAI,CAACqF,KAAK,GAChB,oDAAoD,GACpD,oEAAoE,EACvE;QAAAvF,QAAA,gBAEHjD,OAAA;UAAKqC,SAAS,EAAC,cAAc;UAAAY,QAAA,EAAEE,IAAI,CAACvB;QAAI;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/CvD,OAAA;UAAKqC,SAAS,EAAC,qBAAqB;UAAAY,QAAA,EAAEE,IAAI,CAACxB;QAAK;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,GATlDJ,IAAI,CAACqF,KAAK;QAAApF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUT,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACyF,GAAA,GAhCWF,iBAAiB;AAAA,IAAAhF,EAAA,EAAA8B,GAAA,EAAAQ,GAAA,EAAAM,GAAA,EAAAU,GAAA,EAAAE,GAAA,EAAAgB,GAAA,EAAAO,GAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAnF,EAAA;AAAAmF,YAAA,CAAArD,GAAA;AAAAqD,YAAA,CAAA7C,GAAA;AAAA6C,YAAA,CAAAvC,GAAA;AAAAuC,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}