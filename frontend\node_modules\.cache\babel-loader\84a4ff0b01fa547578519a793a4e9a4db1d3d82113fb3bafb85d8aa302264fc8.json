{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIZekri\\\\frontend\\\\src\\\\components\\\\DragDropComponents.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\n/**\n * DragDropComponents - Composants pour l'interface drag & drop\n * Utilise @dnd-kit pour créer des colonnes draggables et des zones de drop\n */\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useDraggable, useDroppable } from '@dnd-kit/core';\nimport { DarkBadge } from './YellowMindUI';\n\n// Composant de menu contextuel pour les colonnes\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const ColumnContextMenu = ({\n  isOpen,\n  onClose,\n  position,\n  column,\n  onAddToAxis,\n  selectedTable\n}) => {\n  _s();\n  const menuRef = useRef(null);\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (menuRef.current && !menuRef.current.contains(event.target)) {\n        onClose();\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => document.removeEventListener('mousedown', handleClickOutside);\n    }\n  }, [isOpen, onClose]);\n  if (!isOpen) return null;\n  const menuItems = [{\n    id: 'x-axis',\n    label: 'Ajouter à Axe X',\n    icon: '📊',\n    color: 'text-blue-400'\n  }, {\n    id: 'y-axis',\n    label: 'Ajouter à Axe Y',\n    icon: '📈',\n    color: 'text-green-400'\n  }, {\n    id: 'legend',\n    label: 'Ajouter à Légende',\n    icon: '🏷️',\n    color: 'text-yellow-400'\n  }, {\n    id: 'values',\n    label: 'Ajouter aux Valeurs',\n    icon: '💎',\n    color: 'text-purple-400'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: menuRef,\n    className: \"fixed z-50 bg-gray-800 border border-gray-600 rounded-lg shadow-xl backdrop-blur-sm\",\n    style: {\n      left: position.x,\n      top: position.y,\n      minWidth: '200px'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-3 py-2 border-b border-gray-600 mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm font-medium text-gray-200\",\n          children: column.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-400\",\n          children: column.type\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), menuItems.map(item => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          const columnWithTable = {\n            ...column,\n            table: selectedTable\n          };\n          onAddToAxis(item.id, columnWithTable);\n          onClose();\n        },\n        className: `\n              w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left\n              hover:bg-gray-700 transition-colors duration-200 group\n            `,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-lg\",\n          children: item.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `text-sm ${item.color} group-hover:text-gray-100`,\n          children: item.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this)]\n      }, item.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour une colonne draggable\n_s(ColumnContextMenu, \"lbfKxozlpk19p2tUpYavRIkbEU0=\");\n_c = ColumnContextMenu;\nexport const DraggableColumn = ({\n  column,\n  id,\n  onAddToAxis,\n  selectedTable\n}) => {\n  _s2();\n  const {\n    attributes,\n    listeners,\n    setNodeRef,\n    transform,\n    isDragging\n  } = useDraggable({\n    id: id,\n    data: {\n      column: column,\n      type: 'column'\n    }\n  });\n  const style = transform ? {\n    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,\n    opacity: isDragging ? 0.7 : 1,\n    zIndex: isDragging ? 1000 : 1\n  } : undefined;\n  const getColumnIcon = type => {\n    const dataType = type.toLowerCase();\n    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n      return '🔢';\n    }\n    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n      return '📝';\n    }\n    if (dataType.includes('date') || dataType.includes('time')) {\n      return '📅';\n    }\n    if (dataType.includes('bit') || dataType.includes('boolean')) {\n      return '☑️';\n    }\n    return '📊';\n  };\n  const getColumnColor = type => {\n    const dataType = type.toLowerCase();\n    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n      return 'bg-blue-600/20 border-blue-500 text-blue-300';\n    }\n    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n      return 'bg-green-600/20 border-green-500 text-green-300';\n    }\n    if (dataType.includes('date') || dataType.includes('time')) {\n      return 'bg-purple-600/20 border-purple-500 text-purple-300';\n    }\n    return 'bg-gray-600/20 border-gray-500 text-gray-300';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: setNodeRef,\n    style: style,\n    ...listeners,\n    ...attributes,\n    className: `\n        p-3 rounded-lg border-2 border-dashed cursor-grab active:cursor-grabbing\n        transition-all duration-300 ease-in-out transform hover:scale-105 hover:shadow-lg\n        ${getColumnColor(column.type)}\n        ${isDragging ? 'shadow-2xl column-drag-start rotate-3 scale-110' : 'hover:shadow-md hover:shadow-purple-500/10'}\n        group\n      `,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: `text-lg transition-transform duration-200 ${isDragging ? 'scale-110' : 'group-hover:scale-105'}`,\n        children: getColumnIcon(column.type)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 min-w-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: `font-medium truncate transition-colors duration-200 ${isDragging ? 'text-purple-200' : 'group-hover:text-gray-100'}`,\n          children: column.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-xs transition-all duration-200 ${isDragging ? 'opacity-90 text-purple-300' : 'opacity-75 group-hover:opacity-90'}`,\n          children: column.type\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 131,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour une zone de drop\n_s2(DraggableColumn, \"uZNJ3/8UzXAvFYh/tqZxqBQOH0I=\", false, function () {\n  return [useDraggable];\n});\n_c2 = DraggableColumn;\nexport const DropZone = ({\n  id,\n  title,\n  subtitle,\n  icon,\n  children,\n  acceptedColumn,\n  onClear\n}) => {\n  _s3();\n  const {\n    isOver,\n    setNodeRef\n  } = useDroppable({\n    id: id,\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: setNodeRef,\n    className: `\n        p-6 rounded-lg border-2 border-dashed min-h-[140px] transition-all duration-200\n        ${isOver ? 'border-purple-400 bg-purple-600/10 scale-105' : 'border-gray-600 bg-gray-800/30'}\n        ${acceptedColumn ? 'border-purple-500 bg-purple-600/20' : ''}\n      `,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-lg\",\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium text-gray-200\",\n            children: title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), subtitle && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-400\",\n            children: subtitle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 26\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), acceptedColumn && onClear && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClear,\n        className: \"text-gray-400 hover:text-red-400 transition-colors\",\n        title: \"Supprimer\",\n        children: \"\\u2715\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-[60px] flex items-center justify-center\",\n      children: acceptedColumn ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3 p-3 bg-purple-600/30 rounded-lg border border-purple-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xl\",\n            children: acceptedColumn.type.toLowerCase().includes('int') || acceptedColumn.type.toLowerCase().includes('decimal') || acceptedColumn.type.toLowerCase().includes('float') ? '🔢' : acceptedColumn.type.toLowerCase().includes('date') ? '📅' : '📝'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-medium text-purple-200 truncate\",\n              children: acceptedColumn.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-purple-300\",\n              children: acceptedColumn.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 17\n            }, this), acceptedColumn.table && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-purple-400 mt-1\",\n              children: [\"\\uD83D\\uDCCB \", acceptedColumn.table]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 text-sm\",\n          children: isOver ? 'Relâchez ici' : 'Glissez une colonne ici'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), children]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour afficher les colonnes disponibles\n_s3(DropZone, \"fT702R7NW3L8KUJObOwGrnMsXMQ=\", false, function () {\n  return [useDroppable];\n});\n_c3 = DropZone;\nexport const ColumnsPanel = ({\n  columns,\n  title\n}) => {\n  if (!columns || columns.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-16 h-16 bg-gray-700 rounded-2xl mx-auto mb-4 flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-2xl\",\n          children: \"\\uD83D\\uDCCB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-400\",\n        children: \"Aucune colonne disponible\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-200\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DarkBadge, {\n        variant: \"info\",\n        children: columns.length\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3 max-h-[500px] overflow-y-auto\",\n      children: columns.map((column, index) => /*#__PURE__*/_jsxDEV(DraggableColumn, {\n        id: `column-${column.name}`,\n        column: column\n      }, `${column.name}-${index}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 257,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour les zones de drop organisées\n_c4 = ColumnsPanel;\nexport const DropZonesPanel = ({\n  xAxis,\n  yAxis,\n  legend,\n  values,\n  onClearX,\n  onClearY,\n  onClearLegend,\n  onClearValues\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n    children: [/*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"x-axis\",\n      title: \"Axe X\",\n      subtitle: \"Cat\\xE9gories ou groupes\",\n      icon: \"\\uD83D\\uDCCA\",\n      acceptedColumn: xAxis,\n      onClear: onClearX\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"y-axis\",\n      title: \"Axe Y\",\n      subtitle: \"Valeurs num\\xE9riques (optionnel)\",\n      icon: \"\\uD83D\\uDCC8\",\n      acceptedColumn: yAxis,\n      onClear: onClearY\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"legend\",\n      title: \"L\\xE9gende\",\n      subtitle: \"Sous-cat\\xE9gories (optionnel)\",\n      icon: \"\\uD83C\\uDFF7\\uFE0F\",\n      acceptedColumn: legend,\n      onClear: onClearLegend\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"values\",\n      title: \"Valeurs\",\n      subtitle: \"Donn\\xE9es \\xE0 agr\\xE9ger\",\n      icon: \"\\uD83D\\uDC8E\",\n      acceptedColumn: values,\n      onClear: onClearValues\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 288,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour une zone de drop compacte dans la barre fixe\n_c5 = DropZonesPanel;\nexport const CompactDropZone = ({\n  id,\n  title,\n  icon,\n  acceptedColumn,\n  onClear,\n  isOver\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n        flex items-center justify-between p-3 rounded-lg border-2 border-dashed min-h-[60px]\n        transition-all duration-300 ease-in-out transform\n        ${isOver ? 'border-purple-400 bg-purple-600/20 scale-105 drop-zone-glow drop-zone-hover' : 'border-gray-600 bg-gray-800/50 hover:border-gray-500 hover:bg-gray-800/70'}\n        ${acceptedColumn ? 'border-purple-500 bg-purple-600/30 shadow-lg shadow-purple-500/20' : ''}\n        backdrop-blur-sm hover:backdrop-blur-md\n      `,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2 flex-1 min-w-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: `text-lg transition-transform duration-200 ${isOver ? 'scale-110' : ''}`,\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-w-0 flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: `font-medium text-sm truncate transition-colors duration-200 ${isOver ? 'text-purple-200' : 'text-gray-200'}`,\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), acceptedColumn ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1 mt-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-purple-300 truncate font-medium\",\n            children: acceptedColumn.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 15\n          }, this), acceptedColumn.table && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-400\",\n            children: [\"(\", acceptedColumn.table, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-xs transition-colors duration-200 ${isOver ? 'text-purple-300' : 'text-gray-400'}`,\n          children: isOver ? 'Relâchez ici' : 'Glissez ici'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 7\n    }, this), acceptedColumn && onClear && /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onClear,\n      className: \"text-gray-400 hover:text-red-400 transition-all duration-200 ml-2 p-1 hover:bg-red-600/20 rounded hover:scale-110 active:scale-95\",\n      title: \"Supprimer\",\n      children: \"\\u2715\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 331,\n    columnNumber: 5\n  }, this);\n};\n\n// Barre fixe avec les zones de drop\n_c6 = CompactDropZone;\nexport const FixedDropBar = ({\n  xAxis,\n  yAxis,\n  legend,\n  values,\n  onClearX,\n  onClearY,\n  onClearLegend,\n  onClearValues,\n  isVisible = true,\n  position = 'bottom' // 'top' ou 'bottom'\n}) => {\n  _s4();\n  const {\n    isOver: isOverX,\n    setNodeRef: setNodeRefX\n  } = useDroppable({\n    id: 'x-axis-fixed',\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  const {\n    isOver: isOverY,\n    setNodeRef: setNodeRefY\n  } = useDroppable({\n    id: 'y-axis-fixed',\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  const {\n    isOver: isOverLegend,\n    setNodeRef: setNodeRefLegend\n  } = useDroppable({\n    id: 'legend-fixed',\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  const {\n    isOver: isOverValues,\n    setNodeRef: setNodeRefValues\n  } = useDroppable({\n    id: 'values-fixed',\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  if (!isVisible) return null;\n  const positionClasses = position === 'top' ? 'top-0 border-b' : 'bottom-0 border-t';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n      fixed left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-md ${positionClasses} border-gray-700\n      transition-all duration-300 ease-in-out\n      ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'}\n    `,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-6 py-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\uD83C\\uDFAF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-sm font-semibold text-gray-200\",\n            children: \"Zones de Drop\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs text-gray-400\",\n          children: \"Glissez vos colonnes ici pour cr\\xE9er votre visualisation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 lg:grid-cols-4 gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          ref: setNodeRefX,\n          children: /*#__PURE__*/_jsxDEV(CompactDropZone, {\n            id: \"x-axis-fixed\",\n            title: \"Axe X\",\n            icon: \"\\uD83D\\uDCCA\",\n            acceptedColumn: xAxis,\n            onClear: onClearX,\n            isOver: isOverX\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: setNodeRefY,\n          children: /*#__PURE__*/_jsxDEV(CompactDropZone, {\n            id: \"y-axis-fixed\",\n            title: \"Axe Y\",\n            icon: \"\\uD83D\\uDCC8\",\n            acceptedColumn: yAxis,\n            onClear: onClearY,\n            isOver: isOverY\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: setNodeRefLegend,\n          children: /*#__PURE__*/_jsxDEV(CompactDropZone, {\n            id: \"legend-fixed\",\n            title: \"L\\xE9gende\",\n            icon: \"\\uD83C\\uDFF7\\uFE0F\",\n            acceptedColumn: legend,\n            onClear: onClearLegend,\n            isOver: isOverLegend\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: setNodeRefValues,\n          children: /*#__PURE__*/_jsxDEV(CompactDropZone, {\n            id: \"values-fixed\",\n            title: \"Valeurs\",\n            icon: \"\\uD83D\\uDC8E\",\n            acceptedColumn: values,\n            onClear: onClearValues,\n            isOver: isOverValues\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 432,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 427,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour le sélecteur de fonction d'agrégation\n_s4(FixedDropBar, \"iDE4Z2x6jYrF9tSCiODbcpzg8qI=\", false, function () {\n  return [useDroppable, useDroppable, useDroppable, useDroppable];\n});\n_c7 = FixedDropBar;\nexport const AggregationSelector = ({\n  value,\n  onChange,\n  disabled\n}) => {\n  const aggregations = [{\n    value: 'SUM',\n    label: 'Somme',\n    icon: '➕',\n    description: 'Addition de toutes les valeurs'\n  }, {\n    value: 'AVG',\n    label: 'Moyenne',\n    icon: '📊',\n    description: 'Valeur moyenne'\n  }, {\n    value: 'COUNT',\n    label: 'Nombre',\n    icon: '🔢',\n    description: 'Nombre d\\'occurrences'\n  }, {\n    value: 'MIN',\n    label: 'Minimum',\n    icon: '⬇️',\n    description: 'Valeur minimale'\n  }, {\n    value: 'MAX',\n    label: 'Maximum',\n    icon: '⬆️',\n    description: 'Valeur maximale'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"block text-sm font-medium text-gray-300 mb-2\",\n      children: \"Fonction d'agr\\xE9gation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n      value: value,\n      onChange: e => onChange(e.target.value),\n      disabled: disabled,\n      className: \"w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-gray-100 focus:border-purple-500 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed\",\n      children: aggregations.map(agg => /*#__PURE__*/_jsxDEV(\"option\", {\n        value: agg.value,\n        children: [agg.icon, \" \", agg.label, \" - \", agg.description]\n      }, agg.value, true, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 504,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour le sélecteur de type de graphique\n_c8 = AggregationSelector;\nexport const ChartTypeSelector = ({\n  value,\n  onChange\n}) => {\n  const chartTypes = [{\n    value: 'bar',\n    label: 'Barres',\n    icon: '📊',\n    description: 'Graphique en barres'\n  }, {\n    value: 'line',\n    label: 'Ligne',\n    icon: '📈',\n    description: 'Graphique linéaire'\n  }, {\n    value: 'pie',\n    label: 'Circulaire',\n    icon: '🥧',\n    description: 'Graphique circulaire'\n  }, {\n    value: 'scatter',\n    label: 'Nuage',\n    icon: '⚫',\n    description: 'Nuage de points'\n  }, {\n    value: 'stacked_bar',\n    label: 'Barres empilées',\n    icon: '📚',\n    description: 'Barres empilées'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"block text-sm font-medium text-gray-300 mb-2\",\n      children: \"Type de graphique\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 md:grid-cols-3 gap-2\",\n      children: chartTypes.map(type => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onChange(type.value),\n        className: `p-3 rounded-lg border transition-colors text-center ${value === type.value ? 'border-purple-500 bg-purple-600/20 text-purple-300' : 'border-gray-700 bg-gray-800/50 text-gray-300 hover:border-gray-600'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg mb-1\",\n          children: type.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs font-medium\",\n          children: type.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 13\n        }, this)]\n      }, type.value, true, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 535,\n    columnNumber: 5\n  }, this);\n};\n_c9 = ChartTypeSelector;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"ColumnContextMenu\");\n$RefreshReg$(_c2, \"DraggableColumn\");\n$RefreshReg$(_c3, \"DropZone\");\n$RefreshReg$(_c4, \"ColumnsPanel\");\n$RefreshReg$(_c5, \"DropZonesPanel\");\n$RefreshReg$(_c6, \"CompactDropZone\");\n$RefreshReg$(_c7, \"FixedDropBar\");\n$RefreshReg$(_c8, \"AggregationSelector\");\n$RefreshReg$(_c9, \"ChartTypeSelector\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useDraggable", "useDroppable", "DarkBadge", "jsxDEV", "_jsxDEV", "ColumnContextMenu", "isOpen", "onClose", "position", "column", "onAddToAxis", "selectedTable", "_s", "menuRef", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "menuItems", "id", "label", "icon", "color", "ref", "className", "style", "left", "x", "top", "y", "min<PERSON><PERSON><PERSON>", "children", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "map", "item", "onClick", "columnWithTable", "table", "_c", "DraggableColumn", "_s2", "attributes", "listeners", "setNodeRef", "transform", "isDragging", "data", "opacity", "zIndex", "undefined", "getColumnIcon", "dataType", "toLowerCase", "includes", "getColumnColor", "_c2", "DropZone", "title", "subtitle", "acceptedColumn", "onClear", "_s3", "isOver", "accepts", "_c3", "ColumnsPanel", "columns", "length", "variant", "index", "_c4", "DropZonesPanel", "xAxis", "yAxis", "legend", "values", "onClearX", "onClearY", "onClearLegend", "onClearValues", "_c5", "CompactDropZone", "_c6", "FixedDropBar", "isVisible", "_s4", "isOverX", "setNodeRefX", "isOverY", "setNodeRefY", "isOverLegend", "setNodeRefLegend", "isOverValues", "setNodeRefValues", "positionClasses", "_c7", "AggregationSelector", "value", "onChange", "disabled", "aggregations", "description", "e", "agg", "_c8", "ChartTypeSelector", "chartTypes", "_c9", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/AIZekri/frontend/src/components/DragDropComponents.js"], "sourcesContent": ["/**\n * DragDropComponents - Composants pour l'interface drag & drop\n * Utilise @dnd-kit pour créer des colonnes draggables et des zones de drop\n */\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useDraggable, useDroppable } from '@dnd-kit/core';\nimport { DarkBadge } from './YellowMindUI';\n\n// Composant de menu contextuel pour les colonnes\nexport const ColumnContextMenu = ({\n  isOpen,\n  onClose,\n  position,\n  column,\n  onAddToAxis,\n  selectedTable\n}) => {\n  const menuRef = useRef(null);\n\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (menuRef.current && !menuRef.current.contains(event.target)) {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => document.removeEventListener('mousedown', handleClickOutside);\n    }\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  const menuItems = [\n    { id: 'x-axis', label: 'Ajouter à Axe X', icon: '📊', color: 'text-blue-400' },\n    { id: 'y-axis', label: 'Ajouter à Axe Y', icon: '📈', color: 'text-green-400' },\n    { id: 'legend', label: 'Ajouter à Légende', icon: '🏷️', color: 'text-yellow-400' },\n    { id: 'values', label: 'Ajouter aux Valeurs', icon: '💎', color: 'text-purple-400' }\n  ];\n\n  return (\n    <div\n      ref={menuRef}\n      className=\"fixed z-50 bg-gray-800 border border-gray-600 rounded-lg shadow-xl backdrop-blur-sm\"\n      style={{\n        left: position.x,\n        top: position.y,\n        minWidth: '200px'\n      }}\n    >\n      <div className=\"p-2\">\n        <div className=\"px-3 py-2 border-b border-gray-600 mb-2\">\n          <p className=\"text-sm font-medium text-gray-200\">{column.name}</p>\n          <p className=\"text-xs text-gray-400\">{column.type}</p>\n        </div>\n\n        {menuItems.map((item) => (\n          <button\n            key={item.id}\n            onClick={() => {\n              const columnWithTable = { ...column, table: selectedTable };\n              onAddToAxis(item.id, columnWithTable);\n              onClose();\n            }}\n            className={`\n              w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left\n              hover:bg-gray-700 transition-colors duration-200 group\n            `}\n          >\n            <span className=\"text-lg\">{item.icon}</span>\n            <span className={`text-sm ${item.color} group-hover:text-gray-100`}>\n              {item.label}\n            </span>\n          </button>\n        ))}\n      </div>\n    </div>\n  );\n};\n\n// Composant pour une colonne draggable\nexport const DraggableColumn = ({ column, id, onAddToAxis, selectedTable }) => {\n  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({\n    id: id,\n    data: {\n      column: column,\n      type: 'column'\n    }\n  });\n\n  const style = transform ? {\n    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,\n    opacity: isDragging ? 0.7 : 1,\n    zIndex: isDragging ? 1000 : 1\n  } : undefined;\n\n  const getColumnIcon = (type) => {\n    const dataType = type.toLowerCase();\n    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n      return '🔢';\n    }\n    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n      return '📝';\n    }\n    if (dataType.includes('date') || dataType.includes('time')) {\n      return '📅';\n    }\n    if (dataType.includes('bit') || dataType.includes('boolean')) {\n      return '☑️';\n    }\n    return '📊';\n  };\n\n  const getColumnColor = (type) => {\n    const dataType = type.toLowerCase();\n    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n      return 'bg-blue-600/20 border-blue-500 text-blue-300';\n    }\n    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n      return 'bg-green-600/20 border-green-500 text-green-300';\n    }\n    if (dataType.includes('date') || dataType.includes('time')) {\n      return 'bg-purple-600/20 border-purple-500 text-purple-300';\n    }\n    return 'bg-gray-600/20 border-gray-500 text-gray-300';\n  };\n\n  return (\n    <div\n      ref={setNodeRef}\n      style={style}\n      {...listeners}\n      {...attributes}\n      className={`\n        p-3 rounded-lg border-2 border-dashed cursor-grab active:cursor-grabbing\n        transition-all duration-300 ease-in-out transform hover:scale-105 hover:shadow-lg\n        ${getColumnColor(column.type)}\n        ${isDragging ? 'shadow-2xl column-drag-start rotate-3 scale-110' : 'hover:shadow-md hover:shadow-purple-500/10'}\n        group\n      `}\n    >\n      <div className=\"flex items-center space-x-2\">\n        <span className={`text-lg transition-transform duration-200 ${\n          isDragging ? 'scale-110' : 'group-hover:scale-105'\n        }`}>\n          {getColumnIcon(column.type)}\n        </span>\n        <div className=\"flex-1 min-w-0\">\n          <p className={`font-medium truncate transition-colors duration-200 ${\n            isDragging ? 'text-purple-200' : 'group-hover:text-gray-100'\n          }`}>\n            {column.name}\n          </p>\n          <p className={`text-xs transition-all duration-200 ${\n            isDragging ? 'opacity-90 text-purple-300' : 'opacity-75 group-hover:opacity-90'\n          }`}>\n            {column.type}\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Composant pour une zone de drop\nexport const DropZone = ({ id, title, subtitle, icon, children, acceptedColumn, onClear }) => {\n  const { isOver, setNodeRef } = useDroppable({\n    id: id,\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n\n  return (\n    <div\n      ref={setNodeRef}\n      className={`\n        p-6 rounded-lg border-2 border-dashed min-h-[140px] transition-all duration-200\n        ${isOver\n          ? 'border-purple-400 bg-purple-600/10 scale-105'\n          : 'border-gray-600 bg-gray-800/30'\n        }\n        ${acceptedColumn ? 'border-purple-500 bg-purple-600/20' : ''}\n      `}\n    >\n      {/* Header de la zone */}\n      <div className=\"flex items-center justify-between mb-3\">\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-lg\">{icon}</span>\n          <div>\n            <h4 className=\"font-medium text-gray-200\">{title}</h4>\n            {subtitle && <p className=\"text-xs text-gray-400\">{subtitle}</p>}\n          </div>\n        </div>\n        {acceptedColumn && onClear && (\n          <button\n            onClick={onClear}\n            className=\"text-gray-400 hover:text-red-400 transition-colors\"\n            title=\"Supprimer\"\n          >\n            ✕\n          </button>\n        )}\n      </div>\n\n      {/* Contenu de la zone */}\n      <div className=\"min-h-[60px] flex items-center justify-center\">\n        {acceptedColumn ? (\n          <div className=\"w-full\">\n            <div className=\"flex items-center space-x-3 p-3 bg-purple-600/30 rounded-lg border border-purple-500\">\n              <span className=\"text-xl\">\n                {acceptedColumn.type.toLowerCase().includes('int') ||\n                 acceptedColumn.type.toLowerCase().includes('decimal') ||\n                 acceptedColumn.type.toLowerCase().includes('float') ? '🔢' :\n                 acceptedColumn.type.toLowerCase().includes('date') ? '📅' : '📝'}\n              </span>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"font-medium text-purple-200 truncate\">{acceptedColumn.name}</p>\n                <p className=\"text-xs text-purple-300\">{acceptedColumn.type}</p>\n                {acceptedColumn.table && (\n                  <p className=\"text-xs text-purple-400 mt-1\">📋 {acceptedColumn.table}</p>\n                )}\n              </div>\n            </div>\n          </div>\n        ) : (\n          <div className=\"text-center\">\n            <p className=\"text-gray-500 text-sm\">\n              {isOver ? 'Relâchez ici' : 'Glissez une colonne ici'}\n            </p>\n          </div>\n        )}\n      </div>\n\n      {children}\n    </div>\n  );\n};\n\n// Composant pour afficher les colonnes disponibles\nexport const ColumnsPanel = ({ columns, title }) => {\n  if (!columns || columns.length === 0) {\n    return (\n      <div className=\"text-center py-8\">\n        <div className=\"w-16 h-16 bg-gray-700 rounded-2xl mx-auto mb-4 flex items-center justify-center\">\n          <span className=\"text-2xl\">📋</span>\n        </div>\n        <p className=\"text-gray-400\">Aucune colonne disponible</p>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-200\">{title}</h3>\n        <DarkBadge variant=\"info\">{columns.length}</DarkBadge>\n      </div>\n      \n      <div className=\"space-y-3 max-h-[500px] overflow-y-auto\">\n        {columns.map((column, index) => (\n          <DraggableColumn\n            key={`${column.name}-${index}`}\n            id={`column-${column.name}`}\n            column={column}\n          />\n        ))}\n      </div>\n    </div>\n  );\n};\n\n// Composant pour les zones de drop organisées\nexport const DropZonesPanel = ({\n  xAxis,\n  yAxis,\n  legend,\n  values,\n  onClearX,\n  onClearY,\n  onClearLegend,\n  onClearValues\n}) => {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n      <DropZone\n        id=\"x-axis\"\n        title=\"Axe X\"\n        subtitle=\"Catégories ou groupes\"\n        icon=\"📊\"\n        acceptedColumn={xAxis}\n        onClear={onClearX}\n      />\n\n      <DropZone\n        id=\"y-axis\"\n        title=\"Axe Y\"\n        subtitle=\"Valeurs numériques (optionnel)\"\n        icon=\"📈\"\n        acceptedColumn={yAxis}\n        onClear={onClearY}\n      />\n\n      <DropZone\n        id=\"legend\"\n        title=\"Légende\"\n        subtitle=\"Sous-catégories (optionnel)\"\n        icon=\"🏷️\"\n        acceptedColumn={legend}\n        onClear={onClearLegend}\n      />\n\n      <DropZone\n        id=\"values\"\n        title=\"Valeurs\"\n        subtitle=\"Données à agréger\"\n        icon=\"💎\"\n        acceptedColumn={values}\n        onClear={onClearValues}\n      />\n    </div>\n  );\n};\n\n// Composant pour une zone de drop compacte dans la barre fixe\nexport const CompactDropZone = ({ id, title, icon, acceptedColumn, onClear, isOver }) => {\n  return (\n    <div\n      className={`\n        flex items-center justify-between p-3 rounded-lg border-2 border-dashed min-h-[60px]\n        transition-all duration-300 ease-in-out transform\n        ${isOver\n          ? 'border-purple-400 bg-purple-600/20 scale-105 drop-zone-glow drop-zone-hover'\n          : 'border-gray-600 bg-gray-800/50 hover:border-gray-500 hover:bg-gray-800/70'\n        }\n        ${acceptedColumn ? 'border-purple-500 bg-purple-600/30 shadow-lg shadow-purple-500/20' : ''}\n        backdrop-blur-sm hover:backdrop-blur-md\n      `}\n    >\n      <div className=\"flex items-center space-x-2 flex-1 min-w-0\">\n        <span className={`text-lg transition-transform duration-200 ${isOver ? 'scale-110' : ''}`}>\n          {icon}\n        </span>\n        <div className=\"min-w-0 flex-1\">\n          <h4 className={`font-medium text-sm truncate transition-colors duration-200 ${\n            isOver ? 'text-purple-200' : 'text-gray-200'\n          }`}>\n            {title}\n          </h4>\n          {acceptedColumn ? (\n            <div className=\"flex items-center space-x-1 mt-1\">\n              <span className=\"text-xs text-purple-300 truncate font-medium\">\n                {acceptedColumn.name}\n              </span>\n              {acceptedColumn.table && (\n                <span className=\"text-xs text-gray-400\">\n                  ({acceptedColumn.table})\n                </span>\n              )}\n            </div>\n          ) : (\n            <p className={`text-xs transition-colors duration-200 ${\n              isOver ? 'text-purple-300' : 'text-gray-400'\n            }`}>\n              {isOver ? 'Relâchez ici' : 'Glissez ici'}\n            </p>\n          )}\n        </div>\n      </div>\n      {acceptedColumn && onClear && (\n        <button\n          onClick={onClear}\n          className=\"text-gray-400 hover:text-red-400 transition-all duration-200 ml-2 p-1\n                     hover:bg-red-600/20 rounded hover:scale-110 active:scale-95\"\n          title=\"Supprimer\"\n        >\n          ✕\n        </button>\n      )}\n    </div>\n  );\n};\n\n// Barre fixe avec les zones de drop\nexport const FixedDropBar = ({\n  xAxis,\n  yAxis,\n  legend,\n  values,\n  onClearX,\n  onClearY,\n  onClearLegend,\n  onClearValues,\n  isVisible = true,\n  position = 'bottom' // 'top' ou 'bottom'\n}) => {\n  const { isOver: isOverX, setNodeRef: setNodeRefX } = useDroppable({\n    id: 'x-axis-fixed',\n    data: { type: 'dropzone', accepts: ['column'] }\n  });\n\n  const { isOver: isOverY, setNodeRef: setNodeRefY } = useDroppable({\n    id: 'y-axis-fixed',\n    data: { type: 'dropzone', accepts: ['column'] }\n  });\n\n  const { isOver: isOverLegend, setNodeRef: setNodeRefLegend } = useDroppable({\n    id: 'legend-fixed',\n    data: { type: 'dropzone', accepts: ['column'] }\n  });\n\n  const { isOver: isOverValues, setNodeRef: setNodeRefValues } = useDroppable({\n    id: 'values-fixed',\n    data: { type: 'dropzone', accepts: ['column'] }\n  });\n\n  if (!isVisible) return null;\n\n  const positionClasses = position === 'top'\n    ? 'top-0 border-b'\n    : 'bottom-0 border-t';\n\n  return (\n    <div className={`\n      fixed left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-md ${positionClasses} border-gray-700\n      transition-all duration-300 ease-in-out\n      ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'}\n    `}>\n      <div className=\"max-w-7xl mx-auto px-6 py-4\">\n        <div className=\"flex items-center justify-between mb-3\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-lg\">🎯</span>\n            <h3 className=\"text-sm font-semibold text-gray-200\">Zones de Drop</h3>\n          </div>\n          <div className=\"text-xs text-gray-400\">\n            Glissez vos colonnes ici pour créer votre visualisation\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-3\">\n          <div ref={setNodeRefX}>\n            <CompactDropZone\n              id=\"x-axis-fixed\"\n              title=\"Axe X\"\n              icon=\"📊\"\n              acceptedColumn={xAxis}\n              onClear={onClearX}\n              isOver={isOverX}\n            />\n          </div>\n\n          <div ref={setNodeRefY}>\n            <CompactDropZone\n              id=\"y-axis-fixed\"\n              title=\"Axe Y\"\n              icon=\"📈\"\n              acceptedColumn={yAxis}\n              onClear={onClearY}\n              isOver={isOverY}\n            />\n          </div>\n\n          <div ref={setNodeRefLegend}>\n            <CompactDropZone\n              id=\"legend-fixed\"\n              title=\"Légende\"\n              icon=\"🏷️\"\n              acceptedColumn={legend}\n              onClear={onClearLegend}\n              isOver={isOverLegend}\n            />\n          </div>\n\n          <div ref={setNodeRefValues}>\n            <CompactDropZone\n              id=\"values-fixed\"\n              title=\"Valeurs\"\n              icon=\"💎\"\n              acceptedColumn={values}\n              onClear={onClearValues}\n              isOver={isOverValues}\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Composant pour le sélecteur de fonction d'agrégation\nexport const AggregationSelector = ({ value, onChange, disabled }) => {\n  const aggregations = [\n    { value: 'SUM', label: 'Somme', icon: '➕', description: 'Addition de toutes les valeurs' },\n    { value: 'AVG', label: 'Moyenne', icon: '📊', description: 'Valeur moyenne' },\n    { value: 'COUNT', label: 'Nombre', icon: '🔢', description: 'Nombre d\\'occurrences' },\n    { value: 'MIN', label: 'Minimum', icon: '⬇️', description: 'Valeur minimale' },\n    { value: 'MAX', label: 'Maximum', icon: '⬆️', description: 'Valeur maximale' }\n  ];\n\n  return (\n    <div>\n      <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n        Fonction d'agrégation\n      </label>\n      <select\n        value={value}\n        onChange={(e) => onChange(e.target.value)}\n        disabled={disabled}\n        className=\"w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-gray-100 focus:border-purple-500 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed\"\n      >\n        {aggregations.map((agg) => (\n          <option key={agg.value} value={agg.value}>\n            {agg.icon} {agg.label} - {agg.description}\n          </option>\n        ))}\n      </select>\n    </div>\n  );\n};\n\n// Composant pour le sélecteur de type de graphique\nexport const ChartTypeSelector = ({ value, onChange }) => {\n  const chartTypes = [\n    { value: 'bar', label: 'Barres', icon: '📊', description: 'Graphique en barres' },\n    { value: 'line', label: 'Ligne', icon: '📈', description: 'Graphique linéaire' },\n    { value: 'pie', label: 'Circulaire', icon: '🥧', description: 'Graphique circulaire' },\n    { value: 'scatter', label: 'Nuage', icon: '⚫', description: 'Nuage de points' },\n    { value: 'stacked_bar', label: 'Barres empilées', icon: '📚', description: 'Barres empilées' }\n  ];\n\n  return (\n    <div>\n      <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n        Type de graphique\n      </label>\n      <div className=\"grid grid-cols-2 md:grid-cols-3 gap-2\">\n        {chartTypes.map((type) => (\n          <button\n            key={type.value}\n            onClick={() => onChange(type.value)}\n            className={`p-3 rounded-lg border transition-colors text-center ${\n              value === type.value\n                ? 'border-purple-500 bg-purple-600/20 text-purple-300'\n                : 'border-gray-700 bg-gray-800/50 text-gray-300 hover:border-gray-600'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">{type.icon}</div>\n            <div className=\"text-xs font-medium\">{type.label}</div>\n          </button>\n        ))}\n      </div>\n    </div>\n  );\n};\n"], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,YAAY,EAAEC,YAAY,QAAQ,eAAe;AAC1D,SAASC,SAAS,QAAQ,gBAAgB;;AAE1C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAAC;EAChCC,MAAM;EACNC,OAAO;EACPC,QAAQ;EACRC,MAAM;EACNC,WAAW;EACXC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,OAAO,GAAGf,MAAM,CAAC,IAAI,CAAC;EAE5BC,SAAS,CAAC,MAAM;IACd,MAAMe,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIF,OAAO,CAACG,OAAO,IAAI,CAACH,OAAO,CAACG,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QAC9DX,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAED,IAAID,MAAM,EAAE;MACVa,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;MAC1D,OAAO,MAAMK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC5E;EACF,CAAC,EAAE,CAACR,MAAM,EAAEC,OAAO,CAAC,CAAC;EAErB,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMgB,SAAS,GAAG,CAChB;IAAEC,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAC9E;IAAEH,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAiB,CAAC,EAC/E;IAAEH,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,mBAAmB;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAkB,CAAC,EACnF;IAAEH,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,qBAAqB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAkB,CAAC,CACrF;EAED,oBACEtB,OAAA;IACEuB,GAAG,EAAEd,OAAQ;IACbe,SAAS,EAAC,qFAAqF;IAC/FC,KAAK,EAAE;MACLC,IAAI,EAAEtB,QAAQ,CAACuB,CAAC;MAChBC,GAAG,EAAExB,QAAQ,CAACyB,CAAC;MACfC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,eAEF/B,OAAA;MAAKwB,SAAS,EAAC,KAAK;MAAAO,QAAA,gBAClB/B,OAAA;QAAKwB,SAAS,EAAC,yCAAyC;QAAAO,QAAA,gBACtD/B,OAAA;UAAGwB,SAAS,EAAC,mCAAmC;UAAAO,QAAA,EAAE1B,MAAM,CAAC2B;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClEpC,OAAA;UAAGwB,SAAS,EAAC,uBAAuB;UAAAO,QAAA,EAAE1B,MAAM,CAACgC;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,EAELlB,SAAS,CAACoB,GAAG,CAAEC,IAAI,iBAClBvC,OAAA;QAEEwC,OAAO,EAAEA,CAAA,KAAM;UACb,MAAMC,eAAe,GAAG;YAAE,GAAGpC,MAAM;YAAEqC,KAAK,EAAEnC;UAAc,CAAC;UAC3DD,WAAW,CAACiC,IAAI,CAACpB,EAAE,EAAEsB,eAAe,CAAC;UACrCtC,OAAO,CAAC,CAAC;QACX,CAAE;QACFqB,SAAS,EAAE;AACvB;AACA;AACA,aAAc;QAAAO,QAAA,gBAEF/B,OAAA;UAAMwB,SAAS,EAAC,SAAS;UAAAO,QAAA,EAAEQ,IAAI,CAAClB;QAAI;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5CpC,OAAA;UAAMwB,SAAS,EAAE,WAAWe,IAAI,CAACjB,KAAK,4BAA6B;UAAAS,QAAA,EAChEQ,IAAI,CAACnB;QAAK;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA,GAdFG,IAAI,CAACpB,EAAE;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAeN,CACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA5B,EAAA,CAxEaP,iBAAiB;AAAA0C,EAAA,GAAjB1C,iBAAiB;AAyE9B,OAAO,MAAM2C,eAAe,GAAGA,CAAC;EAAEvC,MAAM;EAAEc,EAAE;EAAEb,WAAW;EAAEC;AAAc,CAAC,KAAK;EAAAsC,GAAA;EAC7E,MAAM;IAAEC,UAAU;IAAEC,SAAS;IAAEC,UAAU;IAAEC,SAAS;IAAEC;EAAW,CAAC,GAAGtD,YAAY,CAAC;IAChFuB,EAAE,EAAEA,EAAE;IACNgC,IAAI,EAAE;MACJ9C,MAAM,EAAEA,MAAM;MACdgC,IAAI,EAAE;IACR;EACF,CAAC,CAAC;EAEF,MAAMZ,KAAK,GAAGwB,SAAS,GAAG;IACxBA,SAAS,EAAE,eAAeA,SAAS,CAACtB,CAAC,OAAOsB,SAAS,CAACpB,CAAC,QAAQ;IAC/DuB,OAAO,EAAEF,UAAU,GAAG,GAAG,GAAG,CAAC;IAC7BG,MAAM,EAAEH,UAAU,GAAG,IAAI,GAAG;EAC9B,CAAC,GAAGI,SAAS;EAEb,MAAMC,aAAa,GAAIlB,IAAI,IAAK;IAC9B,MAAMmB,QAAQ,GAAGnB,IAAI,CAACoB,WAAW,CAAC,CAAC;IACnC,IAAID,QAAQ,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MACxH,OAAO,IAAI;IACb;IACA,IAAIF,QAAQ,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1F,OAAO,IAAI;IACb;IACA,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1D,OAAO,IAAI;IACb;IACA,IAAIF,QAAQ,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;MAC5D,OAAO,IAAI;IACb;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMC,cAAc,GAAItB,IAAI,IAAK;IAC/B,MAAMmB,QAAQ,GAAGnB,IAAI,CAACoB,WAAW,CAAC,CAAC;IACnC,IAAID,QAAQ,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MACxH,OAAO,8CAA8C;IACvD;IACA,IAAIF,QAAQ,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1F,OAAO,iDAAiD;IAC1D;IACA,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1D,OAAO,oDAAoD;IAC7D;IACA,OAAO,8CAA8C;EACvD,CAAC;EAED,oBACE1D,OAAA;IACEuB,GAAG,EAAEyB,UAAW;IAChBvB,KAAK,EAAEA,KAAM;IAAA,GACTsB,SAAS;IAAA,GACTD,UAAU;IACdtB,SAAS,EAAE;AACjB;AACA;AACA,UAAUmC,cAAc,CAACtD,MAAM,CAACgC,IAAI,CAAC;AACrC,UAAUa,UAAU,GAAG,iDAAiD,GAAG,4CAA4C;AACvH;AACA,OAAQ;IAAAnB,QAAA,eAEF/B,OAAA;MAAKwB,SAAS,EAAC,6BAA6B;MAAAO,QAAA,gBAC1C/B,OAAA;QAAMwB,SAAS,EAAE,6CACf0B,UAAU,GAAG,WAAW,GAAG,uBAAuB,EACjD;QAAAnB,QAAA,EACAwB,aAAa,CAAClD,MAAM,CAACgC,IAAI;MAAC;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eACPpC,OAAA;QAAKwB,SAAS,EAAC,gBAAgB;QAAAO,QAAA,gBAC7B/B,OAAA;UAAGwB,SAAS,EAAE,uDACZ0B,UAAU,GAAG,iBAAiB,GAAG,2BAA2B,EAC3D;UAAAnB,QAAA,EACA1B,MAAM,CAAC2B;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACJpC,OAAA;UAAGwB,SAAS,EAAE,uCACZ0B,UAAU,GAAG,4BAA4B,GAAG,mCAAmC,EAC9E;UAAAnB,QAAA,EACA1B,MAAM,CAACgC;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAS,GAAA,CAnFaD,eAAe;EAAA,QAC2ChD,YAAY;AAAA;AAAAgE,GAAA,GADtEhB,eAAe;AAoF5B,OAAO,MAAMiB,QAAQ,GAAGA,CAAC;EAAE1C,EAAE;EAAE2C,KAAK;EAAEC,QAAQ;EAAE1C,IAAI;EAAEU,QAAQ;EAAEiC,cAAc;EAAEC;AAAQ,CAAC,KAAK;EAAAC,GAAA;EAC5F,MAAM;IAAEC,MAAM;IAAEnB;EAAW,CAAC,GAAGnD,YAAY,CAAC;IAC1CsB,EAAE,EAAEA,EAAE;IACNgC,IAAI,EAAE;MACJd,IAAI,EAAE,UAAU;MAChB+B,OAAO,EAAE,CAAC,QAAQ;IACpB;EACF,CAAC,CAAC;EAEF,oBACEpE,OAAA;IACEuB,GAAG,EAAEyB,UAAW;IAChBxB,SAAS,EAAE;AACjB;AACA,UAAU2C,MAAM,GACJ,8CAA8C,GAC9C,gCAAgC;AAC5C,UACUH,cAAc,GAAG,oCAAoC,GAAG,EAAE;AACpE,OAAQ;IAAAjC,QAAA,gBAGF/B,OAAA;MAAKwB,SAAS,EAAC,wCAAwC;MAAAO,QAAA,gBACrD/B,OAAA;QAAKwB,SAAS,EAAC,6BAA6B;QAAAO,QAAA,gBAC1C/B,OAAA;UAAMwB,SAAS,EAAC,SAAS;UAAAO,QAAA,EAAEV;QAAI;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvCpC,OAAA;UAAA+B,QAAA,gBACE/B,OAAA;YAAIwB,SAAS,EAAC,2BAA2B;YAAAO,QAAA,EAAE+B;UAAK;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EACrD2B,QAAQ,iBAAI/D,OAAA;YAAGwB,SAAS,EAAC,uBAAuB;YAAAO,QAAA,EAAEgC;UAAQ;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACL4B,cAAc,IAAIC,OAAO,iBACxBjE,OAAA;QACEwC,OAAO,EAAEyB,OAAQ;QACjBzC,SAAS,EAAC,oDAAoD;QAC9DsC,KAAK,EAAC,WAAW;QAAA/B,QAAA,EAClB;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNpC,OAAA;MAAKwB,SAAS,EAAC,+CAA+C;MAAAO,QAAA,EAC3DiC,cAAc,gBACbhE,OAAA;QAAKwB,SAAS,EAAC,QAAQ;QAAAO,QAAA,eACrB/B,OAAA;UAAKwB,SAAS,EAAC,sFAAsF;UAAAO,QAAA,gBACnG/B,OAAA;YAAMwB,SAAS,EAAC,SAAS;YAAAO,QAAA,EACtBiC,cAAc,CAAC3B,IAAI,CAACoB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,IACjDM,cAAc,CAAC3B,IAAI,CAACoB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,SAAS,CAAC,IACrDM,cAAc,CAAC3B,IAAI,CAACoB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,GAAG,IAAI,GAC1DM,cAAc,CAAC3B,IAAI,CAACoB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG;UAAI;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACPpC,OAAA;YAAKwB,SAAS,EAAC,gBAAgB;YAAAO,QAAA,gBAC7B/B,OAAA;cAAGwB,SAAS,EAAC,sCAAsC;cAAAO,QAAA,EAAEiC,cAAc,CAAChC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7EpC,OAAA;cAAGwB,SAAS,EAAC,yBAAyB;cAAAO,QAAA,EAAEiC,cAAc,CAAC3B;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC/D4B,cAAc,CAACtB,KAAK,iBACnB1C,OAAA;cAAGwB,SAAS,EAAC,8BAA8B;cAAAO,QAAA,GAAC,eAAG,EAACiC,cAAc,CAACtB,KAAK;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACzE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAENpC,OAAA;QAAKwB,SAAS,EAAC,aAAa;QAAAO,QAAA,eAC1B/B,OAAA;UAAGwB,SAAS,EAAC,uBAAuB;UAAAO,QAAA,EACjCoC,MAAM,GAAG,cAAc,GAAG;QAAyB;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELL,QAAQ;EAAA;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AAAA8B,GAAA,CA3EaL,QAAQ;EAAA,QACYhE,YAAY;AAAA;AAAAwE,GAAA,GADhCR,QAAQ;AA4ErB,OAAO,MAAMS,YAAY,GAAGA,CAAC;EAAEC,OAAO;EAAET;AAAM,CAAC,KAAK;EAClD,IAAI,CAACS,OAAO,IAAIA,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;IACpC,oBACExE,OAAA;MAAKwB,SAAS,EAAC,kBAAkB;MAAAO,QAAA,gBAC/B/B,OAAA;QAAKwB,SAAS,EAAC,iFAAiF;QAAAO,QAAA,eAC9F/B,OAAA;UAAMwB,SAAS,EAAC,UAAU;UAAAO,QAAA,EAAC;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACNpC,OAAA;QAAGwB,SAAS,EAAC,eAAe;QAAAO,QAAA,EAAC;MAAyB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD,CAAC;EAEV;EAEA,oBACEpC,OAAA;IAAA+B,QAAA,gBACE/B,OAAA;MAAKwB,SAAS,EAAC,wCAAwC;MAAAO,QAAA,gBACrD/B,OAAA;QAAIwB,SAAS,EAAC,qCAAqC;QAAAO,QAAA,EAAE+B;MAAK;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAChEpC,OAAA,CAACF,SAAS;QAAC2E,OAAO,EAAC,MAAM;QAAA1C,QAAA,EAAEwC,OAAO,CAACC;MAAM;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC,eAENpC,OAAA;MAAKwB,SAAS,EAAC,yCAAyC;MAAAO,QAAA,EACrDwC,OAAO,CAACjC,GAAG,CAAC,CAACjC,MAAM,EAAEqE,KAAK,kBACzB1E,OAAA,CAAC4C,eAAe;QAEdzB,EAAE,EAAE,UAAUd,MAAM,CAAC2B,IAAI,EAAG;QAC5B3B,MAAM,EAAEA;MAAO,GAFV,GAAGA,MAAM,CAAC2B,IAAI,IAAI0C,KAAK,EAAE;QAAAzC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAG/B,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAuC,GAAA,GAhCaL,YAAY;AAiCzB,OAAO,MAAMM,cAAc,GAAGA,CAAC;EAC7BC,KAAK;EACLC,KAAK;EACLC,MAAM;EACNC,MAAM;EACNC,QAAQ;EACRC,QAAQ;EACRC,aAAa;EACbC;AACF,CAAC,KAAK;EACJ,oBACEpF,OAAA;IAAKwB,SAAS,EAAC,uCAAuC;IAAAO,QAAA,gBACpD/B,OAAA,CAAC6D,QAAQ;MACP1C,EAAE,EAAC,QAAQ;MACX2C,KAAK,EAAC,OAAO;MACbC,QAAQ,EAAC,0BAAuB;MAChC1C,IAAI,EAAC,cAAI;MACT2C,cAAc,EAAEa,KAAM;MACtBZ,OAAO,EAAEgB;IAAS;MAAAhD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eAEFpC,OAAA,CAAC6D,QAAQ;MACP1C,EAAE,EAAC,QAAQ;MACX2C,KAAK,EAAC,OAAO;MACbC,QAAQ,EAAC,mCAAgC;MACzC1C,IAAI,EAAC,cAAI;MACT2C,cAAc,EAAEc,KAAM;MACtBb,OAAO,EAAEiB;IAAS;MAAAjD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eAEFpC,OAAA,CAAC6D,QAAQ;MACP1C,EAAE,EAAC,QAAQ;MACX2C,KAAK,EAAC,YAAS;MACfC,QAAQ,EAAC,gCAA6B;MACtC1C,IAAI,EAAC,oBAAK;MACV2C,cAAc,EAAEe,MAAO;MACvBd,OAAO,EAAEkB;IAAc;MAAAlD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAEFpC,OAAA,CAAC6D,QAAQ;MACP1C,EAAE,EAAC,QAAQ;MACX2C,KAAK,EAAC,SAAS;MACfC,QAAQ,EAAC,4BAAmB;MAC5B1C,IAAI,EAAC,cAAI;MACT2C,cAAc,EAAEgB,MAAO;MACvBf,OAAO,EAAEmB;IAAc;MAAAnD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;;AAED;AAAAiD,GAAA,GAnDaT,cAAc;AAoD3B,OAAO,MAAMU,eAAe,GAAGA,CAAC;EAAEnE,EAAE;EAAE2C,KAAK;EAAEzC,IAAI;EAAE2C,cAAc;EAAEC,OAAO;EAAEE;AAAO,CAAC,KAAK;EACvF,oBACEnE,OAAA;IACEwB,SAAS,EAAE;AACjB;AACA;AACA,UAAU2C,MAAM,GACJ,6EAA6E,GAC7E,2EAA2E;AACvF,UACUH,cAAc,GAAG,mEAAmE,GAAG,EAAE;AACnG;AACA,OAAQ;IAAAjC,QAAA,gBAEF/B,OAAA;MAAKwB,SAAS,EAAC,4CAA4C;MAAAO,QAAA,gBACzD/B,OAAA;QAAMwB,SAAS,EAAE,6CAA6C2C,MAAM,GAAG,WAAW,GAAG,EAAE,EAAG;QAAApC,QAAA,EACvFV;MAAI;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACPpC,OAAA;QAAKwB,SAAS,EAAC,gBAAgB;QAAAO,QAAA,gBAC7B/B,OAAA;UAAIwB,SAAS,EAAE,+DACb2C,MAAM,GAAG,iBAAiB,GAAG,eAAe,EAC3C;UAAApC,QAAA,EACA+B;QAAK;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EACJ4B,cAAc,gBACbhE,OAAA;UAAKwB,SAAS,EAAC,kCAAkC;UAAAO,QAAA,gBAC/C/B,OAAA;YAAMwB,SAAS,EAAC,8CAA8C;YAAAO,QAAA,EAC3DiC,cAAc,CAAChC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,EACN4B,cAAc,CAACtB,KAAK,iBACnB1C,OAAA;YAAMwB,SAAS,EAAC,uBAAuB;YAAAO,QAAA,GAAC,GACrC,EAACiC,cAAc,CAACtB,KAAK,EAAC,GACzB;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAENpC,OAAA;UAAGwB,SAAS,EAAE,0CACZ2C,MAAM,GAAG,iBAAiB,GAAG,eAAe,EAC3C;UAAApC,QAAA,EACAoC,MAAM,GAAG,cAAc,GAAG;QAAa;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EACL4B,cAAc,IAAIC,OAAO,iBACxBjE,OAAA;MACEwC,OAAO,EAAEyB,OAAQ;MACjBzC,SAAS,EAAC,mIAC6D;MACvEsC,KAAK,EAAC,WAAW;MAAA/B,QAAA,EAClB;IAED;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CACT;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAmD,GAAA,GA1DaD,eAAe;AA2D5B,OAAO,MAAME,YAAY,GAAGA,CAAC;EAC3BX,KAAK;EACLC,KAAK;EACLC,MAAM;EACNC,MAAM;EACNC,QAAQ;EACRC,QAAQ;EACRC,aAAa;EACbC,aAAa;EACbK,SAAS,GAAG,IAAI;EAChBrF,QAAQ,GAAG,QAAQ,CAAC;AACtB,CAAC,KAAK;EAAAsF,GAAA;EACJ,MAAM;IAAEvB,MAAM,EAAEwB,OAAO;IAAE3C,UAAU,EAAE4C;EAAY,CAAC,GAAG/F,YAAY,CAAC;IAChEsB,EAAE,EAAE,cAAc;IAClBgC,IAAI,EAAE;MAAEd,IAAI,EAAE,UAAU;MAAE+B,OAAO,EAAE,CAAC,QAAQ;IAAE;EAChD,CAAC,CAAC;EAEF,MAAM;IAAED,MAAM,EAAE0B,OAAO;IAAE7C,UAAU,EAAE8C;EAAY,CAAC,GAAGjG,YAAY,CAAC;IAChEsB,EAAE,EAAE,cAAc;IAClBgC,IAAI,EAAE;MAAEd,IAAI,EAAE,UAAU;MAAE+B,OAAO,EAAE,CAAC,QAAQ;IAAE;EAChD,CAAC,CAAC;EAEF,MAAM;IAAED,MAAM,EAAE4B,YAAY;IAAE/C,UAAU,EAAEgD;EAAiB,CAAC,GAAGnG,YAAY,CAAC;IAC1EsB,EAAE,EAAE,cAAc;IAClBgC,IAAI,EAAE;MAAEd,IAAI,EAAE,UAAU;MAAE+B,OAAO,EAAE,CAAC,QAAQ;IAAE;EAChD,CAAC,CAAC;EAEF,MAAM;IAAED,MAAM,EAAE8B,YAAY;IAAEjD,UAAU,EAAEkD;EAAiB,CAAC,GAAGrG,YAAY,CAAC;IAC1EsB,EAAE,EAAE,cAAc;IAClBgC,IAAI,EAAE;MAAEd,IAAI,EAAE,UAAU;MAAE+B,OAAO,EAAE,CAAC,QAAQ;IAAE;EAChD,CAAC,CAAC;EAEF,IAAI,CAACqB,SAAS,EAAE,OAAO,IAAI;EAE3B,MAAMU,eAAe,GAAG/F,QAAQ,KAAK,KAAK,GACtC,gBAAgB,GAChB,mBAAmB;EAEvB,oBACEJ,OAAA;IAAKwB,SAAS,EAAE;AACpB,kEAAkE2E,eAAe;AACjF;AACA,QAAQV,SAAS,GAAG,2BAA2B,GAAG,4BAA4B;AAC9E,KAAM;IAAA1D,QAAA,eACA/B,OAAA;MAAKwB,SAAS,EAAC,6BAA6B;MAAAO,QAAA,gBAC1C/B,OAAA;QAAKwB,SAAS,EAAC,wCAAwC;QAAAO,QAAA,gBACrD/B,OAAA;UAAKwB,SAAS,EAAC,6BAA6B;UAAAO,QAAA,gBAC1C/B,OAAA;YAAMwB,SAAS,EAAC,SAAS;YAAAO,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnCpC,OAAA;YAAIwB,SAAS,EAAC,qCAAqC;YAAAO,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACNpC,OAAA;UAAKwB,SAAS,EAAC,uBAAuB;UAAAO,QAAA,EAAC;QAEvC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpC,OAAA;QAAKwB,SAAS,EAAC,uCAAuC;QAAAO,QAAA,gBACpD/B,OAAA;UAAKuB,GAAG,EAAEqE,WAAY;UAAA7D,QAAA,eACpB/B,OAAA,CAACsF,eAAe;YACdnE,EAAE,EAAC,cAAc;YACjB2C,KAAK,EAAC,OAAO;YACbzC,IAAI,EAAC,cAAI;YACT2C,cAAc,EAAEa,KAAM;YACtBZ,OAAO,EAAEgB,QAAS;YAClBd,MAAM,EAAEwB;UAAQ;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpC,OAAA;UAAKuB,GAAG,EAAEuE,WAAY;UAAA/D,QAAA,eACpB/B,OAAA,CAACsF,eAAe;YACdnE,EAAE,EAAC,cAAc;YACjB2C,KAAK,EAAC,OAAO;YACbzC,IAAI,EAAC,cAAI;YACT2C,cAAc,EAAEc,KAAM;YACtBb,OAAO,EAAEiB,QAAS;YAClBf,MAAM,EAAE0B;UAAQ;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpC,OAAA;UAAKuB,GAAG,EAAEyE,gBAAiB;UAAAjE,QAAA,eACzB/B,OAAA,CAACsF,eAAe;YACdnE,EAAE,EAAC,cAAc;YACjB2C,KAAK,EAAC,YAAS;YACfzC,IAAI,EAAC,oBAAK;YACV2C,cAAc,EAAEe,MAAO;YACvBd,OAAO,EAAEkB,aAAc;YACvBhB,MAAM,EAAE4B;UAAa;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpC,OAAA;UAAKuB,GAAG,EAAE2E,gBAAiB;UAAAnE,QAAA,eACzB/B,OAAA,CAACsF,eAAe;YACdnE,EAAE,EAAC,cAAc;YACjB2C,KAAK,EAAC,SAAS;YACfzC,IAAI,EAAC,cAAI;YACT2C,cAAc,EAAEgB,MAAO;YACvBf,OAAO,EAAEmB,aAAc;YACvBjB,MAAM,EAAE8B;UAAa;YAAAhE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAsD,GAAA,CAzGaF,YAAY;EAAA,QAY8B3F,YAAY,EAKZA,YAAY,EAKFA,YAAY,EAKZA,YAAY;AAAA;AAAAuG,GAAA,GA3BhEZ,YAAY;AA0GzB,OAAO,MAAMa,mBAAmB,GAAGA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EACpE,MAAMC,YAAY,GAAG,CACnB;IAAEH,KAAK,EAAE,KAAK;IAAElF,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,GAAG;IAAEqF,WAAW,EAAE;EAAiC,CAAC,EAC1F;IAAEJ,KAAK,EAAE,KAAK;IAAElF,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,IAAI;IAAEqF,WAAW,EAAE;EAAiB,CAAC,EAC7E;IAAEJ,KAAK,EAAE,OAAO;IAAElF,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE,IAAI;IAAEqF,WAAW,EAAE;EAAwB,CAAC,EACrF;IAAEJ,KAAK,EAAE,KAAK;IAAElF,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,IAAI;IAAEqF,WAAW,EAAE;EAAkB,CAAC,EAC9E;IAAEJ,KAAK,EAAE,KAAK;IAAElF,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,IAAI;IAAEqF,WAAW,EAAE;EAAkB,CAAC,CAC/E;EAED,oBACE1G,OAAA;IAAA+B,QAAA,gBACE/B,OAAA;MAAOwB,SAAS,EAAC,8CAA8C;MAAAO,QAAA,EAAC;IAEhE;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACRpC,OAAA;MACEsG,KAAK,EAAEA,KAAM;MACbC,QAAQ,EAAGI,CAAC,IAAKJ,QAAQ,CAACI,CAAC,CAAC7F,MAAM,CAACwF,KAAK,CAAE;MAC1CE,QAAQ,EAAEA,QAAS;MACnBhF,SAAS,EAAC,mKAAmK;MAAAO,QAAA,EAE5K0E,YAAY,CAACnE,GAAG,CAAEsE,GAAG,iBACpB5G,OAAA;QAAwBsG,KAAK,EAAEM,GAAG,CAACN,KAAM;QAAAvE,QAAA,GACtC6E,GAAG,CAACvF,IAAI,EAAC,GAAC,EAACuF,GAAG,CAACxF,KAAK,EAAC,KAAG,EAACwF,GAAG,CAACF,WAAW;MAAA,GAD9BE,GAAG,CAACN,KAAK;QAAArE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEd,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AAAAyE,GAAA,GA9BaR,mBAAmB;AA+BhC,OAAO,MAAMS,iBAAiB,GAAGA,CAAC;EAAER,KAAK;EAAEC;AAAS,CAAC,KAAK;EACxD,MAAMQ,UAAU,GAAG,CACjB;IAAET,KAAK,EAAE,KAAK;IAAElF,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE,IAAI;IAAEqF,WAAW,EAAE;EAAsB,CAAC,EACjF;IAAEJ,KAAK,EAAE,MAAM;IAAElF,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,IAAI;IAAEqF,WAAW,EAAE;EAAqB,CAAC,EAChF;IAAEJ,KAAK,EAAE,KAAK;IAAElF,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE,IAAI;IAAEqF,WAAW,EAAE;EAAuB,CAAC,EACtF;IAAEJ,KAAK,EAAE,SAAS;IAAElF,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,GAAG;IAAEqF,WAAW,EAAE;EAAkB,CAAC,EAC/E;IAAEJ,KAAK,EAAE,aAAa;IAAElF,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,IAAI;IAAEqF,WAAW,EAAE;EAAkB,CAAC,CAC/F;EAED,oBACE1G,OAAA;IAAA+B,QAAA,gBACE/B,OAAA;MAAOwB,SAAS,EAAC,8CAA8C;MAAAO,QAAA,EAAC;IAEhE;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACRpC,OAAA;MAAKwB,SAAS,EAAC,uCAAuC;MAAAO,QAAA,EACnDgF,UAAU,CAACzE,GAAG,CAAED,IAAI,iBACnBrC,OAAA;QAEEwC,OAAO,EAAEA,CAAA,KAAM+D,QAAQ,CAAClE,IAAI,CAACiE,KAAK,CAAE;QACpC9E,SAAS,EAAE,uDACT8E,KAAK,KAAKjE,IAAI,CAACiE,KAAK,GAChB,oDAAoD,GACpD,oEAAoE,EACvE;QAAAvE,QAAA,gBAEH/B,OAAA;UAAKwB,SAAS,EAAC,cAAc;UAAAO,QAAA,EAAEM,IAAI,CAAChB;QAAI;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/CpC,OAAA;UAAKwB,SAAS,EAAC,qBAAqB;UAAAO,QAAA,EAAEM,IAAI,CAACjB;QAAK;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,GATlDC,IAAI,CAACiE,KAAK;QAAArE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUT,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC4E,GAAA,GAhCWF,iBAAiB;AAAA,IAAAnE,EAAA,EAAAiB,GAAA,EAAAS,GAAA,EAAAM,GAAA,EAAAU,GAAA,EAAAE,GAAA,EAAAa,GAAA,EAAAS,GAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAtE,EAAA;AAAAsE,YAAA,CAAArD,GAAA;AAAAqD,YAAA,CAAA5C,GAAA;AAAA4C,YAAA,CAAAtC,GAAA;AAAAsC,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAA1B,GAAA;AAAA0B,YAAA,CAAAb,GAAA;AAAAa,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}