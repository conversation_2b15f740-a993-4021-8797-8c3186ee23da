{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIZekri\\\\frontend\\\\src\\\\components\\\\DragDropComponents.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\n/**\n * DragDropComponents - Composants pour l'interface drag & drop\n * Utilise @dnd-kit pour créer des colonnes draggables et des zones de drop\n */\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useDraggable, useDroppable } from '@dnd-kit/core';\nimport { DarkBadge } from './YellowMindUI';\n\n// Fonctions utilitaires pour les colonnes\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst getColumnIcon = type => {\n  switch (type === null || type === void 0 ? void 0 : type.toLowerCase()) {\n    case 'int':\n    case 'integer':\n    case 'bigint':\n    case 'smallint':\n    case 'tinyint':\n      return '🔢';\n    case 'float':\n    case 'double':\n    case 'decimal':\n    case 'numeric':\n    case 'real':\n      return '📊';\n    case 'varchar':\n    case 'nvarchar':\n    case 'char':\n    case 'nchar':\n    case 'text':\n    case 'ntext':\n      return '📝';\n    case 'datetime':\n    case 'datetime2':\n    case 'date':\n    case 'time':\n    case 'timestamp':\n      return '📅';\n    case 'bit':\n    case 'boolean':\n      return '✅';\n    default:\n      return '📋';\n  }\n};\nconst getColumnTypeLabel = type => {\n  switch (type === null || type === void 0 ? void 0 : type.toLowerCase()) {\n    case 'int':\n    case 'integer':\n    case 'bigint':\n    case 'smallint':\n    case 'tinyint':\n      return 'Entier';\n    case 'float':\n    case 'double':\n    case 'decimal':\n    case 'numeric':\n    case 'real':\n      return 'Décimal';\n    case 'varchar':\n    case 'nvarchar':\n    case 'char':\n    case 'nchar':\n    case 'text':\n    case 'ntext':\n      return 'Texte';\n    case 'datetime':\n    case 'datetime2':\n    case 'date':\n    case 'time':\n    case 'timestamp':\n      return 'Date';\n    case 'bit':\n    case 'boolean':\n      return 'Booléen';\n    default:\n      return type || 'Inconnu';\n  }\n};\nconst getColumnColor = type => {\n  if (!type) return 'bg-gray-600/20 border-gray-500 text-gray-300';\n  const dataType = type.toLowerCase();\n  if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n    return 'bg-blue-600/20 border-blue-500 text-blue-300';\n  }\n  if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n    return 'bg-green-600/20 border-green-500 text-green-300';\n  }\n  if (dataType.includes('date') || dataType.includes('time')) {\n    return 'bg-purple-600/20 border-purple-500 text-purple-300';\n  }\n  return 'bg-gray-600/20 border-gray-500 text-gray-300';\n};\n\n// Composant de menu contextuel pour les colonnes\nexport const ColumnContextMenu = ({\n  isOpen,\n  onClose,\n  position,\n  column,\n  onAddToAxis,\n  selectedTable,\n  currentAssignments = {}\n}) => {\n  _s();\n  const menuRef = useRef(null);\n  const [isProcessing, setIsProcessing] = useState(false);\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (menuRef.current && !menuRef.current.contains(event.target)) {\n        onClose();\n      }\n    };\n    const handleEscape = event => {\n      if (event.key === 'Escape') {\n        onClose();\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n      document.addEventListener('keydown', handleEscape);\n      return () => {\n        document.removeEventListener('mousedown', handleClickOutside);\n        document.removeEventListener('keydown', handleEscape);\n      };\n    }\n  }, [isOpen, onClose]);\n  if (!isOpen) return null;\n  const menuItems = [{\n    id: 'x-axis',\n    label: 'Ajouter à Axe X',\n    icon: '📊',\n    color: 'text-blue-400'\n  }, {\n    id: 'y-axis',\n    label: 'Ajouter à Axe Y',\n    icon: '📈',\n    color: 'text-green-400'\n  }, {\n    id: 'legend',\n    label: 'Ajouter à Légende',\n    icon: '🏷️',\n    color: 'text-yellow-400'\n  }, {\n    id: 'values',\n    label: 'Ajouter aux Valeurs',\n    icon: '💎',\n    color: 'text-purple-400'\n  }];\n  const handleItemClick = async item => {\n    if (isProcessing) return;\n    setIsProcessing(true);\n    try {\n      const columnWithTable = {\n        ...column,\n        table: selectedTable\n      };\n      await onAddToAxis(item.id, columnWithTable);\n      onClose();\n    } catch (error) {\n      console.error('Erreur lors de l\\'ajout de la colonne:', error);\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: menuRef,\n    className: \"fixed z-50 bg-gray-800 border border-gray-600 rounded-lg shadow-xl backdrop-blur-sm animate-in fade-in-0 zoom-in-95 duration-200\",\n    style: {\n      left: Math.min(position.x, window.innerWidth - 220),\n      top: Math.min(position.y, window.innerHeight - 200),\n      minWidth: '200px'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-3 py-2 border-b border-gray-600 mb-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: getColumnIcon(column.type)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-200\",\n              children: column.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-400 capitalize\",\n              children: column.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), menuItems.map(item => {\n        var _currentAssignments$i;\n        const isAssigned = ((_currentAssignments$i = currentAssignments[item.id]) === null || _currentAssignments$i === void 0 ? void 0 : _currentAssignments$i.name) === column.name;\n        return /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleItemClick(item),\n          disabled: isProcessing,\n          className: `\n                w-full flex items-center justify-between px-3 py-2 rounded-md text-left\n                transition-all duration-200 group\n                ${isAssigned ? 'bg-purple-600/20 border border-purple-500/50' : 'hover:bg-gray-700'}\n                ${isProcessing ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}\n              `,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `text-sm ${isAssigned ? 'text-purple-300' : `${item.color} group-hover:text-gray-100`}`,\n              children: item.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this), isAssigned && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-purple-400\",\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 17\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 13\n        }, this);\n      }), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 pt-2 border-t border-gray-600\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-500 px-3 py-1\",\n          children: \"Clic droit ou \\u26A1 pour acc\\xE8s rapide\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 158,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour une colonne draggable\n_s(ColumnContextMenu, \"YPZRw7kMNee+aNsCOPmwsOkCzdk=\");\n_c = ColumnContextMenu;\nexport const DraggableColumn = ({\n  column,\n  id,\n  onAddToAxis,\n  selectedTable,\n  currentAssignments\n}) => {\n  _s2();\n  const [contextMenu, setContextMenu] = useState({\n    isOpen: false,\n    position: {\n      x: 0,\n      y: 0\n    }\n  });\n  const {\n    attributes,\n    listeners,\n    setNodeRef,\n    transform,\n    isDragging\n  } = useDraggable({\n    id: id,\n    data: {\n      column: column,\n      type: 'column'\n    }\n  });\n  const style = transform ? {\n    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,\n    opacity: isDragging ? 0.7 : 1,\n    zIndex: isDragging ? 1000 : 1\n  } : undefined;\n  const handleContextMenu = e => {\n    e.preventDefault();\n    setContextMenu({\n      isOpen: true,\n      position: {\n        x: e.clientX,\n        y: e.clientY\n      }\n    });\n  };\n  const handleQuickAdd = e => {\n    e.stopPropagation();\n    setContextMenu({\n      isOpen: true,\n      position: {\n        x: e.currentTarget.getBoundingClientRect().right + 10,\n        y: e.currentTarget.getBoundingClientRect().top\n      }\n    });\n  };\n\n  // Vérifier si la colonne est assignée quelque part\n  const isAssigned = currentAssignments && Object.values(currentAssignments).some(assignment => (assignment === null || assignment === void 0 ? void 0 : assignment.name) === column.name);\n\n  // Trouver où la colonne est assignée\n  const getAssignmentInfo = () => {\n    if (!currentAssignments) return null;\n    const assignments = [];\n    Object.entries(currentAssignments).forEach(([key, value]) => {\n      if ((value === null || value === void 0 ? void 0 : value.name) === column.name) {\n        const labels = {\n          'x-axis': 'X',\n          'y-axis': 'Y',\n          'legend': 'L',\n          'values': 'V'\n        };\n        assignments.push(labels[key] || key);\n      }\n    });\n    return assignments.length > 0 ? assignments.join(', ') : null;\n  };\n  const getColumnIcon = type => {\n    const dataType = type.toLowerCase();\n    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n      return '🔢';\n    }\n    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n      return '📝';\n    }\n    if (dataType.includes('date') || dataType.includes('time')) {\n      return '📅';\n    }\n    if (dataType.includes('bit') || dataType.includes('boolean')) {\n      return '☑️';\n    }\n    return '📊';\n  };\n  const getColumnColor = type => {\n    const dataType = type.toLowerCase();\n    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n      return 'bg-blue-600/20 border-blue-500 text-blue-300';\n    }\n    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n      return 'bg-green-600/20 border-green-500 text-green-300';\n    }\n    if (dataType.includes('date') || dataType.includes('time')) {\n      return 'bg-purple-600/20 border-purple-500 text-purple-300';\n    }\n    return 'bg-gray-600/20 border-gray-500 text-gray-300';\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: setNodeRef,\n      style: style,\n      ...listeners,\n      ...attributes,\n      onContextMenu: handleContextMenu,\n      className: `\n          p-3 rounded-lg border-2 border-dashed cursor-grab active:cursor-grabbing\n          transition-all duration-300 ease-in-out transform hover:scale-105 hover:shadow-lg\n          ${getColumnColor(column.type)}\n          ${isDragging ? 'shadow-2xl column-drag-start rotate-3 scale-110' : 'hover:shadow-md hover:shadow-purple-500/10'}\n          ${isAssigned ? 'ring-2 ring-purple-500/50 bg-purple-600/10' : ''}\n          group relative\n        `,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 flex-1 min-w-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: `text-lg transition-transform duration-200 ${isDragging ? 'scale-110' : 'group-hover:scale-105'}`,\n            children: getColumnIcon(column.type)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: `font-medium truncate transition-colors duration-200 ${isDragging ? 'text-purple-200' : 'group-hover:text-gray-100'}`,\n                children: column.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this), isAssigned && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs bg-purple-600/30 text-purple-300 px-1.5 py-0.5 rounded-full border border-purple-500/50\",\n                children: getAssignmentInfo()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-xs transition-all duration-200 ${isDragging ? 'opacity-90 text-purple-300' : 'opacity-75 group-hover:opacity-90'}`,\n              children: column.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this), onAddToAxis && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleQuickAdd,\n          className: \"opacity-0 group-hover:opacity-100 transition-all duration-200 p-1 rounded hover:bg-gray-700 text-gray-400 hover:text-purple-400 transform hover:scale-110 active:scale-95\",\n          title: \"Ajouter rapidement\",\n          children: \"\\u26A1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ColumnContextMenu, {\n      isOpen: contextMenu.isOpen,\n      onClose: () => setContextMenu({\n        ...contextMenu,\n        isOpen: false\n      }),\n      position: contextMenu.position,\n      column: column,\n      onAddToAxis: onAddToAxis,\n      selectedTable: selectedTable,\n      currentAssignments: currentAssignments\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// Composant pour une zone de drop\n_s2(DraggableColumn, \"Ug8s2XjgY4HyuI7lKr8hN/wtf3s=\", false, function () {\n  return [useDraggable];\n});\n_c2 = DraggableColumn;\nexport const DropZone = ({\n  id,\n  title,\n  subtitle,\n  icon,\n  children,\n  acceptedColumn,\n  onClear\n}) => {\n  _s3();\n  const {\n    isOver,\n    setNodeRef\n  } = useDroppable({\n    id: id,\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: setNodeRef,\n    className: `\n        p-6 rounded-lg border-2 border-dashed min-h-[140px] transition-all duration-200\n        ${isOver ? 'border-purple-400 bg-purple-600/10 scale-105' : 'border-gray-600 bg-gray-800/30'}\n        ${acceptedColumn ? 'border-purple-500 bg-purple-600/20' : ''}\n      `,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-lg\",\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium text-gray-200\",\n            children: title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this), subtitle && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-400\",\n            children: subtitle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 26\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 9\n      }, this), acceptedColumn && onClear && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClear,\n        className: \"text-gray-400 hover:text-red-400 transition-colors\",\n        title: \"Supprimer\",\n        children: \"\\u2715\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 419,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 410,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-[60px] flex items-center justify-center\",\n      children: acceptedColumn ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3 p-3 bg-purple-600/30 rounded-lg border border-purple-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xl\",\n            children: acceptedColumn.type.toLowerCase().includes('int') || acceptedColumn.type.toLowerCase().includes('decimal') || acceptedColumn.type.toLowerCase().includes('float') ? '🔢' : acceptedColumn.type.toLowerCase().includes('date') ? '📅' : '📝'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-medium text-purple-200 truncate\",\n              children: acceptedColumn.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-purple-300\",\n              children: acceptedColumn.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 17\n            }, this), acceptedColumn.table && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-purple-400 mt-1\",\n              children: [\"\\uD83D\\uDCCB \", acceptedColumn.table]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 text-sm\",\n          children: isOver ? 'Relâchez ici' : 'Glissez une colonne ici'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 430,\n      columnNumber: 7\n    }, this), children]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 398,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour afficher les colonnes disponibles\n_s3(DropZone, \"fT702R7NW3L8KUJObOwGrnMsXMQ=\", false, function () {\n  return [useDroppable];\n});\n_c3 = DropZone;\nexport const ColumnsPanel = ({\n  columns,\n  title,\n  onAddToAxis,\n  selectedTable,\n  currentAssignments\n}) => {\n  if (!columns || columns.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-16 h-16 bg-gray-700 rounded-2xl mx-auto mb-4 flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-2xl\",\n          children: \"\\uD83D\\uDCCB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-400\",\n        children: \"Aucune colonne disponible\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 467,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-200\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 479,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DarkBadge, {\n        variant: \"info\",\n        children: columns.length\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 478,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3 max-h-[500px] overflow-y-auto\",\n      children: columns.map((column, index) => /*#__PURE__*/_jsxDEV(DraggableColumn, {\n        id: `column-${column.name}`,\n        column: column,\n        onAddToAxis: onAddToAxis,\n        selectedTable: selectedTable,\n        currentAssignments: currentAssignments\n      }, `${column.name}-${index}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 485,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 483,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 477,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour les zones de drop organisées\n_c4 = ColumnsPanel;\nexport const DropZonesPanel = ({\n  xAxis,\n  yAxis,\n  legend,\n  values,\n  onClearX,\n  onClearY,\n  onClearLegend,\n  onClearValues\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n    children: [/*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"x-axis\",\n      title: \"Axe X\",\n      subtitle: \"Cat\\xE9gories ou groupes\",\n      icon: \"\\uD83D\\uDCCA\",\n      acceptedColumn: xAxis,\n      onClear: onClearX\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 512,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"y-axis\",\n      title: \"Axe Y\",\n      subtitle: \"Valeurs num\\xE9riques (optionnel)\",\n      icon: \"\\uD83D\\uDCC8\",\n      acceptedColumn: yAxis,\n      onClear: onClearY\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 521,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"legend\",\n      title: \"L\\xE9gende\",\n      subtitle: \"Sous-cat\\xE9gories (optionnel)\",\n      icon: \"\\uD83C\\uDFF7\\uFE0F\",\n      acceptedColumn: legend,\n      onClear: onClearLegend\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 530,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"values\",\n      title: \"Valeurs\",\n      subtitle: \"Donn\\xE9es \\xE0 agr\\xE9ger\",\n      icon: \"\\uD83D\\uDC8E\",\n      acceptedColumn: values,\n      onClear: onClearValues\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 511,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour une zone de drop compacte dans la barre fixe\n_c5 = DropZonesPanel;\nexport const CompactDropZone = ({\n  id,\n  title,\n  icon,\n  acceptedColumn,\n  onClear,\n  isOver\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n        flex items-center justify-between p-3 rounded-lg border-2 border-dashed min-h-[60px]\n        transition-all duration-300 ease-in-out transform\n        ${isOver ? 'border-purple-400 bg-purple-600/20 scale-105 drop-zone-glow drop-zone-hover' : 'border-gray-600 bg-gray-800/50 hover:border-gray-500 hover:bg-gray-800/70'}\n        ${acceptedColumn ? 'border-purple-500 bg-purple-600/30 shadow-lg shadow-purple-500/20' : ''}\n        backdrop-blur-sm hover:backdrop-blur-md\n      `,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2 flex-1 min-w-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: `text-lg transition-transform duration-200 ${isOver ? 'scale-110' : ''}`,\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 567,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-w-0 flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: `font-medium text-sm truncate transition-colors duration-200 ${isOver ? 'text-purple-200' : 'text-gray-200'}`,\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 11\n        }, this), acceptedColumn ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1 mt-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-purple-300 truncate font-medium\",\n            children: acceptedColumn.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 15\n          }, this), acceptedColumn.table && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-400\",\n            children: [\"(\", acceptedColumn.table, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-xs transition-colors duration-200 ${isOver ? 'text-purple-300' : 'text-gray-400'}`,\n          children: isOver ? 'Relâchez ici' : 'Glissez ici'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 570,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 566,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-1\",\n      children: acceptedColumn && onClear && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClear,\n        className: \"text-gray-400 hover:text-red-400 transition-all duration-200 p-1 hover:bg-red-600/20 rounded hover:scale-110 active:scale-95\",\n        title: \"Supprimer\",\n        children: \"\\u2715\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 598,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 596,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 554,\n    columnNumber: 5\n  }, this);\n};\n\n// Barre fixe avec les zones de drop\n_c6 = CompactDropZone;\nexport const FixedDropBar = ({\n  xAxis,\n  yAxis,\n  legend,\n  values,\n  onClearX,\n  onClearY,\n  onClearLegend,\n  onClearValues,\n  isVisible = true,\n  position = 'bottom' // 'top' ou 'bottom'\n}) => {\n  _s4();\n  const {\n    isOver: isOverX,\n    setNodeRef: setNodeRefX\n  } = useDroppable({\n    id: 'x-axis-fixed',\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  const {\n    isOver: isOverY,\n    setNodeRef: setNodeRefY\n  } = useDroppable({\n    id: 'y-axis-fixed',\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  const {\n    isOver: isOverLegend,\n    setNodeRef: setNodeRefLegend\n  } = useDroppable({\n    id: 'legend-fixed',\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  const {\n    isOver: isOverValues,\n    setNodeRef: setNodeRefValues\n  } = useDroppable({\n    id: 'values-fixed',\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  if (!isVisible) return null;\n  const positionClasses = position === 'top' ? 'top-0 border-b' : 'bottom-0 border-t';\n  const hasAnyColumn = xAxis || yAxis || legend || values;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n      fixed left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-md ${positionClasses} border-gray-700\n      transition-all duration-300 ease-in-out shadow-2xl\n      ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'}\n    `,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 py-3 sm:py-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg animate-pulse\",\n            children: \"\\uD83C\\uDFAF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-semibold text-gray-200\",\n              children: \"Zones de Drop\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 15\n            }, this), hasAnyColumn && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-purple-400\",\n              children: [[xAxis, yAxis, legend, values].filter(Boolean).length, \" colonne(s) assign\\xE9e(s)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 661,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden sm:block text-xs text-gray-400\",\n          children: \"Glissez vos colonnes ici pour cr\\xE9er votre visualisation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 672,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 660,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          ref: setNodeRefX,\n          children: /*#__PURE__*/_jsxDEV(CompactDropZone, {\n            id: \"x-axis-fixed\",\n            title: \"Axe X\",\n            icon: \"\\uD83D\\uDCCA\",\n            acceptedColumn: xAxis,\n            onClear: onClearX,\n            isOver: isOverX\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 679,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 678,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: setNodeRefY,\n          children: /*#__PURE__*/_jsxDEV(CompactDropZone, {\n            id: \"y-axis-fixed\",\n            title: \"Axe Y\",\n            icon: \"\\uD83D\\uDCC8\",\n            acceptedColumn: yAxis,\n            onClear: onClearY,\n            isOver: isOverY\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 690,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 689,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: setNodeRefLegend,\n          children: /*#__PURE__*/_jsxDEV(CompactDropZone, {\n            id: \"legend-fixed\",\n            title: \"L\\xE9gende\",\n            icon: \"\\uD83C\\uDFF7\\uFE0F\",\n            acceptedColumn: legend,\n            onClear: onClearLegend,\n            isOver: isOverLegend\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 701,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 700,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: setNodeRefValues,\n          children: /*#__PURE__*/_jsxDEV(CompactDropZone, {\n            id: \"values-fixed\",\n            title: \"Valeurs\",\n            icon: \"\\uD83D\\uDC8E\",\n            acceptedColumn: values,\n            onClear: onClearValues,\n            isOver: isOverValues\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 712,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 711,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 677,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sm:hidden mt-2 text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-500\",\n          children: \"Glissez ou utilisez le menu \\u26A1 des colonnes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 725,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 724,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 659,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 654,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour le sélecteur de fonction d'agrégation\n_s4(FixedDropBar, \"iDE4Z2x6jYrF9tSCiODbcpzg8qI=\", false, function () {\n  return [useDroppable, useDroppable, useDroppable, useDroppable];\n});\n_c7 = FixedDropBar;\nexport const AggregationSelector = ({\n  value,\n  onChange,\n  disabled\n}) => {\n  const aggregations = [{\n    value: 'SUM',\n    label: 'Somme',\n    icon: '➕',\n    description: 'Addition de toutes les valeurs'\n  }, {\n    value: 'AVG',\n    label: 'Moyenne',\n    icon: '📊',\n    description: 'Valeur moyenne'\n  }, {\n    value: 'COUNT',\n    label: 'Nombre',\n    icon: '🔢',\n    description: 'Nombre d\\'occurrences'\n  }, {\n    value: 'MIN',\n    label: 'Minimum',\n    icon: '⬇️',\n    description: 'Valeur minimale'\n  }, {\n    value: 'MAX',\n    label: 'Maximum',\n    icon: '⬆️',\n    description: 'Valeur maximale'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"block text-sm font-medium text-gray-300 mb-2\",\n      children: \"Fonction d'agr\\xE9gation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 746,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n      value: value,\n      onChange: e => onChange(e.target.value),\n      disabled: disabled,\n      className: \"w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-gray-100 focus:border-purple-500 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed\",\n      children: aggregations.map(agg => /*#__PURE__*/_jsxDEV(\"option\", {\n        value: agg.value,\n        children: [agg.icon, \" \", agg.label, \" - \", agg.description]\n      }, agg.value, true, {\n        fileName: _jsxFileName,\n        lineNumber: 756,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 749,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 745,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour le sélecteur de type de graphique\n_c8 = AggregationSelector;\nexport const ChartTypeSelector = ({\n  value,\n  onChange\n}) => {\n  const chartTypes = [{\n    value: 'bar',\n    label: 'Barres',\n    icon: '📊',\n    description: 'Graphique en barres'\n  }, {\n    value: 'line',\n    label: 'Ligne',\n    icon: '📈',\n    description: 'Graphique linéaire'\n  }, {\n    value: 'pie',\n    label: 'Circulaire',\n    icon: '🥧',\n    description: 'Graphique circulaire'\n  }, {\n    value: 'scatter',\n    label: 'Nuage',\n    icon: '⚫',\n    description: 'Nuage de points'\n  }, {\n    value: 'stacked_bar',\n    label: 'Barres empilées',\n    icon: '📚',\n    description: 'Barres empilées'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"block text-sm font-medium text-gray-300 mb-2\",\n      children: \"Type de graphique\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 777,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 md:grid-cols-3 gap-2\",\n      children: chartTypes.map(type => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onChange(type.value),\n        className: `p-3 rounded-lg border transition-colors text-center ${value === type.value ? 'border-purple-500 bg-purple-600/20 text-purple-300' : 'border-gray-700 bg-gray-800/50 text-gray-300 hover:border-gray-600'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg mb-1\",\n          children: type.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 791,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs font-medium\",\n          children: type.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 792,\n          columnNumber: 13\n        }, this)]\n      }, type.value, true, {\n        fileName: _jsxFileName,\n        lineNumber: 782,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 780,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 776,\n    columnNumber: 5\n  }, this);\n};\n_c9 = ChartTypeSelector;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"ColumnContextMenu\");\n$RefreshReg$(_c2, \"DraggableColumn\");\n$RefreshReg$(_c3, \"DropZone\");\n$RefreshReg$(_c4, \"ColumnsPanel\");\n$RefreshReg$(_c5, \"DropZonesPanel\");\n$RefreshReg$(_c6, \"CompactDropZone\");\n$RefreshReg$(_c7, \"FixedDropBar\");\n$RefreshReg$(_c8, \"AggregationSelector\");\n$RefreshReg$(_c9, \"ChartTypeSelector\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useDraggable", "useDroppable", "DarkBadge", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "getColumnIcon", "type", "toLowerCase", "getColumnTypeLabel", "getColumnColor", "dataType", "includes", "ColumnContextMenu", "isOpen", "onClose", "position", "column", "onAddToAxis", "selectedTable", "currentAssignments", "_s", "menuRef", "isProcessing", "setIsProcessing", "handleClickOutside", "event", "current", "contains", "target", "handleEscape", "key", "document", "addEventListener", "removeEventListener", "menuItems", "id", "label", "icon", "color", "handleItemClick", "item", "columnWithTable", "table", "error", "console", "ref", "className", "style", "left", "Math", "min", "x", "window", "innerWidth", "top", "y", "innerHeight", "min<PERSON><PERSON><PERSON>", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "map", "_currentAssignments$i", "isAssigned", "onClick", "disabled", "_c", "DraggableColumn", "_s2", "contextMenu", "setContextMenu", "attributes", "listeners", "setNodeRef", "transform", "isDragging", "data", "opacity", "zIndex", "undefined", "handleContextMenu", "e", "preventDefault", "clientX", "clientY", "handleQuickAdd", "stopPropagation", "currentTarget", "getBoundingClientRect", "right", "Object", "values", "some", "assignment", "getAssignmentInfo", "assignments", "entries", "for<PERSON>ach", "value", "labels", "push", "length", "join", "onContextMenu", "title", "_c2", "DropZone", "subtitle", "acceptedColumn", "onClear", "_s3", "isOver", "accepts", "_c3", "ColumnsPanel", "columns", "variant", "index", "_c4", "DropZonesPanel", "xAxis", "yAxis", "legend", "onClearX", "onClearY", "onClearLegend", "onClearValues", "_c5", "CompactDropZone", "_c6", "FixedDropBar", "isVisible", "_s4", "isOverX", "setNodeRefX", "isOverY", "setNodeRefY", "isOverLegend", "setNodeRefLegend", "isOverValues", "setNodeRefValues", "positionClasses", "hasAnyColumn", "filter", "Boolean", "_c7", "AggregationSelector", "onChange", "aggregations", "description", "agg", "_c8", "ChartTypeSelector", "chartTypes", "_c9", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/AIZekri/frontend/src/components/DragDropComponents.js"], "sourcesContent": ["/**\n * DragDropComponents - Composants pour l'interface drag & drop\n * Utilise @dnd-kit pour créer des colonnes draggables et des zones de drop\n */\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useDraggable, useDroppable } from '@dnd-kit/core';\nimport { DarkBadge } from './YellowMindUI';\n\n// Fonctions utilitaires pour les colonnes\nconst getColumnIcon = (type) => {\n  switch (type?.toLowerCase()) {\n    case 'int':\n    case 'integer':\n    case 'bigint':\n    case 'smallint':\n    case 'tinyint':\n      return '🔢';\n    case 'float':\n    case 'double':\n    case 'decimal':\n    case 'numeric':\n    case 'real':\n      return '📊';\n    case 'varchar':\n    case 'nvarchar':\n    case 'char':\n    case 'nchar':\n    case 'text':\n    case 'ntext':\n      return '📝';\n    case 'datetime':\n    case 'datetime2':\n    case 'date':\n    case 'time':\n    case 'timestamp':\n      return '📅';\n    case 'bit':\n    case 'boolean':\n      return '✅';\n    default:\n      return '📋';\n  }\n};\n\nconst getColumnTypeLabel = (type) => {\n  switch (type?.toLowerCase()) {\n    case 'int':\n    case 'integer':\n    case 'bigint':\n    case 'smallint':\n    case 'tinyint':\n      return 'Entier';\n    case 'float':\n    case 'double':\n    case 'decimal':\n    case 'numeric':\n    case 'real':\n      return 'Décimal';\n    case 'varchar':\n    case 'nvarchar':\n    case 'char':\n    case 'nchar':\n    case 'text':\n    case 'ntext':\n      return 'Texte';\n    case 'datetime':\n    case 'datetime2':\n    case 'date':\n    case 'time':\n    case 'timestamp':\n      return 'Date';\n    case 'bit':\n    case 'boolean':\n      return 'Booléen';\n    default:\n      return type || 'Inconnu';\n  }\n};\n\nconst getColumnColor = (type) => {\n  if (!type) return 'bg-gray-600/20 border-gray-500 text-gray-300';\n\n  const dataType = type.toLowerCase();\n  if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n    return 'bg-blue-600/20 border-blue-500 text-blue-300';\n  }\n  if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n    return 'bg-green-600/20 border-green-500 text-green-300';\n  }\n  if (dataType.includes('date') || dataType.includes('time')) {\n    return 'bg-purple-600/20 border-purple-500 text-purple-300';\n  }\n  return 'bg-gray-600/20 border-gray-500 text-gray-300';\n};\n\n// Composant de menu contextuel pour les colonnes\nexport const ColumnContextMenu = ({\n  isOpen,\n  onClose,\n  position,\n  column,\n  onAddToAxis,\n  selectedTable,\n  currentAssignments = {}\n}) => {\n  const menuRef = useRef(null);\n  const [isProcessing, setIsProcessing] = useState(false);\n\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (menuRef.current && !menuRef.current.contains(event.target)) {\n        onClose();\n      }\n    };\n\n    const handleEscape = (event) => {\n      if (event.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n      document.addEventListener('keydown', handleEscape);\n      return () => {\n        document.removeEventListener('mousedown', handleClickOutside);\n        document.removeEventListener('keydown', handleEscape);\n      };\n    }\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  const menuItems = [\n    { id: 'x-axis', label: 'Ajouter à Axe X', icon: '📊', color: 'text-blue-400' },\n    { id: 'y-axis', label: 'Ajouter à Axe Y', icon: '📈', color: 'text-green-400' },\n    { id: 'legend', label: 'Ajouter à Légende', icon: '🏷️', color: 'text-yellow-400' },\n    { id: 'values', label: 'Ajouter aux Valeurs', icon: '💎', color: 'text-purple-400' }\n  ];\n\n  const handleItemClick = async (item) => {\n    if (isProcessing) return;\n\n    setIsProcessing(true);\n    try {\n      const columnWithTable = { ...column, table: selectedTable };\n      await onAddToAxis(item.id, columnWithTable);\n      onClose();\n    } catch (error) {\n      console.error('Erreur lors de l\\'ajout de la colonne:', error);\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  return (\n    <div\n      ref={menuRef}\n      className=\"fixed z-50 bg-gray-800 border border-gray-600 rounded-lg shadow-xl backdrop-blur-sm\n                 animate-in fade-in-0 zoom-in-95 duration-200\"\n      style={{\n        left: Math.min(position.x, window.innerWidth - 220),\n        top: Math.min(position.y, window.innerHeight - 200),\n        minWidth: '200px'\n      }}\n    >\n      <div className=\"p-2\">\n        <div className=\"px-3 py-2 border-b border-gray-600 mb-2\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-lg\">{getColumnIcon(column.type)}</span>\n            <div>\n              <p className=\"text-sm font-medium text-gray-200\">{column.name}</p>\n              <p className=\"text-xs text-gray-400 capitalize\">{column.type}</p>\n            </div>\n          </div>\n        </div>\n\n        {menuItems.map((item) => {\n          const isAssigned = currentAssignments[item.id]?.name === column.name;\n          return (\n            <button\n              key={item.id}\n              onClick={() => handleItemClick(item)}\n              disabled={isProcessing}\n              className={`\n                w-full flex items-center justify-between px-3 py-2 rounded-md text-left\n                transition-all duration-200 group\n                ${isAssigned\n                  ? 'bg-purple-600/20 border border-purple-500/50'\n                  : 'hover:bg-gray-700'\n                }\n                ${isProcessing ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}\n              `}\n            >\n              <div className=\"flex items-center space-x-3\">\n                <span className=\"text-lg\">{item.icon}</span>\n                <span className={`text-sm ${\n                  isAssigned ? 'text-purple-300' : `${item.color} group-hover:text-gray-100`\n                }`}>\n                  {item.label}\n                </span>\n              </div>\n              {isAssigned && (\n                <span className=\"text-xs text-purple-400\">✓</span>\n              )}\n            </button>\n          );\n        })}\n\n        <div className=\"mt-2 pt-2 border-t border-gray-600\">\n          <p className=\"text-xs text-gray-500 px-3 py-1\">\n            Clic droit ou ⚡ pour accès rapide\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Composant pour une colonne draggable\nexport const DraggableColumn = ({ column, id, onAddToAxis, selectedTable, currentAssignments }) => {\n  const [contextMenu, setContextMenu] = useState({ isOpen: false, position: { x: 0, y: 0 } });\n\n  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({\n    id: id,\n    data: {\n      column: column,\n      type: 'column'\n    }\n  });\n\n  const style = transform ? {\n    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,\n    opacity: isDragging ? 0.7 : 1,\n    zIndex: isDragging ? 1000 : 1\n  } : undefined;\n\n  const handleContextMenu = (e) => {\n    e.preventDefault();\n    setContextMenu({\n      isOpen: true,\n      position: { x: e.clientX, y: e.clientY }\n    });\n  };\n\n  const handleQuickAdd = (e) => {\n    e.stopPropagation();\n    setContextMenu({\n      isOpen: true,\n      position: {\n        x: e.currentTarget.getBoundingClientRect().right + 10,\n        y: e.currentTarget.getBoundingClientRect().top\n      }\n    });\n  };\n\n  // Vérifier si la colonne est assignée quelque part\n  const isAssigned = currentAssignments && Object.values(currentAssignments).some(\n    assignment => assignment?.name === column.name\n  );\n\n  // Trouver où la colonne est assignée\n  const getAssignmentInfo = () => {\n    if (!currentAssignments) return null;\n\n    const assignments = [];\n    Object.entries(currentAssignments).forEach(([key, value]) => {\n      if (value?.name === column.name) {\n        const labels = {\n          'x-axis': 'X',\n          'y-axis': 'Y',\n          'legend': 'L',\n          'values': 'V'\n        };\n        assignments.push(labels[key] || key);\n      }\n    });\n    return assignments.length > 0 ? assignments.join(', ') : null;\n  };\n\n  const getColumnIcon = (type) => {\n    const dataType = type.toLowerCase();\n    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n      return '🔢';\n    }\n    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n      return '📝';\n    }\n    if (dataType.includes('date') || dataType.includes('time')) {\n      return '📅';\n    }\n    if (dataType.includes('bit') || dataType.includes('boolean')) {\n      return '☑️';\n    }\n    return '📊';\n  };\n\n  const getColumnColor = (type) => {\n    const dataType = type.toLowerCase();\n    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n      return 'bg-blue-600/20 border-blue-500 text-blue-300';\n    }\n    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n      return 'bg-green-600/20 border-green-500 text-green-300';\n    }\n    if (dataType.includes('date') || dataType.includes('time')) {\n      return 'bg-purple-600/20 border-purple-500 text-purple-300';\n    }\n    return 'bg-gray-600/20 border-gray-500 text-gray-300';\n  };\n\n  return (\n    <>\n      <div\n        ref={setNodeRef}\n        style={style}\n        {...listeners}\n        {...attributes}\n        onContextMenu={handleContextMenu}\n        className={`\n          p-3 rounded-lg border-2 border-dashed cursor-grab active:cursor-grabbing\n          transition-all duration-300 ease-in-out transform hover:scale-105 hover:shadow-lg\n          ${getColumnColor(column.type)}\n          ${isDragging ? 'shadow-2xl column-drag-start rotate-3 scale-110' : 'hover:shadow-md hover:shadow-purple-500/10'}\n          ${isAssigned ? 'ring-2 ring-purple-500/50 bg-purple-600/10' : ''}\n          group relative\n        `}\n      >\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2 flex-1 min-w-0\">\n            <span className={`text-lg transition-transform duration-200 ${\n              isDragging ? 'scale-110' : 'group-hover:scale-105'\n            }`}>\n              {getColumnIcon(column.type)}\n            </span>\n            <div className=\"flex-1 min-w-0\">\n              <div className=\"flex items-center space-x-2\">\n                <p className={`font-medium truncate transition-colors duration-200 ${\n                  isDragging ? 'text-purple-200' : 'group-hover:text-gray-100'\n                }`}>\n                  {column.name}\n                </p>\n                {isAssigned && (\n                  <span className=\"text-xs bg-purple-600/30 text-purple-300 px-1.5 py-0.5 rounded-full border border-purple-500/50\">\n                    {getAssignmentInfo()}\n                  </span>\n                )}\n              </div>\n              <p className={`text-xs transition-all duration-200 ${\n                isDragging ? 'opacity-90 text-purple-300' : 'opacity-75 group-hover:opacity-90'\n              }`}>\n                {column.type}\n              </p>\n            </div>\n          </div>\n\n          {/* Bouton d'ajout rapide */}\n          {onAddToAxis && (\n            <button\n              onClick={handleQuickAdd}\n              className=\"opacity-0 group-hover:opacity-100 transition-all duration-200\n                         p-1 rounded hover:bg-gray-700 text-gray-400 hover:text-purple-400\n                         transform hover:scale-110 active:scale-95\"\n              title=\"Ajouter rapidement\"\n            >\n              ⚡\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Menu contextuel */}\n      <ColumnContextMenu\n        isOpen={contextMenu.isOpen}\n        onClose={() => setContextMenu({ ...contextMenu, isOpen: false })}\n        position={contextMenu.position}\n        column={column}\n        onAddToAxis={onAddToAxis}\n        selectedTable={selectedTable}\n        currentAssignments={currentAssignments}\n      />\n    </>\n  );\n};\n\n// Composant pour une zone de drop\nexport const DropZone = ({ id, title, subtitle, icon, children, acceptedColumn, onClear }) => {\n  const { isOver, setNodeRef } = useDroppable({\n    id: id,\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n\n  return (\n    <div\n      ref={setNodeRef}\n      className={`\n        p-6 rounded-lg border-2 border-dashed min-h-[140px] transition-all duration-200\n        ${isOver\n          ? 'border-purple-400 bg-purple-600/10 scale-105'\n          : 'border-gray-600 bg-gray-800/30'\n        }\n        ${acceptedColumn ? 'border-purple-500 bg-purple-600/20' : ''}\n      `}\n    >\n      {/* Header de la zone */}\n      <div className=\"flex items-center justify-between mb-3\">\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-lg\">{icon}</span>\n          <div>\n            <h4 className=\"font-medium text-gray-200\">{title}</h4>\n            {subtitle && <p className=\"text-xs text-gray-400\">{subtitle}</p>}\n          </div>\n        </div>\n        {acceptedColumn && onClear && (\n          <button\n            onClick={onClear}\n            className=\"text-gray-400 hover:text-red-400 transition-colors\"\n            title=\"Supprimer\"\n          >\n            ✕\n          </button>\n        )}\n      </div>\n\n      {/* Contenu de la zone */}\n      <div className=\"min-h-[60px] flex items-center justify-center\">\n        {acceptedColumn ? (\n          <div className=\"w-full\">\n            <div className=\"flex items-center space-x-3 p-3 bg-purple-600/30 rounded-lg border border-purple-500\">\n              <span className=\"text-xl\">\n                {acceptedColumn.type.toLowerCase().includes('int') ||\n                 acceptedColumn.type.toLowerCase().includes('decimal') ||\n                 acceptedColumn.type.toLowerCase().includes('float') ? '🔢' :\n                 acceptedColumn.type.toLowerCase().includes('date') ? '📅' : '📝'}\n              </span>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"font-medium text-purple-200 truncate\">{acceptedColumn.name}</p>\n                <p className=\"text-xs text-purple-300\">{acceptedColumn.type}</p>\n                {acceptedColumn.table && (\n                  <p className=\"text-xs text-purple-400 mt-1\">📋 {acceptedColumn.table}</p>\n                )}\n              </div>\n            </div>\n          </div>\n        ) : (\n          <div className=\"text-center\">\n            <p className=\"text-gray-500 text-sm\">\n              {isOver ? 'Relâchez ici' : 'Glissez une colonne ici'}\n            </p>\n          </div>\n        )}\n      </div>\n\n      {children}\n    </div>\n  );\n};\n\n// Composant pour afficher les colonnes disponibles\nexport const ColumnsPanel = ({ columns, title, onAddToAxis, selectedTable, currentAssignments }) => {\n  if (!columns || columns.length === 0) {\n    return (\n      <div className=\"text-center py-8\">\n        <div className=\"w-16 h-16 bg-gray-700 rounded-2xl mx-auto mb-4 flex items-center justify-center\">\n          <span className=\"text-2xl\">📋</span>\n        </div>\n        <p className=\"text-gray-400\">Aucune colonne disponible</p>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-200\">{title}</h3>\n        <DarkBadge variant=\"info\">{columns.length}</DarkBadge>\n      </div>\n\n      <div className=\"space-y-3 max-h-[500px] overflow-y-auto\">\n        {columns.map((column, index) => (\n          <DraggableColumn\n            key={`${column.name}-${index}`}\n            id={`column-${column.name}`}\n            column={column}\n            onAddToAxis={onAddToAxis}\n            selectedTable={selectedTable}\n            currentAssignments={currentAssignments}\n          />\n        ))}\n      </div>\n    </div>\n  );\n};\n\n// Composant pour les zones de drop organisées\nexport const DropZonesPanel = ({\n  xAxis,\n  yAxis,\n  legend,\n  values,\n  onClearX,\n  onClearY,\n  onClearLegend,\n  onClearValues\n}) => {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n      <DropZone\n        id=\"x-axis\"\n        title=\"Axe X\"\n        subtitle=\"Catégories ou groupes\"\n        icon=\"📊\"\n        acceptedColumn={xAxis}\n        onClear={onClearX}\n      />\n\n      <DropZone\n        id=\"y-axis\"\n        title=\"Axe Y\"\n        subtitle=\"Valeurs numériques (optionnel)\"\n        icon=\"📈\"\n        acceptedColumn={yAxis}\n        onClear={onClearY}\n      />\n\n      <DropZone\n        id=\"legend\"\n        title=\"Légende\"\n        subtitle=\"Sous-catégories (optionnel)\"\n        icon=\"🏷️\"\n        acceptedColumn={legend}\n        onClear={onClearLegend}\n      />\n\n      <DropZone\n        id=\"values\"\n        title=\"Valeurs\"\n        subtitle=\"Données à agréger\"\n        icon=\"💎\"\n        acceptedColumn={values}\n        onClear={onClearValues}\n      />\n    </div>\n  );\n};\n\n// Composant pour une zone de drop compacte dans la barre fixe\nexport const CompactDropZone = ({ id, title, icon, acceptedColumn, onClear, isOver }) => {\n  return (\n    <div\n      className={`\n        flex items-center justify-between p-3 rounded-lg border-2 border-dashed min-h-[60px]\n        transition-all duration-300 ease-in-out transform\n        ${isOver\n          ? 'border-purple-400 bg-purple-600/20 scale-105 drop-zone-glow drop-zone-hover'\n          : 'border-gray-600 bg-gray-800/50 hover:border-gray-500 hover:bg-gray-800/70'\n        }\n        ${acceptedColumn ? 'border-purple-500 bg-purple-600/30 shadow-lg shadow-purple-500/20' : ''}\n        backdrop-blur-sm hover:backdrop-blur-md\n      `}\n    >\n      <div className=\"flex items-center space-x-2 flex-1 min-w-0\">\n        <span className={`text-lg transition-transform duration-200 ${isOver ? 'scale-110' : ''}`}>\n          {icon}\n        </span>\n        <div className=\"min-w-0 flex-1\">\n          <h4 className={`font-medium text-sm truncate transition-colors duration-200 ${\n            isOver ? 'text-purple-200' : 'text-gray-200'\n          }`}>\n            {title}\n          </h4>\n          {acceptedColumn ? (\n            <div className=\"flex items-center space-x-1 mt-1\">\n              <span className=\"text-xs text-purple-300 truncate font-medium\">\n                {acceptedColumn.name}\n              </span>\n              {acceptedColumn.table && (\n                <span className=\"text-xs text-gray-400\">\n                  ({acceptedColumn.table})\n                </span>\n              )}\n            </div>\n          ) : (\n            <p className={`text-xs transition-colors duration-200 ${\n              isOver ? 'text-purple-300' : 'text-gray-400'\n            }`}>\n              {isOver ? 'Relâchez ici' : 'Glissez ici'}\n            </p>\n          )}\n        </div>\n      </div>\n      <div className=\"flex items-center space-x-1\">\n        {acceptedColumn && onClear && (\n          <button\n            onClick={onClear}\n            className=\"text-gray-400 hover:text-red-400 transition-all duration-200 p-1\n                       hover:bg-red-600/20 rounded hover:scale-110 active:scale-95\"\n            title=\"Supprimer\"\n          >\n            ✕\n          </button>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// Barre fixe avec les zones de drop\nexport const FixedDropBar = ({\n  xAxis,\n  yAxis,\n  legend,\n  values,\n  onClearX,\n  onClearY,\n  onClearLegend,\n  onClearValues,\n  isVisible = true,\n  position = 'bottom' // 'top' ou 'bottom'\n}) => {\n  const { isOver: isOverX, setNodeRef: setNodeRefX } = useDroppable({\n    id: 'x-axis-fixed',\n    data: { type: 'dropzone', accepts: ['column'] }\n  });\n\n  const { isOver: isOverY, setNodeRef: setNodeRefY } = useDroppable({\n    id: 'y-axis-fixed',\n    data: { type: 'dropzone', accepts: ['column'] }\n  });\n\n  const { isOver: isOverLegend, setNodeRef: setNodeRefLegend } = useDroppable({\n    id: 'legend-fixed',\n    data: { type: 'dropzone', accepts: ['column'] }\n  });\n\n  const { isOver: isOverValues, setNodeRef: setNodeRefValues } = useDroppable({\n    id: 'values-fixed',\n    data: { type: 'dropzone', accepts: ['column'] }\n  });\n\n  if (!isVisible) return null;\n\n  const positionClasses = position === 'top'\n    ? 'top-0 border-b'\n    : 'bottom-0 border-t';\n\n  const hasAnyColumn = xAxis || yAxis || legend || values;\n\n  return (\n    <div className={`\n      fixed left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-md ${positionClasses} border-gray-700\n      transition-all duration-300 ease-in-out shadow-2xl\n      ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'}\n    `}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 py-3 sm:py-4\">\n        <div className=\"flex items-center justify-between mb-3\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-lg animate-pulse\">🎯</span>\n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-200\">Zones de Drop</h3>\n              {hasAnyColumn && (\n                <p className=\"text-xs text-purple-400\">\n                  {[xAxis, yAxis, legend, values].filter(Boolean).length} colonne(s) assignée(s)\n                </p>\n              )}\n            </div>\n          </div>\n          <div className=\"hidden sm:block text-xs text-gray-400\">\n            Glissez vos colonnes ici pour créer votre visualisation\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3\">\n          <div ref={setNodeRefX}>\n            <CompactDropZone\n              id=\"x-axis-fixed\"\n              title=\"Axe X\"\n              icon=\"📊\"\n              acceptedColumn={xAxis}\n              onClear={onClearX}\n              isOver={isOverX}\n            />\n          </div>\n\n          <div ref={setNodeRefY}>\n            <CompactDropZone\n              id=\"y-axis-fixed\"\n              title=\"Axe Y\"\n              icon=\"📈\"\n              acceptedColumn={yAxis}\n              onClear={onClearY}\n              isOver={isOverY}\n            />\n          </div>\n\n          <div ref={setNodeRefLegend}>\n            <CompactDropZone\n              id=\"legend-fixed\"\n              title=\"Légende\"\n              icon=\"🏷️\"\n              acceptedColumn={legend}\n              onClear={onClearLegend}\n              isOver={isOverLegend}\n            />\n          </div>\n\n          <div ref={setNodeRefValues}>\n            <CompactDropZone\n              id=\"values-fixed\"\n              title=\"Valeurs\"\n              icon=\"💎\"\n              acceptedColumn={values}\n              onClear={onClearValues}\n              isOver={isOverValues}\n            />\n          </div>\n        </div>\n\n        {/* Indicateur mobile */}\n        <div className=\"sm:hidden mt-2 text-center\">\n          <p className=\"text-xs text-gray-500\">\n            Glissez ou utilisez le menu ⚡ des colonnes\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Composant pour le sélecteur de fonction d'agrégation\nexport const AggregationSelector = ({ value, onChange, disabled }) => {\n  const aggregations = [\n    { value: 'SUM', label: 'Somme', icon: '➕', description: 'Addition de toutes les valeurs' },\n    { value: 'AVG', label: 'Moyenne', icon: '📊', description: 'Valeur moyenne' },\n    { value: 'COUNT', label: 'Nombre', icon: '🔢', description: 'Nombre d\\'occurrences' },\n    { value: 'MIN', label: 'Minimum', icon: '⬇️', description: 'Valeur minimale' },\n    { value: 'MAX', label: 'Maximum', icon: '⬆️', description: 'Valeur maximale' }\n  ];\n\n  return (\n    <div>\n      <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n        Fonction d'agrégation\n      </label>\n      <select\n        value={value}\n        onChange={(e) => onChange(e.target.value)}\n        disabled={disabled}\n        className=\"w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-gray-100 focus:border-purple-500 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed\"\n      >\n        {aggregations.map((agg) => (\n          <option key={agg.value} value={agg.value}>\n            {agg.icon} {agg.label} - {agg.description}\n          </option>\n        ))}\n      </select>\n    </div>\n  );\n};\n\n// Composant pour le sélecteur de type de graphique\nexport const ChartTypeSelector = ({ value, onChange }) => {\n  const chartTypes = [\n    { value: 'bar', label: 'Barres', icon: '📊', description: 'Graphique en barres' },\n    { value: 'line', label: 'Ligne', icon: '📈', description: 'Graphique linéaire' },\n    { value: 'pie', label: 'Circulaire', icon: '🥧', description: 'Graphique circulaire' },\n    { value: 'scatter', label: 'Nuage', icon: '⚫', description: 'Nuage de points' },\n    { value: 'stacked_bar', label: 'Barres empilées', icon: '📚', description: 'Barres empilées' }\n  ];\n\n  return (\n    <div>\n      <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n        Type de graphique\n      </label>\n      <div className=\"grid grid-cols-2 md:grid-cols-3 gap-2\">\n        {chartTypes.map((type) => (\n          <button\n            key={type.value}\n            onClick={() => onChange(type.value)}\n            className={`p-3 rounded-lg border transition-colors text-center ${\n              value === type.value\n                ? 'border-purple-500 bg-purple-600/20 text-purple-300'\n                : 'border-gray-700 bg-gray-800/50 text-gray-300 hover:border-gray-600'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">{type.icon}</div>\n            <div className=\"text-xs font-medium\">{type.label}</div>\n          </button>\n        ))}\n      </div>\n    </div>\n  );\n};\n"], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,YAAY,EAAEC,YAAY,QAAQ,eAAe;AAC1D,SAASC,SAAS,QAAQ,gBAAgB;;AAE1C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,aAAa,GAAIC,IAAI,IAAK;EAC9B,QAAQA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,WAAW,CAAC,CAAC;IACzB,KAAK,KAAK;IACV,KAAK,SAAS;IACd,KAAK,QAAQ;IACb,KAAK,UAAU;IACf,KAAK,SAAS;MACZ,OAAO,IAAI;IACb,KAAK,OAAO;IACZ,KAAK,QAAQ;IACb,KAAK,SAAS;IACd,KAAK,SAAS;IACd,KAAK,MAAM;MACT,OAAO,IAAI;IACb,KAAK,SAAS;IACd,KAAK,UAAU;IACf,KAAK,MAAM;IACX,KAAK,OAAO;IACZ,KAAK,MAAM;IACX,KAAK,OAAO;MACV,OAAO,IAAI;IACb,KAAK,UAAU;IACf,KAAK,WAAW;IAChB,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,WAAW;MACd,OAAO,IAAI;IACb,KAAK,KAAK;IACV,KAAK,SAAS;MACZ,OAAO,GAAG;IACZ;MACE,OAAO,IAAI;EACf;AACF,CAAC;AAED,MAAMC,kBAAkB,GAAIF,IAAI,IAAK;EACnC,QAAQA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,WAAW,CAAC,CAAC;IACzB,KAAK,KAAK;IACV,KAAK,SAAS;IACd,KAAK,QAAQ;IACb,KAAK,UAAU;IACf,KAAK,SAAS;MACZ,OAAO,QAAQ;IACjB,KAAK,OAAO;IACZ,KAAK,QAAQ;IACb,KAAK,SAAS;IACd,KAAK,SAAS;IACd,KAAK,MAAM;MACT,OAAO,SAAS;IAClB,KAAK,SAAS;IACd,KAAK,UAAU;IACf,KAAK,MAAM;IACX,KAAK,OAAO;IACZ,KAAK,MAAM;IACX,KAAK,OAAO;MACV,OAAO,OAAO;IAChB,KAAK,UAAU;IACf,KAAK,WAAW;IAChB,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,WAAW;MACd,OAAO,MAAM;IACf,KAAK,KAAK;IACV,KAAK,SAAS;MACZ,OAAO,SAAS;IAClB;MACE,OAAOD,IAAI,IAAI,SAAS;EAC5B;AACF,CAAC;AAED,MAAMG,cAAc,GAAIH,IAAI,IAAK;EAC/B,IAAI,CAACA,IAAI,EAAE,OAAO,8CAA8C;EAEhE,MAAMI,QAAQ,GAAGJ,IAAI,CAACC,WAAW,CAAC,CAAC;EACnC,IAAIG,QAAQ,CAACC,QAAQ,CAAC,KAAK,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,SAAS,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;IACxH,OAAO,8CAA8C;EACvD;EACA,IAAID,QAAQ,CAACC,QAAQ,CAAC,SAAS,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,MAAM,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;IAC1F,OAAO,iDAAiD;EAC1D;EACA,IAAID,QAAQ,CAACC,QAAQ,CAAC,MAAM,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;IAC1D,OAAO,oDAAoD;EAC7D;EACA,OAAO,8CAA8C;AACvD,CAAC;;AAED;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAAC;EAChCC,MAAM;EACNC,OAAO;EACPC,QAAQ;EACRC,MAAM;EACNC,WAAW;EACXC,aAAa;EACbC,kBAAkB,GAAG,CAAC;AACxB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,OAAO,GAAGzB,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAEvDE,SAAS,CAAC,MAAM;IACd,MAAM2B,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIJ,OAAO,CAACK,OAAO,IAAI,CAACL,OAAO,CAACK,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QAC9Dd,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAED,MAAMe,YAAY,GAAIJ,KAAK,IAAK;MAC9B,IAAIA,KAAK,CAACK,GAAG,KAAK,QAAQ,EAAE;QAC1BhB,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAED,IAAID,MAAM,EAAE;MACVkB,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAER,kBAAkB,CAAC;MAC1DO,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEH,YAAY,CAAC;MAClD,OAAO,MAAM;QACXE,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAET,kBAAkB,CAAC;QAC7DO,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEJ,YAAY,CAAC;MACvD,CAAC;IACH;EACF,CAAC,EAAE,CAAChB,MAAM,EAAEC,OAAO,CAAC,CAAC;EAErB,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMqB,SAAS,GAAG,CAChB;IAAEC,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAC9E;IAAEH,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAiB,CAAC,EAC/E;IAAEH,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,mBAAmB;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAkB,CAAC,EACnF;IAAEH,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,qBAAqB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAkB,CAAC,CACrF;EAED,MAAMC,eAAe,GAAG,MAAOC,IAAI,IAAK;IACtC,IAAIlB,YAAY,EAAE;IAElBC,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMkB,eAAe,GAAG;QAAE,GAAGzB,MAAM;QAAE0B,KAAK,EAAExB;MAAc,CAAC;MAC3D,MAAMD,WAAW,CAACuB,IAAI,CAACL,EAAE,EAAEM,eAAe,CAAC;MAC3C3B,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAChE,CAAC,SAAS;MACRpB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,oBACErB,OAAA;IACE2C,GAAG,EAAExB,OAAQ;IACbyB,SAAS,EAAC,kIAC8C;IACxDC,KAAK,EAAE;MACLC,IAAI,EAAEC,IAAI,CAACC,GAAG,CAACnC,QAAQ,CAACoC,CAAC,EAAEC,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC;MACnDC,GAAG,EAAEL,IAAI,CAACC,GAAG,CAACnC,QAAQ,CAACwC,CAAC,EAAEH,MAAM,CAACI,WAAW,GAAG,GAAG,CAAC;MACnDC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,eAEFxD,OAAA;MAAK4C,SAAS,EAAC,KAAK;MAAAY,QAAA,gBAClBxD,OAAA;QAAK4C,SAAS,EAAC,yCAAyC;QAAAY,QAAA,eACtDxD,OAAA;UAAK4C,SAAS,EAAC,6BAA6B;UAAAY,QAAA,gBAC1CxD,OAAA;YAAM4C,SAAS,EAAC,SAAS;YAAAY,QAAA,EAAErD,aAAa,CAACW,MAAM,CAACV,IAAI;UAAC;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7D5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAG4C,SAAS,EAAC,mCAAmC;cAAAY,QAAA,EAAE1C,MAAM,CAAC+C;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClE5D,OAAA;cAAG4C,SAAS,EAAC,kCAAkC;cAAAY,QAAA,EAAE1C,MAAM,CAACV;YAAI;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL5B,SAAS,CAAC8B,GAAG,CAAExB,IAAI,IAAK;QAAA,IAAAyB,qBAAA;QACvB,MAAMC,UAAU,GAAG,EAAAD,qBAAA,GAAA9C,kBAAkB,CAACqB,IAAI,CAACL,EAAE,CAAC,cAAA8B,qBAAA,uBAA3BA,qBAAA,CAA6BF,IAAI,MAAK/C,MAAM,CAAC+C,IAAI;QACpE,oBACE7D,OAAA;UAEEiE,OAAO,EAAEA,CAAA,KAAM5B,eAAe,CAACC,IAAI,CAAE;UACrC4B,QAAQ,EAAE9C,YAAa;UACvBwB,SAAS,EAAE;AACzB;AACA;AACA,kBAAkBoB,UAAU,GACR,8CAA8C,GAC9C,mBAAmB;AACvC,kBACkB5C,YAAY,GAAG,+BAA+B,GAAG,gBAAgB;AACnF,eAAgB;UAAAoC,QAAA,gBAEFxD,OAAA;YAAK4C,SAAS,EAAC,6BAA6B;YAAAY,QAAA,gBAC1CxD,OAAA;cAAM4C,SAAS,EAAC,SAAS;cAAAY,QAAA,EAAElB,IAAI,CAACH;YAAI;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5C5D,OAAA;cAAM4C,SAAS,EAAE,WACfoB,UAAU,GAAG,iBAAiB,GAAG,GAAG1B,IAAI,CAACF,KAAK,4BAA4B,EACzE;cAAAoB,QAAA,EACAlB,IAAI,CAACJ;YAAK;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EACLI,UAAU,iBACThE,OAAA;YAAM4C,SAAS,EAAC,yBAAyB;YAAAY,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAClD;QAAA,GAvBItB,IAAI,CAACL,EAAE;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwBN,CAAC;MAEb,CAAC,CAAC,eAEF5D,OAAA;QAAK4C,SAAS,EAAC,oCAAoC;QAAAY,QAAA,eACjDxD,OAAA;UAAG4C,SAAS,EAAC,iCAAiC;UAAAY,QAAA,EAAC;QAE/C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA1C,EAAA,CA3HaR,iBAAiB;AAAAyD,EAAA,GAAjBzD,iBAAiB;AA4H9B,OAAO,MAAM0D,eAAe,GAAGA,CAAC;EAAEtD,MAAM;EAAEmB,EAAE;EAAElB,WAAW;EAAEC,aAAa;EAAEC;AAAmB,CAAC,KAAK;EAAAoD,GAAA;EACjG,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG9E,QAAQ,CAAC;IAAEkB,MAAM,EAAE,KAAK;IAAEE,QAAQ,EAAE;MAAEoC,CAAC,EAAE,CAAC;MAAEI,CAAC,EAAE;IAAE;EAAE,CAAC,CAAC;EAE3F,MAAM;IAAEmB,UAAU;IAAEC,SAAS;IAAEC,UAAU;IAAEC,SAAS;IAAEC;EAAW,CAAC,GAAGhF,YAAY,CAAC;IAChFqC,EAAE,EAAEA,EAAE;IACN4C,IAAI,EAAE;MACJ/D,MAAM,EAAEA,MAAM;MACdV,IAAI,EAAE;IACR;EACF,CAAC,CAAC;EAEF,MAAMyC,KAAK,GAAG8B,SAAS,GAAG;IACxBA,SAAS,EAAE,eAAeA,SAAS,CAAC1B,CAAC,OAAO0B,SAAS,CAACtB,CAAC,QAAQ;IAC/DyB,OAAO,EAAEF,UAAU,GAAG,GAAG,GAAG,CAAC;IAC7BG,MAAM,EAAEH,UAAU,GAAG,IAAI,GAAG;EAC9B,CAAC,GAAGI,SAAS;EAEb,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBZ,cAAc,CAAC;MACb5D,MAAM,EAAE,IAAI;MACZE,QAAQ,EAAE;QAAEoC,CAAC,EAAEiC,CAAC,CAACE,OAAO;QAAE/B,CAAC,EAAE6B,CAAC,CAACG;MAAQ;IACzC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAIJ,CAAC,IAAK;IAC5BA,CAAC,CAACK,eAAe,CAAC,CAAC;IACnBhB,cAAc,CAAC;MACb5D,MAAM,EAAE,IAAI;MACZE,QAAQ,EAAE;QACRoC,CAAC,EAAEiC,CAAC,CAACM,aAAa,CAACC,qBAAqB,CAAC,CAAC,CAACC,KAAK,GAAG,EAAE;QACrDrC,CAAC,EAAE6B,CAAC,CAACM,aAAa,CAACC,qBAAqB,CAAC,CAAC,CAACrC;MAC7C;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMY,UAAU,GAAG/C,kBAAkB,IAAI0E,MAAM,CAACC,MAAM,CAAC3E,kBAAkB,CAAC,CAAC4E,IAAI,CAC7EC,UAAU,IAAI,CAAAA,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEjC,IAAI,MAAK/C,MAAM,CAAC+C,IAC5C,CAAC;;EAED;EACA,MAAMkC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAAC9E,kBAAkB,EAAE,OAAO,IAAI;IAEpC,MAAM+E,WAAW,GAAG,EAAE;IACtBL,MAAM,CAACM,OAAO,CAAChF,kBAAkB,CAAC,CAACiF,OAAO,CAAC,CAAC,CAACtE,GAAG,EAAEuE,KAAK,CAAC,KAAK;MAC3D,IAAI,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEtC,IAAI,MAAK/C,MAAM,CAAC+C,IAAI,EAAE;QAC/B,MAAMuC,MAAM,GAAG;UACb,QAAQ,EAAE,GAAG;UACb,QAAQ,EAAE,GAAG;UACb,QAAQ,EAAE,GAAG;UACb,QAAQ,EAAE;QACZ,CAAC;QACDJ,WAAW,CAACK,IAAI,CAACD,MAAM,CAACxE,GAAG,CAAC,IAAIA,GAAG,CAAC;MACtC;IACF,CAAC,CAAC;IACF,OAAOoE,WAAW,CAACM,MAAM,GAAG,CAAC,GAAGN,WAAW,CAACO,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;EAC/D,CAAC;EAED,MAAMpG,aAAa,GAAIC,IAAI,IAAK;IAC9B,MAAMI,QAAQ,GAAGJ,IAAI,CAACC,WAAW,CAAC,CAAC;IACnC,IAAIG,QAAQ,CAACC,QAAQ,CAAC,KAAK,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,SAAS,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MACxH,OAAO,IAAI;IACb;IACA,IAAID,QAAQ,CAACC,QAAQ,CAAC,SAAS,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,MAAM,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1F,OAAO,IAAI;IACb;IACA,IAAID,QAAQ,CAACC,QAAQ,CAAC,MAAM,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1D,OAAO,IAAI;IACb;IACA,IAAID,QAAQ,CAACC,QAAQ,CAAC,KAAK,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,SAAS,CAAC,EAAE;MAC5D,OAAO,IAAI;IACb;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMF,cAAc,GAAIH,IAAI,IAAK;IAC/B,MAAMI,QAAQ,GAAGJ,IAAI,CAACC,WAAW,CAAC,CAAC;IACnC,IAAIG,QAAQ,CAACC,QAAQ,CAAC,KAAK,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,SAAS,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MACxH,OAAO,8CAA8C;IACvD;IACA,IAAID,QAAQ,CAACC,QAAQ,CAAC,SAAS,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,MAAM,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1F,OAAO,iDAAiD;IAC1D;IACA,IAAID,QAAQ,CAACC,QAAQ,CAAC,MAAM,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1D,OAAO,oDAAoD;IAC7D;IACA,OAAO,8CAA8C;EACvD,CAAC;EAED,oBACET,OAAA,CAAAE,SAAA;IAAAsD,QAAA,gBACExD,OAAA;MACE2C,GAAG,EAAE+B,UAAW;MAChB7B,KAAK,EAAEA,KAAM;MAAA,GACT4B,SAAS;MAAA,GACTD,UAAU;MACdgC,aAAa,EAAEvB,iBAAkB;MACjCrC,SAAS,EAAE;AACnB;AACA;AACA,YAAYrC,cAAc,CAACO,MAAM,CAACV,IAAI,CAAC;AACvC,YAAYwE,UAAU,GAAG,iDAAiD,GAAG,4CAA4C;AACzH,YAAYZ,UAAU,GAAG,4CAA4C,GAAG,EAAE;AAC1E;AACA,SAAU;MAAAR,QAAA,eAEFxD,OAAA;QAAK4C,SAAS,EAAC,mCAAmC;QAAAY,QAAA,gBAChDxD,OAAA;UAAK4C,SAAS,EAAC,4CAA4C;UAAAY,QAAA,gBACzDxD,OAAA;YAAM4C,SAAS,EAAE,6CACfgC,UAAU,GAAG,WAAW,GAAG,uBAAuB,EACjD;YAAApB,QAAA,EACArD,aAAa,CAACW,MAAM,CAACV,IAAI;UAAC;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACP5D,OAAA;YAAK4C,SAAS,EAAC,gBAAgB;YAAAY,QAAA,gBAC7BxD,OAAA;cAAK4C,SAAS,EAAC,6BAA6B;cAAAY,QAAA,gBAC1CxD,OAAA;gBAAG4C,SAAS,EAAE,uDACZgC,UAAU,GAAG,iBAAiB,GAAG,2BAA2B,EAC3D;gBAAApB,QAAA,EACA1C,MAAM,CAAC+C;cAAI;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,EACHI,UAAU,iBACThE,OAAA;gBAAM4C,SAAS,EAAC,iGAAiG;gBAAAY,QAAA,EAC9GuC,iBAAiB,CAAC;cAAC;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN5D,OAAA;cAAG4C,SAAS,EAAE,uCACZgC,UAAU,GAAG,4BAA4B,GAAG,mCAAmC,EAC9E;cAAApB,QAAA,EACA1C,MAAM,CAACV;YAAI;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL7C,WAAW,iBACVf,OAAA;UACEiE,OAAO,EAAEqB,cAAe;UACxB1C,SAAS,EAAC,2KAE2C;UACrD6D,KAAK,EAAC,oBAAoB;UAAAjD,QAAA,EAC3B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA,CAACU,iBAAiB;MAChBC,MAAM,EAAE2D,WAAW,CAAC3D,MAAO;MAC3BC,OAAO,EAAEA,CAAA,KAAM2D,cAAc,CAAC;QAAE,GAAGD,WAAW;QAAE3D,MAAM,EAAE;MAAM,CAAC,CAAE;MACjEE,QAAQ,EAAEyD,WAAW,CAACzD,QAAS;MAC/BC,MAAM,EAAEA,MAAO;MACfC,WAAW,EAAEA,WAAY;MACzBC,aAAa,EAAEA,aAAc;MAC7BC,kBAAkB,EAAEA;IAAmB;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC;EAAA,eACF,CAAC;AAEP,CAAC;;AAED;AAAAS,GAAA,CArKaD,eAAe;EAAA,QAG2CxE,YAAY;AAAA;AAAA8G,GAAA,GAHtEtC,eAAe;AAsK5B,OAAO,MAAMuC,QAAQ,GAAGA,CAAC;EAAE1E,EAAE;EAAEwE,KAAK;EAAEG,QAAQ;EAAEzE,IAAI;EAAEqB,QAAQ;EAAEqD,cAAc;EAAEC;AAAQ,CAAC,KAAK;EAAAC,GAAA;EAC5F,MAAM;IAAEC,MAAM;IAAEtC;EAAW,CAAC,GAAG7E,YAAY,CAAC;IAC1CoC,EAAE,EAAEA,EAAE;IACN4C,IAAI,EAAE;MACJzE,IAAI,EAAE,UAAU;MAChB6G,OAAO,EAAE,CAAC,QAAQ;IACpB;EACF,CAAC,CAAC;EAEF,oBACEjH,OAAA;IACE2C,GAAG,EAAE+B,UAAW;IAChB9B,SAAS,EAAE;AACjB;AACA,UAAUoE,MAAM,GACJ,8CAA8C,GAC9C,gCAAgC;AAC5C,UACUH,cAAc,GAAG,oCAAoC,GAAG,EAAE;AACpE,OAAQ;IAAArD,QAAA,gBAGFxD,OAAA;MAAK4C,SAAS,EAAC,wCAAwC;MAAAY,QAAA,gBACrDxD,OAAA;QAAK4C,SAAS,EAAC,6BAA6B;QAAAY,QAAA,gBAC1CxD,OAAA;UAAM4C,SAAS,EAAC,SAAS;UAAAY,QAAA,EAAErB;QAAI;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvC5D,OAAA;UAAAwD,QAAA,gBACExD,OAAA;YAAI4C,SAAS,EAAC,2BAA2B;YAAAY,QAAA,EAAEiD;UAAK;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EACrDgD,QAAQ,iBAAI5G,OAAA;YAAG4C,SAAS,EAAC,uBAAuB;YAAAY,QAAA,EAAEoD;UAAQ;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACLiD,cAAc,IAAIC,OAAO,iBACxB9G,OAAA;QACEiE,OAAO,EAAE6C,OAAQ;QACjBlE,SAAS,EAAC,oDAAoD;QAC9D6D,KAAK,EAAC,WAAW;QAAAjD,QAAA,EAClB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN5D,OAAA;MAAK4C,SAAS,EAAC,+CAA+C;MAAAY,QAAA,EAC3DqD,cAAc,gBACb7G,OAAA;QAAK4C,SAAS,EAAC,QAAQ;QAAAY,QAAA,eACrBxD,OAAA;UAAK4C,SAAS,EAAC,sFAAsF;UAAAY,QAAA,gBACnGxD,OAAA;YAAM4C,SAAS,EAAC,SAAS;YAAAY,QAAA,EACtBqD,cAAc,CAACzG,IAAI,CAACC,WAAW,CAAC,CAAC,CAACI,QAAQ,CAAC,KAAK,CAAC,IACjDoG,cAAc,CAACzG,IAAI,CAACC,WAAW,CAAC,CAAC,CAACI,QAAQ,CAAC,SAAS,CAAC,IACrDoG,cAAc,CAACzG,IAAI,CAACC,WAAW,CAAC,CAAC,CAACI,QAAQ,CAAC,OAAO,CAAC,GAAG,IAAI,GAC1DoG,cAAc,CAACzG,IAAI,CAACC,WAAW,CAAC,CAAC,CAACI,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG;UAAI;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACP5D,OAAA;YAAK4C,SAAS,EAAC,gBAAgB;YAAAY,QAAA,gBAC7BxD,OAAA;cAAG4C,SAAS,EAAC,sCAAsC;cAAAY,QAAA,EAAEqD,cAAc,CAAChD;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7E5D,OAAA;cAAG4C,SAAS,EAAC,yBAAyB;cAAAY,QAAA,EAAEqD,cAAc,CAACzG;YAAI;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC/DiD,cAAc,CAACrE,KAAK,iBACnBxC,OAAA;cAAG4C,SAAS,EAAC,8BAA8B;cAAAY,QAAA,GAAC,eAAG,EAACqD,cAAc,CAACrE,KAAK;YAAA;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACzE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAEN5D,OAAA;QAAK4C,SAAS,EAAC,aAAa;QAAAY,QAAA,eAC1BxD,OAAA;UAAG4C,SAAS,EAAC,uBAAuB;UAAAY,QAAA,EACjCwD,MAAM,GAAG,cAAc,GAAG;QAAyB;UAAAvD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELJ,QAAQ;EAAA;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AAAAmD,GAAA,CA3EaJ,QAAQ;EAAA,QACY9G,YAAY;AAAA;AAAAqH,GAAA,GADhCP,QAAQ;AA4ErB,OAAO,MAAMQ,YAAY,GAAGA,CAAC;EAAEC,OAAO;EAAEX,KAAK;EAAE1F,WAAW;EAAEC,aAAa;EAAEC;AAAmB,CAAC,KAAK;EAClG,IAAI,CAACmG,OAAO,IAAIA,OAAO,CAACd,MAAM,KAAK,CAAC,EAAE;IACpC,oBACEtG,OAAA;MAAK4C,SAAS,EAAC,kBAAkB;MAAAY,QAAA,gBAC/BxD,OAAA;QAAK4C,SAAS,EAAC,iFAAiF;QAAAY,QAAA,eAC9FxD,OAAA;UAAM4C,SAAS,EAAC,UAAU;UAAAY,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACN5D,OAAA;QAAG4C,SAAS,EAAC,eAAe;QAAAY,QAAA,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD,CAAC;EAEV;EAEA,oBACE5D,OAAA;IAAAwD,QAAA,gBACExD,OAAA;MAAK4C,SAAS,EAAC,wCAAwC;MAAAY,QAAA,gBACrDxD,OAAA;QAAI4C,SAAS,EAAC,qCAAqC;QAAAY,QAAA,EAAEiD;MAAK;QAAAhD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAChE5D,OAAA,CAACF,SAAS;QAACuH,OAAO,EAAC,MAAM;QAAA7D,QAAA,EAAE4D,OAAO,CAACd;MAAM;QAAA7C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC,eAEN5D,OAAA;MAAK4C,SAAS,EAAC,yCAAyC;MAAAY,QAAA,EACrD4D,OAAO,CAACtD,GAAG,CAAC,CAAChD,MAAM,EAAEwG,KAAK,kBACzBtH,OAAA,CAACoE,eAAe;QAEdnC,EAAE,EAAE,UAAUnB,MAAM,CAAC+C,IAAI,EAAG;QAC5B/C,MAAM,EAAEA,MAAO;QACfC,WAAW,EAAEA,WAAY;QACzBC,aAAa,EAAEA,aAAc;QAC7BC,kBAAkB,EAAEA;MAAmB,GALlC,GAAGH,MAAM,CAAC+C,IAAI,IAAIyD,KAAK,EAAE;QAAA7D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAM/B,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA2D,GAAA,GAnCaJ,YAAY;AAoCzB,OAAO,MAAMK,cAAc,GAAGA,CAAC;EAC7BC,KAAK;EACLC,KAAK;EACLC,MAAM;EACN/B,MAAM;EACNgC,QAAQ;EACRC,QAAQ;EACRC,aAAa;EACbC;AACF,CAAC,KAAK;EACJ,oBACE/H,OAAA;IAAK4C,SAAS,EAAC,uCAAuC;IAAAY,QAAA,gBACpDxD,OAAA,CAAC2G,QAAQ;MACP1E,EAAE,EAAC,QAAQ;MACXwE,KAAK,EAAC,OAAO;MACbG,QAAQ,EAAC,0BAAuB;MAChCzE,IAAI,EAAC,cAAI;MACT0E,cAAc,EAAEY,KAAM;MACtBX,OAAO,EAAEc;IAAS;MAAAnE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eAEF5D,OAAA,CAAC2G,QAAQ;MACP1E,EAAE,EAAC,QAAQ;MACXwE,KAAK,EAAC,OAAO;MACbG,QAAQ,EAAC,mCAAgC;MACzCzE,IAAI,EAAC,cAAI;MACT0E,cAAc,EAAEa,KAAM;MACtBZ,OAAO,EAAEe;IAAS;MAAApE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eAEF5D,OAAA,CAAC2G,QAAQ;MACP1E,EAAE,EAAC,QAAQ;MACXwE,KAAK,EAAC,YAAS;MACfG,QAAQ,EAAC,gCAA6B;MACtCzE,IAAI,EAAC,oBAAK;MACV0E,cAAc,EAAEc,MAAO;MACvBb,OAAO,EAAEgB;IAAc;MAAArE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAEF5D,OAAA,CAAC2G,QAAQ;MACP1E,EAAE,EAAC,QAAQ;MACXwE,KAAK,EAAC,SAAS;MACfG,QAAQ,EAAC,4BAAmB;MAC5BzE,IAAI,EAAC,cAAI;MACT0E,cAAc,EAAEjB,MAAO;MACvBkB,OAAO,EAAEiB;IAAc;MAAAtE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;;AAED;AAAAoE,GAAA,GAnDaR,cAAc;AAoD3B,OAAO,MAAMS,eAAe,GAAGA,CAAC;EAAEhG,EAAE;EAAEwE,KAAK;EAAEtE,IAAI;EAAE0E,cAAc;EAAEC,OAAO;EAAEE;AAAO,CAAC,KAAK;EACvF,oBACEhH,OAAA;IACE4C,SAAS,EAAE;AACjB;AACA;AACA,UAAUoE,MAAM,GACJ,6EAA6E,GAC7E,2EAA2E;AACvF,UACUH,cAAc,GAAG,mEAAmE,GAAG,EAAE;AACnG;AACA,OAAQ;IAAArD,QAAA,gBAEFxD,OAAA;MAAK4C,SAAS,EAAC,4CAA4C;MAAAY,QAAA,gBACzDxD,OAAA;QAAM4C,SAAS,EAAE,6CAA6CoE,MAAM,GAAG,WAAW,GAAG,EAAE,EAAG;QAAAxD,QAAA,EACvFrB;MAAI;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACP5D,OAAA;QAAK4C,SAAS,EAAC,gBAAgB;QAAAY,QAAA,gBAC7BxD,OAAA;UAAI4C,SAAS,EAAE,+DACboE,MAAM,GAAG,iBAAiB,GAAG,eAAe,EAC3C;UAAAxD,QAAA,EACAiD;QAAK;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EACJiD,cAAc,gBACb7G,OAAA;UAAK4C,SAAS,EAAC,kCAAkC;UAAAY,QAAA,gBAC/CxD,OAAA;YAAM4C,SAAS,EAAC,8CAA8C;YAAAY,QAAA,EAC3DqD,cAAc,CAAChD;UAAI;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,EACNiD,cAAc,CAACrE,KAAK,iBACnBxC,OAAA;YAAM4C,SAAS,EAAC,uBAAuB;YAAAY,QAAA,GAAC,GACrC,EAACqD,cAAc,CAACrE,KAAK,EAAC,GACzB;UAAA;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAEN5D,OAAA;UAAG4C,SAAS,EAAE,0CACZoE,MAAM,GAAG,iBAAiB,GAAG,eAAe,EAC3C;UAAAxD,QAAA,EACAwD,MAAM,GAAG,cAAc,GAAG;QAAa;UAAAvD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN5D,OAAA;MAAK4C,SAAS,EAAC,6BAA6B;MAAAY,QAAA,EACzCqD,cAAc,IAAIC,OAAO,iBACxB9G,OAAA;QACEiE,OAAO,EAAE6C,OAAQ;QACjBlE,SAAS,EAAC,8HAC6D;QACvE6D,KAAK,EAAC,WAAW;QAAAjD,QAAA,EAClB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAsE,GAAA,GA5DaD,eAAe;AA6D5B,OAAO,MAAME,YAAY,GAAGA,CAAC;EAC3BV,KAAK;EACLC,KAAK;EACLC,MAAM;EACN/B,MAAM;EACNgC,QAAQ;EACRC,QAAQ;EACRC,aAAa;EACbC,aAAa;EACbK,SAAS,GAAG,IAAI;EAChBvH,QAAQ,GAAG,QAAQ,CAAC;AACtB,CAAC,KAAK;EAAAwH,GAAA;EACJ,MAAM;IAAErB,MAAM,EAAEsB,OAAO;IAAE5D,UAAU,EAAE6D;EAAY,CAAC,GAAG1I,YAAY,CAAC;IAChEoC,EAAE,EAAE,cAAc;IAClB4C,IAAI,EAAE;MAAEzE,IAAI,EAAE,UAAU;MAAE6G,OAAO,EAAE,CAAC,QAAQ;IAAE;EAChD,CAAC,CAAC;EAEF,MAAM;IAAED,MAAM,EAAEwB,OAAO;IAAE9D,UAAU,EAAE+D;EAAY,CAAC,GAAG5I,YAAY,CAAC;IAChEoC,EAAE,EAAE,cAAc;IAClB4C,IAAI,EAAE;MAAEzE,IAAI,EAAE,UAAU;MAAE6G,OAAO,EAAE,CAAC,QAAQ;IAAE;EAChD,CAAC,CAAC;EAEF,MAAM;IAAED,MAAM,EAAE0B,YAAY;IAAEhE,UAAU,EAAEiE;EAAiB,CAAC,GAAG9I,YAAY,CAAC;IAC1EoC,EAAE,EAAE,cAAc;IAClB4C,IAAI,EAAE;MAAEzE,IAAI,EAAE,UAAU;MAAE6G,OAAO,EAAE,CAAC,QAAQ;IAAE;EAChD,CAAC,CAAC;EAEF,MAAM;IAAED,MAAM,EAAE4B,YAAY;IAAElE,UAAU,EAAEmE;EAAiB,CAAC,GAAGhJ,YAAY,CAAC;IAC1EoC,EAAE,EAAE,cAAc;IAClB4C,IAAI,EAAE;MAAEzE,IAAI,EAAE,UAAU;MAAE6G,OAAO,EAAE,CAAC,QAAQ;IAAE;EAChD,CAAC,CAAC;EAEF,IAAI,CAACmB,SAAS,EAAE,OAAO,IAAI;EAE3B,MAAMU,eAAe,GAAGjI,QAAQ,KAAK,KAAK,GACtC,gBAAgB,GAChB,mBAAmB;EAEvB,MAAMkI,YAAY,GAAGtB,KAAK,IAAIC,KAAK,IAAIC,MAAM,IAAI/B,MAAM;EAEvD,oBACE5F,OAAA;IAAK4C,SAAS,EAAE;AACpB,kEAAkEkG,eAAe;AACjF;AACA,QAAQV,SAAS,GAAG,2BAA2B,GAAG,4BAA4B;AAC9E,KAAM;IAAA5E,QAAA,eACAxD,OAAA;MAAK4C,SAAS,EAAC,6CAA6C;MAAAY,QAAA,gBAC1DxD,OAAA;QAAK4C,SAAS,EAAC,wCAAwC;QAAAY,QAAA,gBACrDxD,OAAA;UAAK4C,SAAS,EAAC,6BAA6B;UAAAY,QAAA,gBAC1CxD,OAAA;YAAM4C,SAAS,EAAC,uBAAuB;YAAAY,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjD5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAI4C,SAAS,EAAC,qCAAqC;cAAAY,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACrEmF,YAAY,iBACX/I,OAAA;cAAG4C,SAAS,EAAC,yBAAyB;cAAAY,QAAA,GACnC,CAACiE,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAE/B,MAAM,CAAC,CAACoD,MAAM,CAACC,OAAO,CAAC,CAAC3C,MAAM,EAAC,4BACzD;YAAA;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN5D,OAAA;UAAK4C,SAAS,EAAC,uCAAuC;UAAAY,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5D,OAAA;QAAK4C,SAAS,EAAC,gDAAgD;QAAAY,QAAA,gBAC7DxD,OAAA;UAAK2C,GAAG,EAAE4F,WAAY;UAAA/E,QAAA,eACpBxD,OAAA,CAACiI,eAAe;YACdhG,EAAE,EAAC,cAAc;YACjBwE,KAAK,EAAC,OAAO;YACbtE,IAAI,EAAC,cAAI;YACT0E,cAAc,EAAEY,KAAM;YACtBX,OAAO,EAAEc,QAAS;YAClBZ,MAAM,EAAEsB;UAAQ;YAAA7E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN5D,OAAA;UAAK2C,GAAG,EAAE8F,WAAY;UAAAjF,QAAA,eACpBxD,OAAA,CAACiI,eAAe;YACdhG,EAAE,EAAC,cAAc;YACjBwE,KAAK,EAAC,OAAO;YACbtE,IAAI,EAAC,cAAI;YACT0E,cAAc,EAAEa,KAAM;YACtBZ,OAAO,EAAEe,QAAS;YAClBb,MAAM,EAAEwB;UAAQ;YAAA/E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN5D,OAAA;UAAK2C,GAAG,EAAEgG,gBAAiB;UAAAnF,QAAA,eACzBxD,OAAA,CAACiI,eAAe;YACdhG,EAAE,EAAC,cAAc;YACjBwE,KAAK,EAAC,YAAS;YACftE,IAAI,EAAC,oBAAK;YACV0E,cAAc,EAAEc,MAAO;YACvBb,OAAO,EAAEgB,aAAc;YACvBd,MAAM,EAAE0B;UAAa;YAAAjF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN5D,OAAA;UAAK2C,GAAG,EAAEkG,gBAAiB;UAAArF,QAAA,eACzBxD,OAAA,CAACiI,eAAe;YACdhG,EAAE,EAAC,cAAc;YACjBwE,KAAK,EAAC,SAAS;YACftE,IAAI,EAAC,cAAI;YACT0E,cAAc,EAAEjB,MAAO;YACvBkB,OAAO,EAAEiB,aAAc;YACvBf,MAAM,EAAE4B;UAAa;YAAAnF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5D,OAAA;QAAK4C,SAAS,EAAC,4BAA4B;QAAAY,QAAA,eACzCxD,OAAA;UAAG4C,SAAS,EAAC,uBAAuB;UAAAY,QAAA,EAAC;QAErC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAyE,GAAA,CAzHaF,YAAY;EAAA,QAY8BtI,YAAY,EAKZA,YAAY,EAKFA,YAAY,EAKZA,YAAY;AAAA;AAAAqJ,GAAA,GA3BhEf,YAAY;AA0HzB,OAAO,MAAMgB,mBAAmB,GAAGA,CAAC;EAAEhD,KAAK;EAAEiD,QAAQ;EAAElF;AAAS,CAAC,KAAK;EACpE,MAAMmF,YAAY,GAAG,CACnB;IAAElD,KAAK,EAAE,KAAK;IAAEjE,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,GAAG;IAAEmH,WAAW,EAAE;EAAiC,CAAC,EAC1F;IAAEnD,KAAK,EAAE,KAAK;IAAEjE,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,IAAI;IAAEmH,WAAW,EAAE;EAAiB,CAAC,EAC7E;IAAEnD,KAAK,EAAE,OAAO;IAAEjE,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE,IAAI;IAAEmH,WAAW,EAAE;EAAwB,CAAC,EACrF;IAAEnD,KAAK,EAAE,KAAK;IAAEjE,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,IAAI;IAAEmH,WAAW,EAAE;EAAkB,CAAC,EAC9E;IAAEnD,KAAK,EAAE,KAAK;IAAEjE,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,IAAI;IAAEmH,WAAW,EAAE;EAAkB,CAAC,CAC/E;EAED,oBACEtJ,OAAA;IAAAwD,QAAA,gBACExD,OAAA;MAAO4C,SAAS,EAAC,8CAA8C;MAAAY,QAAA,EAAC;IAEhE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACR5D,OAAA;MACEmG,KAAK,EAAEA,KAAM;MACbiD,QAAQ,EAAGlE,CAAC,IAAKkE,QAAQ,CAAClE,CAAC,CAACxD,MAAM,CAACyE,KAAK,CAAE;MAC1CjC,QAAQ,EAAEA,QAAS;MACnBtB,SAAS,EAAC,mKAAmK;MAAAY,QAAA,EAE5K6F,YAAY,CAACvF,GAAG,CAAEyF,GAAG,iBACpBvJ,OAAA;QAAwBmG,KAAK,EAAEoD,GAAG,CAACpD,KAAM;QAAA3C,QAAA,GACtC+F,GAAG,CAACpH,IAAI,EAAC,GAAC,EAACoH,GAAG,CAACrH,KAAK,EAAC,KAAG,EAACqH,GAAG,CAACD,WAAW;MAAA,GAD9BC,GAAG,CAACpD,KAAK;QAAA1C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEd,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AAAA4F,GAAA,GA9BaL,mBAAmB;AA+BhC,OAAO,MAAMM,iBAAiB,GAAGA,CAAC;EAAEtD,KAAK;EAAEiD;AAAS,CAAC,KAAK;EACxD,MAAMM,UAAU,GAAG,CACjB;IAAEvD,KAAK,EAAE,KAAK;IAAEjE,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE,IAAI;IAAEmH,WAAW,EAAE;EAAsB,CAAC,EACjF;IAAEnD,KAAK,EAAE,MAAM;IAAEjE,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,IAAI;IAAEmH,WAAW,EAAE;EAAqB,CAAC,EAChF;IAAEnD,KAAK,EAAE,KAAK;IAAEjE,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE,IAAI;IAAEmH,WAAW,EAAE;EAAuB,CAAC,EACtF;IAAEnD,KAAK,EAAE,SAAS;IAAEjE,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,GAAG;IAAEmH,WAAW,EAAE;EAAkB,CAAC,EAC/E;IAAEnD,KAAK,EAAE,aAAa;IAAEjE,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,IAAI;IAAEmH,WAAW,EAAE;EAAkB,CAAC,CAC/F;EAED,oBACEtJ,OAAA;IAAAwD,QAAA,gBACExD,OAAA;MAAO4C,SAAS,EAAC,8CAA8C;MAAAY,QAAA,EAAC;IAEhE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACR5D,OAAA;MAAK4C,SAAS,EAAC,uCAAuC;MAAAY,QAAA,EACnDkG,UAAU,CAAC5F,GAAG,CAAE1D,IAAI,iBACnBJ,OAAA;QAEEiE,OAAO,EAAEA,CAAA,KAAMmF,QAAQ,CAAChJ,IAAI,CAAC+F,KAAK,CAAE;QACpCvD,SAAS,EAAE,uDACTuD,KAAK,KAAK/F,IAAI,CAAC+F,KAAK,GAChB,oDAAoD,GACpD,oEAAoE,EACvE;QAAA3C,QAAA,gBAEHxD,OAAA;UAAK4C,SAAS,EAAC,cAAc;UAAAY,QAAA,EAAEpD,IAAI,CAAC+B;QAAI;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/C5D,OAAA;UAAK4C,SAAS,EAAC,qBAAqB;UAAAY,QAAA,EAAEpD,IAAI,CAAC8B;QAAK;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,GATlDxD,IAAI,CAAC+F,KAAK;QAAA1C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUT,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC+F,GAAA,GAhCWF,iBAAiB;AAAA,IAAAtF,EAAA,EAAAuC,GAAA,EAAAQ,GAAA,EAAAK,GAAA,EAAAS,GAAA,EAAAE,GAAA,EAAAgB,GAAA,EAAAM,GAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAzF,EAAA;AAAAyF,YAAA,CAAAlD,GAAA;AAAAkD,YAAA,CAAA1C,GAAA;AAAA0C,YAAA,CAAArC,GAAA;AAAAqC,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAA1B,GAAA;AAAA0B,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}