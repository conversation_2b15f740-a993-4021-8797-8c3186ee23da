{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_pluginSyntaxTypescript", "_helperCreateClassFeaturesPlugin", "_constEnum", "_enum", "_globalTypes", "_namespace", "isInType", "path", "parent", "type", "parentPath", "findParent", "exportKind", "NEEDS_EXPLICIT_ESM", "WeakMap", "PARSED_PARAMS", "WeakSet", "safeRemove", "ids", "getBindingIdentifiers", "name", "Object", "keys", "binding", "scope", "getBinding", "identifier", "removeBinding", "opts", "noScope", "remove", "assertCjsTransformEnabled", "pass", "wrong", "suggestion", "extra", "file", "get", "buildCodeFrameError", "_default", "exports", "default", "declare", "api", "types", "t", "template", "assertVersion", "JSX_PRAGMA_REGEX", "allowNamespaces", "jsxPragma", "jsxPragmaFrag", "onlyRemoveTypeImports", "optimizeConstEnums", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "classMemberVisitors", "field", "node", "value", "decorators", "definite", "isClassPrivateProperty", "abstract", "accessibility", "readonly", "optional", "typeAnnotation", "override", "method", "constructor", "classPath", "assigns", "<PERSON><PERSON><PERSON><PERSON>", "param", "parameter", "has", "add", "id", "isIdentifier", "isAssignmentPattern", "left", "push", "statement", "ast", "cloneNode", "replaceWith", "registerBinding", "injectInitialization", "inherits", "syntaxTypeScript", "visitor", "Pattern", "visitPattern", "Identifier", "RestElement", "Program", "enter", "state", "fileJsxPragma", "fileJsxPragmaFrag", "programScope", "GLOBAL_TYPES", "set", "Set", "comments", "comment", "jsxMatches", "exec", "pragmaImportName", "split", "pragmaFragImportName", "stmt", "isImportDeclaration", "program", "importKind", "specifier", "specifiers", "registerGlobalType", "local", "importsToRemove", "specifiersLength", "length", "isAllSpecifiersElided", "size", "isImportTypeOnly", "programPath", "importPath", "isTSImportEqualsDeclaration", "isExport", "isExportDeclaration", "isVariableDeclaration", "isTSTypeAliasDeclaration", "isTSDeclareFunction", "isTSInterfaceDeclaration", "isClassDeclaration", "isTSEnumDeclaration", "isTSModuleDeclaration", "exit", "sourceType", "pushContainer", "exportNamedDeclaration", "ExportNamedDeclaration", "source", "every", "isExportSpecifier", "isGlobalType", "declaration", "namespace", "isStringLiteral", "getFirstIdentifier", "hasOwnBinding", "newExport", "replaceWithMultiple", "variableDeclaration", "variableDeclarator", "registerDeclaration", "ExportAllDeclaration", "ExportSpecifier", "ExportDefaultDeclaration", "TSDeclareFunction", "TSDeclareMethod", "VariableDeclaration", "VariableDeclarator", "TSIndexSignature", "ClassDeclaration", "Class", "typeParameters", "superTypeParameters", "implements", "for<PERSON>ach", "child", "isClassMethod", "isClassPrivateMethod", "kind", "isClassProperty", "isClassAccessorProperty", "Function", "returnType", "params", "shift", "TSModuleDeclaration", "transpileNamespace", "TSInterfaceDeclaration", "TSTypeAliasDeclaration", "TSEnumDeclaration", "const", "transpileConstEnum", "transpileEnum", "TSImportEqualsDeclaration", "moduleReference", "init", "<PERSON><PERSON><PERSON>", "isTSExternalModuleReference", "callExpression", "expression", "entityNameToExpr", "newNode", "TSExportAssignment", "TSTypeAssertion", "tsSatisfiesExpression", "isTSAsExpression", "isTSSatisfiesExpression", "tsInstantiationExpression", "CallExpression", "OptionalCallExpression", "NewExpression", "JSXOpeningElement", "TaggedTemplateExpression", "isTSQualifiedName", "memberExpression", "right", "referencePaths", "sourceFileHasJsx", "traverse", "JSXElement|JSXFragment", "stop"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport syntaxTypeScript from \"@babel/plugin-syntax-typescript\";\nimport type { PluginPass, types as t, Scope, NodePath } from \"@babel/core\";\nimport { injectInitialization } from \"@babel/helper-create-class-features-plugin\";\nimport type { Options as SyntaxOptions } from \"@babel/plugin-syntax-typescript\";\n\nimport transpileConstEnum from \"./const-enum.ts\";\nimport type { NodePathConstEnum } from \"./const-enum.ts\";\nimport transpileEnum from \"./enum.ts\";\nimport {\n  GLOBAL_TYPES,\n  isGlobalType,\n  registerGlobalType,\n} from \"./global-types.ts\";\nimport transpileNamespace, { getFirstIdentifier } from \"./namespace.ts\";\n\nfunction isInType(path: NodePath) {\n  switch (path.parent.type) {\n    case \"TSTypeReference\":\n    case process.env.BABEL_8_BREAKING\n      ? \"TSClassImplements\"\n      : \"TSExpressionWithTypeArguments\":\n    case process.env.BABEL_8_BREAKING\n      ? \"TSInterfaceHeritage\"\n      : \"TSExpressionWithTypeArguments\":\n    case \"TSTypeQuery\":\n      return true;\n    case \"TSQualifiedName\":\n      return (\n        // `import foo = ns.bar` is transformed to `var foo = ns.bar` and should not be removed\n        path.parentPath.findParent(path => path.type !== \"TSQualifiedName\")\n          .type !== \"TSImportEqualsDeclaration\"\n      );\n    case \"ExportSpecifier\":\n      return (\n        // export { type foo };\n        path.parent.exportKind === \"type\" ||\n        // export type { foo };\n        // @ts-expect-error: DeclareExportDeclaration does not have `exportKind`\n        (path.parentPath as NodePath<t.ExportSpecifier>).parent.exportKind ===\n          \"type\"\n      );\n    default:\n      return false;\n  }\n}\n\n// Track programs which contain imports/exports of values, so that we can include\n// empty exports for programs that do not, but were parsed as modules. This allows\n// tools to infer unambiguously that results are ESM.\nconst NEEDS_EXPLICIT_ESM = new WeakMap();\nconst PARSED_PARAMS = new WeakSet();\n\n// A hack to avoid removing the impl Binding when we remove the declare NodePath\nfunction safeRemove(path: NodePath) {\n  const ids = path.getBindingIdentifiers();\n  for (const name of Object.keys(ids)) {\n    const binding = path.scope.getBinding(name);\n    if (binding && binding.identifier === ids[name]) {\n      binding.scope.removeBinding(name);\n    }\n  }\n  path.opts.noScope = true;\n  path.remove();\n  path.opts.noScope = false;\n}\n\nfunction assertCjsTransformEnabled(\n  path: NodePath,\n  pass: PluginPass,\n  wrong: string,\n  suggestion: string,\n  extra: string = \"\",\n): void {\n  if (pass.file.get(\"@babel/plugin-transform-modules-*\") !== \"commonjs\") {\n    throw path.buildCodeFrameError(\n      `\\`${wrong}\\` is only supported when compiling modules to CommonJS.\\n` +\n        `Please consider using \\`${suggestion}\\`${extra}, or add ` +\n        `@babel/plugin-transform-modules-commonjs to your Babel config.`,\n    );\n  }\n}\n\nexport interface Options extends SyntaxOptions {\n  /** @default true */\n  allowNamespaces?: boolean;\n  /** @default \"React.createElement\" */\n  jsxPragma?: string;\n  /** @default \"React.Fragment\" */\n  jsxPragmaFrag?: string;\n  onlyRemoveTypeImports?: boolean;\n  optimizeConstEnums?: boolean;\n  allowDeclareFields?: boolean;\n}\n\ntype ExtraNodeProps = {\n  declare?: unknown;\n  accessibility?: unknown;\n  abstract?: unknown;\n  optional?: unknown;\n  override?: unknown;\n};\n\nexport default declare((api, opts: Options) => {\n  // `@babel/core` and `@babel/types` are bundled in some downstream libraries.\n  // Ref: https://github.com/babel/babel/issues/15089\n  const { types: t, template } = api;\n\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const JSX_PRAGMA_REGEX = /\\*?\\s*@jsx((?:Frag)?)\\s+(\\S+)/;\n\n  const {\n    allowNamespaces = true,\n    jsxPragma = \"React.createElement\",\n    jsxPragmaFrag = \"React.Fragment\",\n    onlyRemoveTypeImports = false,\n    optimizeConstEnums = false,\n  } = opts;\n\n  if (!process.env.BABEL_8_BREAKING) {\n    // eslint-disable-next-line no-var\n    var { allowDeclareFields = false } = opts;\n  }\n\n  const classMemberVisitors = {\n    field(\n      path: NodePath<\n        (t.ClassPrivateProperty | t.ClassProperty | t.ClassAccessorProperty) &\n          ExtraNodeProps\n      >,\n    ) {\n      const { node } = path;\n\n      if (!process.env.BABEL_8_BREAKING) {\n        if (!allowDeclareFields && node.declare) {\n          throw path.buildCodeFrameError(\n            `The 'declare' modifier is only allowed when the 'allowDeclareFields' option of ` +\n              `@babel/plugin-transform-typescript or @babel/preset-typescript is enabled.`,\n          );\n        }\n      }\n      if (node.declare) {\n        if (node.value) {\n          throw path.buildCodeFrameError(\n            `Fields with the 'declare' modifier cannot be initialized here, but only in the constructor`,\n          );\n        }\n        if (!node.decorators) {\n          path.remove();\n        }\n      } else if (node.definite) {\n        if (node.value) {\n          throw path.buildCodeFrameError(\n            `Definitely assigned fields cannot be initialized here, but only in the constructor`,\n          );\n        }\n        if (!process.env.BABEL_8_BREAKING) {\n          // keep the definitely assigned fields only when `allowDeclareFields` (equivalent of\n          // Typescript's `useDefineForClassFields`) is true\n          if (\n            !allowDeclareFields &&\n            !node.decorators &&\n            !t.isClassPrivateProperty(node)\n          ) {\n            path.remove();\n          }\n        }\n      } else if (node.abstract) {\n        path.remove();\n      } else if (!process.env.BABEL_8_BREAKING) {\n        if (\n          !allowDeclareFields &&\n          !node.value &&\n          !node.decorators &&\n          !t.isClassPrivateProperty(node)\n        ) {\n          path.remove();\n        }\n      }\n\n      if (node.accessibility) node.accessibility = null;\n      if (node.abstract) node.abstract = null;\n      if (node.readonly) node.readonly = null;\n      if (node.optional) node.optional = null;\n      if (node.typeAnnotation) node.typeAnnotation = null;\n      if (node.definite) node.definite = null;\n      if (node.declare) node.declare = null;\n      if (node.override) node.override = null;\n    },\n    method({ node }: NodePath<t.ClassMethod | t.ClassPrivateMethod>) {\n      if (node.accessibility) node.accessibility = null;\n      if (node.abstract) node.abstract = null;\n      if (node.optional) node.optional = null;\n      if (node.override) node.override = null;\n\n      // Rest handled by Function visitor\n    },\n    constructor(path: NodePath<t.ClassMethod>, classPath: NodePath<t.Class>) {\n      if (path.node.accessibility) path.node.accessibility = null;\n      // Collects parameter properties so that we can add an assignment\n      // for each of them in the constructor body\n      //\n      // We use a WeakSet to ensure an assignment for a parameter\n      // property is only added once. This is necessary for cases like\n      // using `transform-classes`, which causes this visitor to run\n      // twice.\n      const assigns: t.ExpressionStatement[] = [];\n      const { scope } = path;\n      for (const paramPath of path.get(\"params\")) {\n        const param = paramPath.node;\n        if (param.type === \"TSParameterProperty\") {\n          const parameter = param.parameter;\n          if (PARSED_PARAMS.has(parameter)) continue;\n          PARSED_PARAMS.add(parameter);\n          let id;\n          if (t.isIdentifier(parameter)) {\n            id = parameter;\n          } else if (\n            t.isAssignmentPattern(parameter) &&\n            t.isIdentifier(parameter.left)\n          ) {\n            id = parameter.left;\n          } else {\n            throw paramPath.buildCodeFrameError(\n              \"Parameter properties can not be destructuring patterns.\",\n            );\n          }\n          assigns.push(\n            template.statement.ast`\n              this.${t.cloneNode(id)} = ${t.cloneNode(id)}\n            ` as t.ExpressionStatement,\n          );\n\n          paramPath.replaceWith(paramPath.get(\"parameter\"));\n          scope.registerBinding(\"param\", paramPath);\n        }\n      }\n      injectInitialization(classPath, path, assigns);\n    },\n  };\n\n  return {\n    name: \"transform-typescript\",\n    inherits: syntaxTypeScript,\n\n    visitor: {\n      //\"Pattern\" alias doesn't include Identifier or RestElement.\n      Pattern: visitPattern,\n      Identifier: visitPattern,\n      RestElement: visitPattern,\n\n      Program: {\n        enter(path, state) {\n          const { file } = state;\n          let fileJsxPragma = null;\n          let fileJsxPragmaFrag = null;\n          const programScope = path.scope;\n\n          if (!GLOBAL_TYPES.has(programScope)) {\n            GLOBAL_TYPES.set(programScope, new Set());\n          }\n\n          if (file.ast.comments) {\n            for (const comment of file.ast.comments) {\n              const jsxMatches = JSX_PRAGMA_REGEX.exec(comment.value);\n              if (jsxMatches) {\n                if (jsxMatches[1]) {\n                  // isFragment\n                  fileJsxPragmaFrag = jsxMatches[2];\n                } else {\n                  fileJsxPragma = jsxMatches[2];\n                }\n              }\n            }\n          }\n\n          let pragmaImportName = fileJsxPragma || jsxPragma;\n          if (pragmaImportName) {\n            [pragmaImportName] = pragmaImportName.split(\".\");\n          }\n\n          let pragmaFragImportName = fileJsxPragmaFrag || jsxPragmaFrag;\n          if (pragmaFragImportName) {\n            [pragmaFragImportName] = pragmaFragImportName.split(\".\");\n          }\n\n          // remove type imports\n          for (let stmt of path.get(\"body\") as Iterable<\n            NodePath<t.Statement | t.Expression>\n          >) {\n            if (stmt.isImportDeclaration()) {\n              if (!NEEDS_EXPLICIT_ESM.has(state.file.ast.program)) {\n                NEEDS_EXPLICIT_ESM.set(state.file.ast.program, true);\n              }\n\n              if (stmt.node.importKind === \"type\") {\n                for (const specifier of stmt.node.specifiers) {\n                  registerGlobalType(programScope, specifier.local.name);\n                }\n                stmt.remove();\n                continue;\n              }\n\n              const importsToRemove: Set<NodePath<t.Node>> = new Set();\n              const specifiersLength = stmt.node.specifiers.length;\n              const isAllSpecifiersElided = () =>\n                specifiersLength > 0 &&\n                specifiersLength === importsToRemove.size;\n\n              for (const specifier of stmt.node.specifiers) {\n                if (\n                  specifier.type === \"ImportSpecifier\" &&\n                  specifier.importKind === \"type\"\n                ) {\n                  registerGlobalType(programScope, specifier.local.name);\n                  const binding = stmt.scope.getBinding(specifier.local.name);\n                  if (binding) {\n                    importsToRemove.add(binding.path);\n                  }\n                }\n              }\n\n              // If onlyRemoveTypeImports is `true`, only remove type-only imports\n              // and exports introduced in TypeScript 3.8.\n              if (onlyRemoveTypeImports) {\n                NEEDS_EXPLICIT_ESM.set(path.node, false);\n              } else {\n                // Note: this will allow both `import { } from \"m\"` and `import \"m\";`.\n                // In TypeScript, the former would be elided.\n                if (stmt.node.specifiers.length === 0) {\n                  NEEDS_EXPLICIT_ESM.set(path.node, false);\n                  continue;\n                }\n\n                for (const specifier of stmt.node.specifiers) {\n                  const binding = stmt.scope.getBinding(specifier.local.name);\n\n                  // The binding may not exist if the import node was explicitly\n                  // injected by another plugin. Currently core does not do a good job\n                  // of keeping scope bindings synchronized with the AST. For now we\n                  // just bail if there is no binding, since chances are good that if\n                  // the import statement was injected then it wasn't a typescript type\n                  // import anyway.\n                  if (binding && !importsToRemove.has(binding.path)) {\n                    if (\n                      isImportTypeOnly({\n                        binding,\n                        programPath: path,\n                        pragmaImportName,\n                        pragmaFragImportName,\n                      })\n                    ) {\n                      importsToRemove.add(binding.path);\n                    } else {\n                      NEEDS_EXPLICIT_ESM.set(path.node, false);\n                    }\n                  }\n                }\n              }\n\n              if (isAllSpecifiersElided() && !onlyRemoveTypeImports) {\n                stmt.remove();\n              } else {\n                for (const importPath of importsToRemove) {\n                  importPath.remove();\n                }\n              }\n\n              continue;\n            }\n\n            if (!onlyRemoveTypeImports && stmt.isTSImportEqualsDeclaration()) {\n              const { id } = stmt.node;\n              const binding = stmt.scope.getBinding(id.name);\n              if (\n                binding &&\n                (process.env.BABEL_8_BREAKING ||\n                  // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n                  !stmt.node.isExport) &&\n                isImportTypeOnly({\n                  binding,\n                  programPath: path,\n                  pragmaImportName,\n                  pragmaFragImportName,\n                })\n              ) {\n                stmt.remove();\n                continue;\n              }\n            }\n\n            if (stmt.isExportDeclaration()) {\n              stmt = stmt.get(\"declaration\");\n            }\n\n            if (stmt.isVariableDeclaration({ declare: true })) {\n              for (const name of Object.keys(stmt.getBindingIdentifiers())) {\n                registerGlobalType(programScope, name);\n              }\n            } else if (\n              stmt.isTSTypeAliasDeclaration() ||\n              (stmt.isTSDeclareFunction() && stmt.get(\"id\").isIdentifier()) ||\n              stmt.isTSInterfaceDeclaration() ||\n              stmt.isClassDeclaration({ declare: true }) ||\n              stmt.isTSEnumDeclaration({ declare: true }) ||\n              (stmt.isTSModuleDeclaration({ declare: true }) &&\n                stmt.get(\"id\").isIdentifier())\n            ) {\n              registerGlobalType(\n                programScope,\n                (stmt.node.id as t.Identifier).name,\n              );\n            }\n          }\n        },\n        exit(path) {\n          if (\n            path.node.sourceType === \"module\" &&\n            NEEDS_EXPLICIT_ESM.get(path.node)\n          ) {\n            // If there are no remaining value exports, this file can no longer\n            // be inferred to be ESM. Leave behind an empty export declaration\n            // so it can be.\n            path.pushContainer(\"body\", t.exportNamedDeclaration());\n          }\n        },\n      },\n\n      ExportNamedDeclaration(path, state) {\n        if (!NEEDS_EXPLICIT_ESM.has(state.file.ast.program)) {\n          NEEDS_EXPLICIT_ESM.set(state.file.ast.program, true);\n        }\n\n        if (path.node.exportKind === \"type\") {\n          path.remove();\n          return;\n        }\n\n        if (\n          process.env.BABEL_8_BREAKING &&\n          t.isTSImportEqualsDeclaration(path.node.declaration)\n        ) {\n          return;\n        }\n\n        // remove export declaration that is filled with type-only specifiers\n        //   export { type A1, type A2 } from \"a\";\n        if (\n          path.node.source &&\n          path.node.specifiers.length > 0 &&\n          path.node.specifiers.every(\n            specifier =>\n              specifier.type === \"ExportSpecifier\" &&\n              specifier.exportKind === \"type\",\n          )\n        ) {\n          path.remove();\n          return;\n        }\n\n        // remove export declaration if it's exporting only types\n        // This logic is needed when exportKind is \"value\", because\n        // currently the \"type\" keyword is optional.\n        // TODO:\n        // Also, currently @babel/parser sets exportKind to \"value\" for\n        //   export interface A {}\n        //   etc.\n        if (\n          !path.node.source &&\n          path.node.specifiers.length > 0 &&\n          path.node.specifiers.every(\n            specifier =>\n              t.isExportSpecifier(specifier) &&\n              isGlobalType(path, specifier.local.name),\n          )\n        ) {\n          path.remove();\n          return;\n        }\n\n        // Convert `export namespace X {}` into `export let X; namespace X {}`,\n        // so that when visiting TSModuleDeclaration we do not have to possibly\n        // replace its parent path.\n        if (t.isTSModuleDeclaration(path.node.declaration)) {\n          const namespace = path.node.declaration;\n          if (!t.isStringLiteral(namespace.id)) {\n            const id = getFirstIdentifier(namespace.id);\n            if (path.scope.hasOwnBinding(id.name)) {\n              path.replaceWith(namespace);\n            } else {\n              const [newExport] = path.replaceWithMultiple([\n                t.exportNamedDeclaration(\n                  t.variableDeclaration(\"let\", [\n                    t.variableDeclarator(t.cloneNode(id)),\n                  ]),\n                ),\n                namespace,\n              ]);\n              path.scope.registerDeclaration(newExport);\n            }\n          }\n        }\n\n        NEEDS_EXPLICIT_ESM.set(state.file.ast.program, false);\n      },\n\n      ExportAllDeclaration(path) {\n        if (path.node.exportKind === \"type\") path.remove();\n      },\n\n      ExportSpecifier(path) {\n        // remove type exports\n        type Parent = t.ExportDeclaration & { source?: t.StringLiteral };\n        const parent = path.parent as Parent;\n        if (\n          (!parent.source && isGlobalType(path, path.node.local.name)) ||\n          path.node.exportKind === \"type\"\n        ) {\n          path.remove();\n        }\n      },\n\n      ExportDefaultDeclaration(path, state) {\n        if (!NEEDS_EXPLICIT_ESM.has(state.file.ast.program)) {\n          NEEDS_EXPLICIT_ESM.set(state.file.ast.program, true);\n        }\n\n        // remove whole declaration if it's exporting a TS type\n        if (\n          t.isIdentifier(path.node.declaration) &&\n          isGlobalType(path, path.node.declaration.name)\n        ) {\n          path.remove();\n\n          return;\n        }\n\n        NEEDS_EXPLICIT_ESM.set(state.file.ast.program, false);\n      },\n\n      TSDeclareFunction(path) {\n        safeRemove(path);\n      },\n\n      TSDeclareMethod(path) {\n        safeRemove(path);\n      },\n\n      VariableDeclaration(path) {\n        if (path.node.declare) {\n          safeRemove(path);\n        }\n      },\n\n      VariableDeclarator({ node }) {\n        if (node.definite) node.definite = null;\n      },\n\n      TSIndexSignature(path) {\n        path.remove();\n      },\n\n      ClassDeclaration(path) {\n        const { node } = path;\n        if (node.declare) {\n          safeRemove(path);\n        }\n      },\n\n      Class(path) {\n        const { node }: { node: typeof path.node & ExtraNodeProps } = path;\n\n        if (node.typeParameters) node.typeParameters = null;\n        if (process.env.BABEL_8_BREAKING) {\n          // @ts-ignore(Babel 7 vs Babel 8) Renamed\n          if (node.superTypeArguments) node.superTypeArguments = null;\n        } else {\n          // @ts-ignore(Babel 7 vs Babel 8) Renamed\n          if (node.superTypeParameters) node.superTypeParameters = null;\n        }\n        if (node.implements) node.implements = null;\n        if (node.abstract) node.abstract = null;\n\n        // Similar to the logic in `transform-flow-strip-types`, we need to\n        // handle `TSParameterProperty` and `ClassProperty` here because the\n        // class transform would transform the class, causing more specific\n        // visitors to not run.\n        path.get(\"body.body\").forEach(child => {\n          if (child.isClassMethod() || child.isClassPrivateMethod()) {\n            if (child.node.kind === \"constructor\") {\n              classMemberVisitors.constructor(\n                // @ts-expect-error A constructor must not be a private method\n                child,\n                path,\n              );\n            } else {\n              classMemberVisitors.method(child);\n            }\n          } else if (\n            child.isClassProperty() ||\n            child.isClassPrivateProperty() ||\n            child.isClassAccessorProperty()\n          ) {\n            classMemberVisitors.field(child);\n          }\n        });\n      },\n\n      Function(path) {\n        const { node } = path;\n        if (node.typeParameters) node.typeParameters = null;\n        if (node.returnType) node.returnType = null;\n\n        const params = node.params;\n        if (params.length > 0 && t.isIdentifier(params[0], { name: \"this\" })) {\n          params.shift();\n        }\n      },\n\n      TSModuleDeclaration(path) {\n        transpileNamespace(path, allowNamespaces);\n      },\n\n      TSInterfaceDeclaration(path) {\n        path.remove();\n      },\n\n      TSTypeAliasDeclaration(path) {\n        path.remove();\n      },\n\n      TSEnumDeclaration(path) {\n        if (optimizeConstEnums && path.node.const) {\n          transpileConstEnum(path as NodePathConstEnum, t);\n        } else {\n          transpileEnum(path, t);\n        }\n      },\n\n      TSImportEqualsDeclaration(\n        path: NodePath<t.TSImportEqualsDeclaration>,\n        pass,\n      ) {\n        const { id, moduleReference } = path.node;\n\n        let init: t.Expression;\n        let varKind: \"var\" | \"const\";\n        if (t.isTSExternalModuleReference(moduleReference)) {\n          // import alias = require('foo');\n          assertCjsTransformEnabled(\n            path,\n            pass,\n            `import ${id.name} = require(...);`,\n            `import ${id.name} from '...';`,\n            \" alongside Typescript's --allowSyntheticDefaultImports option\",\n          );\n          init = t.callExpression(t.identifier(\"require\"), [\n            moduleReference.expression,\n          ]);\n          varKind = \"const\";\n        } else {\n          // import alias = Namespace;\n          init = entityNameToExpr(moduleReference);\n          varKind = \"var\";\n        }\n        const newNode = t.variableDeclaration(varKind, [\n          t.variableDeclarator(id, init),\n        ]);\n\n        if (process.env.BABEL_8_BREAKING) {\n          path.replaceWith(newNode);\n        } else {\n          path.replaceWith(\n            // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n            path.node.isExport ? t.exportNamedDeclaration(newNode) : newNode,\n          );\n        }\n        path.scope.registerDeclaration(path);\n      },\n\n      TSExportAssignment(path, pass) {\n        assertCjsTransformEnabled(\n          path,\n          pass,\n          `export = <value>;`,\n          `export default <value>;`,\n        );\n        path.replaceWith(\n          template.statement.ast`module.exports = ${path.node.expression}`,\n        );\n      },\n\n      TSTypeAssertion(path) {\n        path.replaceWith(path.node.expression);\n      },\n\n      [`TSAsExpression${\n        // Added in Babel 7.20.0\n        t.tsSatisfiesExpression ? \"|TSSatisfiesExpression\" : \"\"\n      }`](path: NodePath<t.TSAsExpression | t.TSSatisfiesExpression>) {\n        let { node }: { node: t.Expression } = path;\n        do {\n          node = node.expression;\n        } while (t.isTSAsExpression(node) || t.isTSSatisfiesExpression?.(node));\n        path.replaceWith(node);\n      },\n\n      [process.env.BABEL_8_BREAKING\n        ? \"TSNonNullExpression|TSInstantiationExpression\"\n        : /* This has been introduced in Babel 7.18.0\n             We use api.types.* and not t.* for feature detection,\n             because the Babel version that is running this plugin\n             (where we check if the visitor is valid) might be different\n             from the Babel version that we resolve with `import \"@babel/core\"`.\n             This happens, for example, with Next.js that bundled `@babel/core`\n             but allows loading unbundled plugin (which cannot obviously import\n             the bundled `@babel/core` version).\n           */\n          api.types.tsInstantiationExpression\n          ? \"TSNonNullExpression|TSInstantiationExpression\"\n          : \"TSNonNullExpression\"](\n        path: NodePath<t.TSNonNullExpression | t.TSInstantiationExpression>,\n      ) {\n        path.replaceWith(path.node.expression);\n      },\n\n      CallExpression(path) {\n        if (process.env.BABEL_8_BREAKING) {\n          path.node.typeArguments = null;\n        } else {\n          // @ts-ignore(Babel 7 vs Babel 8) Removed in Babel 8\n          path.node.typeParameters = null;\n        }\n      },\n\n      OptionalCallExpression(path) {\n        if (process.env.BABEL_8_BREAKING) {\n          path.node.typeArguments = null;\n        } else {\n          // @ts-ignore(Babel 7 vs Babel 8) Removed in Babel 8\n          path.node.typeParameters = null;\n        }\n      },\n\n      NewExpression(path) {\n        if (process.env.BABEL_8_BREAKING) {\n          path.node.typeArguments = null;\n        } else {\n          // @ts-ignore(Babel 7 vs Babel 8) Removed in Babel 8\n          path.node.typeParameters = null;\n        }\n      },\n\n      JSXOpeningElement(path) {\n        if (process.env.BABEL_8_BREAKING) {\n          //@ts-ignore(Babel 7 vs Babel 8) Babel 8 AST\n          path.node.typeArguments = null;\n        } else {\n          // @ts-ignore(Babel 7 vs Babel 8) Removed in Babel 8\n          path.node.typeParameters = null;\n        }\n      },\n\n      TaggedTemplateExpression(path) {\n        if (process.env.BABEL_8_BREAKING) {\n          // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST\n          path.node.typeArguments = null;\n        } else {\n          // @ts-ignore(Babel 7 vs Babel 8) Removed in Babel 8\n          path.node.typeParameters = null;\n        }\n      },\n    },\n  };\n\n  function entityNameToExpr(node: t.TSEntityName): t.Expression {\n    if (t.isTSQualifiedName(node)) {\n      return t.memberExpression(entityNameToExpr(node.left), node.right);\n    }\n\n    return node;\n  }\n\n  function visitPattern({\n    node,\n  }: NodePath<t.Identifier | t.Pattern | t.RestElement>) {\n    // @ts-expect-error typeAnnotation does not exist in VoidPattern\n    if (node.typeAnnotation) node.typeAnnotation = null;\n    if (t.isIdentifier(node) && node.optional) node.optional = null;\n    // 'access' and 'readonly' are only for parameter properties, so constructor visitor will handle them.\n  }\n\n  function isImportTypeOnly({\n    binding,\n    programPath,\n    pragmaImportName,\n    pragmaFragImportName,\n  }: {\n    binding: Scope.Binding;\n    programPath: NodePath<t.Program>;\n    pragmaImportName: string;\n    pragmaFragImportName: string;\n  }) {\n    for (const path of binding.referencePaths) {\n      if (!isInType(path)) {\n        return false;\n      }\n    }\n\n    if (\n      binding.identifier.name !== pragmaImportName &&\n      binding.identifier.name !== pragmaFragImportName\n    ) {\n      return true;\n    }\n\n    // \"React\" or the JSX pragma is referenced as a value if there are any JSX elements/fragments in the code.\n    let sourceFileHasJsx = false;\n    programPath.traverse({\n      \"JSXElement|JSXFragment\"(path) {\n        sourceFileHasJsx = true;\n        path.stop();\n      },\n    });\n    return !sourceFileHasJsx;\n  }\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAD,OAAA;AAEA,IAAAE,gCAAA,GAAAF,OAAA;AAGA,IAAAG,UAAA,GAAAH,OAAA;AAEA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,YAAA,GAAAL,OAAA;AAKA,IAAAM,UAAA,GAAAN,OAAA;AAEA,SAASO,QAAQA,CAACC,IAAc,EAAE;EAChC,QAAQA,IAAI,CAACC,MAAM,CAACC,IAAI;IACtB,KAAK,iBAAiB;IACtB,KAEI,+BAA+B;IACnC,KAEI,+BAA+B;IACnC,KAAK,aAAa;MAChB,OAAO,IAAI;IACb,KAAK,iBAAiB;MACpB,QAEEF,IAAI,CAACG,UAAU,CAACC,UAAU,CAACJ,IAAI,IAAIA,IAAI,CAACE,IAAI,KAAK,iBAAiB,CAAC,CAChEA,IAAI,KAAK;MAA2B;IAE3C,KAAK,iBAAiB;MACpB,QAEEF,IAAI,CAACC,MAAM,CAACI,UAAU,KAAK,MAAM,IAGhCL,IAAI,CAACG,UAAU,CAAiCF,MAAM,CAACI,UAAU,KAChE;MAAM;IAEZ;MACE,OAAO,KAAK;EAChB;AACF;AAKA,MAAMC,kBAAkB,GAAG,IAAIC,OAAO,CAAC,CAAC;AACxC,MAAMC,aAAa,GAAG,IAAIC,OAAO,CAAC,CAAC;AAGnC,SAASC,UAAUA,CAACV,IAAc,EAAE;EAClC,MAAMW,GAAG,GAAGX,IAAI,CAACY,qBAAqB,CAAC,CAAC;EACxC,KAAK,MAAMC,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACJ,GAAG,CAAC,EAAE;IACnC,MAAMK,OAAO,GAAGhB,IAAI,CAACiB,KAAK,CAACC,UAAU,CAACL,IAAI,CAAC;IAC3C,IAAIG,OAAO,IAAIA,OAAO,CAACG,UAAU,KAAKR,GAAG,CAACE,IAAI,CAAC,EAAE;MAC/CG,OAAO,CAACC,KAAK,CAACG,aAAa,CAACP,IAAI,CAAC;IACnC;EACF;EACAb,IAAI,CAACqB,IAAI,CAACC,OAAO,GAAG,IAAI;EACxBtB,IAAI,CAACuB,MAAM,CAAC,CAAC;EACbvB,IAAI,CAACqB,IAAI,CAACC,OAAO,GAAG,KAAK;AAC3B;AAEA,SAASE,yBAAyBA,CAChCxB,IAAc,EACdyB,IAAgB,EAChBC,KAAa,EACbC,UAAkB,EAClBC,KAAa,GAAG,EAAE,EACZ;EACN,IAAIH,IAAI,CAACI,IAAI,CAACC,GAAG,CAAC,mCAAmC,CAAC,KAAK,UAAU,EAAE;IACrE,MAAM9B,IAAI,CAAC+B,mBAAmB,CAC5B,KAAKL,KAAK,4DAA4D,GACpE,2BAA2BC,UAAU,KAAKC,KAAK,WAAW,GAC1D,gEACJ,CAAC;EACH;AACF;AAAC,IAAAI,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAsBc,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEf,IAAa,KAAK;EAG7C,MAAM;IAAEgB,KAAK,EAAEC,CAAC;IAAEC;EAAS,CAAC,GAAGH,GAAG;EAElCA,GAAG,CAACI,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAMC,gBAAgB,GAAG,+BAA+B;EAExD,MAAM;IACJC,eAAe,GAAG,IAAI;IACtBC,SAAS,GAAG,qBAAqB;IACjCC,aAAa,GAAG,gBAAgB;IAChCC,qBAAqB,GAAG,KAAK;IAC7BC,kBAAkB,GAAG;EACvB,CAAC,GAAGzB,IAAI;EAE2B;IAEjC,IAAI;MAAE0B,kBAAkB,GAAG;IAAM,CAAC,GAAG1B,IAAI;EAC3C;EAEA,MAAM2B,mBAAmB,GAAG;IAC1BC,KAAKA,CACHjD,IAGC,EACD;MACA,MAAM;QAAEkD;MAAK,CAAC,GAAGlD,IAAI;MAEc;QACjC,IAAI,CAAC+C,kBAAkB,IAAIG,IAAI,CAACf,OAAO,EAAE;UACvC,MAAMnC,IAAI,CAAC+B,mBAAmB,CAC5B,iFAAiF,GAC/E,4EACJ,CAAC;QACH;MACF;MACA,IAAImB,IAAI,CAACf,OAAO,EAAE;QAChB,IAAIe,IAAI,CAACC,KAAK,EAAE;UACd,MAAMnD,IAAI,CAAC+B,mBAAmB,CAC5B,4FACF,CAAC;QACH;QACA,IAAI,CAACmB,IAAI,CAACE,UAAU,EAAE;UACpBpD,IAAI,CAACuB,MAAM,CAAC,CAAC;QACf;MACF,CAAC,MAAM,IAAI2B,IAAI,CAACG,QAAQ,EAAE;QACxB,IAAIH,IAAI,CAACC,KAAK,EAAE;UACd,MAAMnD,IAAI,CAAC+B,mBAAmB,CAC5B,oFACF,CAAC;QACH;QACmC;UAGjC,IACE,CAACgB,kBAAkB,IACnB,CAACG,IAAI,CAACE,UAAU,IAChB,CAACd,CAAC,CAACgB,sBAAsB,CAACJ,IAAI,CAAC,EAC/B;YACAlD,IAAI,CAACuB,MAAM,CAAC,CAAC;UACf;QACF;MACF,CAAC,MAAM,IAAI2B,IAAI,CAACK,QAAQ,EAAE;QACxBvD,IAAI,CAACuB,MAAM,CAAC,CAAC;MACf,CAAC,MAAyC;QACxC,IACE,CAACwB,kBAAkB,IACnB,CAACG,IAAI,CAACC,KAAK,IACX,CAACD,IAAI,CAACE,UAAU,IAChB,CAACd,CAAC,CAACgB,sBAAsB,CAACJ,IAAI,CAAC,EAC/B;UACAlD,IAAI,CAACuB,MAAM,CAAC,CAAC;QACf;MACF;MAEA,IAAI2B,IAAI,CAACM,aAAa,EAAEN,IAAI,CAACM,aAAa,GAAG,IAAI;MACjD,IAAIN,IAAI,CAACK,QAAQ,EAAEL,IAAI,CAACK,QAAQ,GAAG,IAAI;MACvC,IAAIL,IAAI,CAACO,QAAQ,EAAEP,IAAI,CAACO,QAAQ,GAAG,IAAI;MACvC,IAAIP,IAAI,CAACQ,QAAQ,EAAER,IAAI,CAACQ,QAAQ,GAAG,IAAI;MACvC,IAAIR,IAAI,CAACS,cAAc,EAAET,IAAI,CAACS,cAAc,GAAG,IAAI;MACnD,IAAIT,IAAI,CAACG,QAAQ,EAAEH,IAAI,CAACG,QAAQ,GAAG,IAAI;MACvC,IAAIH,IAAI,CAACf,OAAO,EAAEe,IAAI,CAACf,OAAO,GAAG,IAAI;MACrC,IAAIe,IAAI,CAACU,QAAQ,EAAEV,IAAI,CAACU,QAAQ,GAAG,IAAI;IACzC,CAAC;IACDC,MAAMA,CAAC;MAAEX;IAAqD,CAAC,EAAE;MAC/D,IAAIA,IAAI,CAACM,aAAa,EAAEN,IAAI,CAACM,aAAa,GAAG,IAAI;MACjD,IAAIN,IAAI,CAACK,QAAQ,EAAEL,IAAI,CAACK,QAAQ,GAAG,IAAI;MACvC,IAAIL,IAAI,CAACQ,QAAQ,EAAER,IAAI,CAACQ,QAAQ,GAAG,IAAI;MACvC,IAAIR,IAAI,CAACU,QAAQ,EAAEV,IAAI,CAACU,QAAQ,GAAG,IAAI;IAGzC,CAAC;IACDE,WAAWA,CAAC9D,IAA6B,EAAE+D,SAA4B,EAAE;MACvE,IAAI/D,IAAI,CAACkD,IAAI,CAACM,aAAa,EAAExD,IAAI,CAACkD,IAAI,CAACM,aAAa,GAAG,IAAI;MAQ3D,MAAMQ,OAAgC,GAAG,EAAE;MAC3C,MAAM;QAAE/C;MAAM,CAAC,GAAGjB,IAAI;MACtB,KAAK,MAAMiE,SAAS,IAAIjE,IAAI,CAAC8B,GAAG,CAAC,QAAQ,CAAC,EAAE;QAC1C,MAAMoC,KAAK,GAAGD,SAAS,CAACf,IAAI;QAC5B,IAAIgB,KAAK,CAAChE,IAAI,KAAK,qBAAqB,EAAE;UACxC,MAAMiE,SAAS,GAAGD,KAAK,CAACC,SAAS;UACjC,IAAI3D,aAAa,CAAC4D,GAAG,CAACD,SAAS,CAAC,EAAE;UAClC3D,aAAa,CAAC6D,GAAG,CAACF,SAAS,CAAC;UAC5B,IAAIG,EAAE;UACN,IAAIhC,CAAC,CAACiC,YAAY,CAACJ,SAAS,CAAC,EAAE;YAC7BG,EAAE,GAAGH,SAAS;UAChB,CAAC,MAAM,IACL7B,CAAC,CAACkC,mBAAmB,CAACL,SAAS,CAAC,IAChC7B,CAAC,CAACiC,YAAY,CAACJ,SAAS,CAACM,IAAI,CAAC,EAC9B;YACAH,EAAE,GAAGH,SAAS,CAACM,IAAI;UACrB,CAAC,MAAM;YACL,MAAMR,SAAS,CAAClC,mBAAmB,CACjC,yDACF,CAAC;UACH;UACAiC,OAAO,CAACU,IAAI,CACVnC,QAAQ,CAACoC,SAAS,CAACC,GAAG;AAClC,qBAAqBtC,CAAC,CAACuC,SAAS,CAACP,EAAE,CAAC,MAAMhC,CAAC,CAACuC,SAAS,CAACP,EAAE,CAAC;AACzD,aACU,CAAC;UAEDL,SAAS,CAACa,WAAW,CAACb,SAAS,CAACnC,GAAG,CAAC,WAAW,CAAC,CAAC;UACjDb,KAAK,CAAC8D,eAAe,CAAC,OAAO,EAAEd,SAAS,CAAC;QAC3C;MACF;MACA,IAAAe,qDAAoB,EAACjB,SAAS,EAAE/D,IAAI,EAAEgE,OAAO,CAAC;IAChD;EACF,CAAC;EAED,OAAO;IACLnD,IAAI,EAAE,sBAAsB;IAC5BoE,QAAQ,EAAEC,+BAAgB;IAE1BC,OAAO,EAAE;MAEPC,OAAO,EAAEC,YAAY;MACrBC,UAAU,EAAED,YAAY;MACxBE,WAAW,EAAEF,YAAY;MAEzBG,OAAO,EAAE;QACPC,KAAKA,CAACzF,IAAI,EAAE0F,KAAK,EAAE;UACjB,MAAM;YAAE7D;UAAK,CAAC,GAAG6D,KAAK;UACtB,IAAIC,aAAa,GAAG,IAAI;UACxB,IAAIC,iBAAiB,GAAG,IAAI;UAC5B,MAAMC,YAAY,GAAG7F,IAAI,CAACiB,KAAK;UAE/B,IAAI,CAAC6E,yBAAY,CAAC1B,GAAG,CAACyB,YAAY,CAAC,EAAE;YACnCC,yBAAY,CAACC,GAAG,CAACF,YAAY,EAAE,IAAIG,GAAG,CAAC,CAAC,CAAC;UAC3C;UAEA,IAAInE,IAAI,CAAC+C,GAAG,CAACqB,QAAQ,EAAE;YACrB,KAAK,MAAMC,OAAO,IAAIrE,IAAI,CAAC+C,GAAG,CAACqB,QAAQ,EAAE;cACvC,MAAME,UAAU,GAAG1D,gBAAgB,CAAC2D,IAAI,CAACF,OAAO,CAAC/C,KAAK,CAAC;cACvD,IAAIgD,UAAU,EAAE;gBACd,IAAIA,UAAU,CAAC,CAAC,CAAC,EAAE;kBAEjBP,iBAAiB,GAAGO,UAAU,CAAC,CAAC,CAAC;gBACnC,CAAC,MAAM;kBACLR,aAAa,GAAGQ,UAAU,CAAC,CAAC,CAAC;gBAC/B;cACF;YACF;UACF;UAEA,IAAIE,gBAAgB,GAAGV,aAAa,IAAIhD,SAAS;UACjD,IAAI0D,gBAAgB,EAAE;YACpB,CAACA,gBAAgB,CAAC,GAAGA,gBAAgB,CAACC,KAAK,CAAC,GAAG,CAAC;UAClD;UAEA,IAAIC,oBAAoB,GAAGX,iBAAiB,IAAIhD,aAAa;UAC7D,IAAI2D,oBAAoB,EAAE;YACxB,CAACA,oBAAoB,CAAC,GAAGA,oBAAoB,CAACD,KAAK,CAAC,GAAG,CAAC;UAC1D;UAGA,KAAK,IAAIE,IAAI,IAAIxG,IAAI,CAAC8B,GAAG,CAAC,MAAM,CAAC,EAE9B;YACD,IAAI0E,IAAI,CAACC,mBAAmB,CAAC,CAAC,EAAE;cAC9B,IAAI,CAACnG,kBAAkB,CAAC8D,GAAG,CAACsB,KAAK,CAAC7D,IAAI,CAAC+C,GAAG,CAAC8B,OAAO,CAAC,EAAE;gBACnDpG,kBAAkB,CAACyF,GAAG,CAACL,KAAK,CAAC7D,IAAI,CAAC+C,GAAG,CAAC8B,OAAO,EAAE,IAAI,CAAC;cACtD;cAEA,IAAIF,IAAI,CAACtD,IAAI,CAACyD,UAAU,KAAK,MAAM,EAAE;gBACnC,KAAK,MAAMC,SAAS,IAAIJ,IAAI,CAACtD,IAAI,CAAC2D,UAAU,EAAE;kBAC5C,IAAAC,+BAAkB,EAACjB,YAAY,EAAEe,SAAS,CAACG,KAAK,CAAClG,IAAI,CAAC;gBACxD;gBACA2F,IAAI,CAACjF,MAAM,CAAC,CAAC;gBACb;cACF;cAEA,MAAMyF,eAAsC,GAAG,IAAIhB,GAAG,CAAC,CAAC;cACxD,MAAMiB,gBAAgB,GAAGT,IAAI,CAACtD,IAAI,CAAC2D,UAAU,CAACK,MAAM;cACpD,MAAMC,qBAAqB,GAAGA,CAAA,KAC5BF,gBAAgB,GAAG,CAAC,IACpBA,gBAAgB,KAAKD,eAAe,CAACI,IAAI;cAE3C,KAAK,MAAMR,SAAS,IAAIJ,IAAI,CAACtD,IAAI,CAAC2D,UAAU,EAAE;gBAC5C,IACED,SAAS,CAAC1G,IAAI,KAAK,iBAAiB,IACpC0G,SAAS,CAACD,UAAU,KAAK,MAAM,EAC/B;kBACA,IAAAG,+BAAkB,EAACjB,YAAY,EAAEe,SAAS,CAACG,KAAK,CAAClG,IAAI,CAAC;kBACtD,MAAMG,OAAO,GAAGwF,IAAI,CAACvF,KAAK,CAACC,UAAU,CAAC0F,SAAS,CAACG,KAAK,CAAClG,IAAI,CAAC;kBAC3D,IAAIG,OAAO,EAAE;oBACXgG,eAAe,CAAC3C,GAAG,CAACrD,OAAO,CAAChB,IAAI,CAAC;kBACnC;gBACF;cACF;cAIA,IAAI6C,qBAAqB,EAAE;gBACzBvC,kBAAkB,CAACyF,GAAG,CAAC/F,IAAI,CAACkD,IAAI,EAAE,KAAK,CAAC;cAC1C,CAAC,MAAM;gBAGL,IAAIsD,IAAI,CAACtD,IAAI,CAAC2D,UAAU,CAACK,MAAM,KAAK,CAAC,EAAE;kBACrC5G,kBAAkB,CAACyF,GAAG,CAAC/F,IAAI,CAACkD,IAAI,EAAE,KAAK,CAAC;kBACxC;gBACF;gBAEA,KAAK,MAAM0D,SAAS,IAAIJ,IAAI,CAACtD,IAAI,CAAC2D,UAAU,EAAE;kBAC5C,MAAM7F,OAAO,GAAGwF,IAAI,CAACvF,KAAK,CAACC,UAAU,CAAC0F,SAAS,CAACG,KAAK,CAAClG,IAAI,CAAC;kBAQ3D,IAAIG,OAAO,IAAI,CAACgG,eAAe,CAAC5C,GAAG,CAACpD,OAAO,CAAChB,IAAI,CAAC,EAAE;oBACjD,IACEqH,gBAAgB,CAAC;sBACfrG,OAAO;sBACPsG,WAAW,EAAEtH,IAAI;sBACjBqG,gBAAgB;sBAChBE;oBACF,CAAC,CAAC,EACF;sBACAS,eAAe,CAAC3C,GAAG,CAACrD,OAAO,CAAChB,IAAI,CAAC;oBACnC,CAAC,MAAM;sBACLM,kBAAkB,CAACyF,GAAG,CAAC/F,IAAI,CAACkD,IAAI,EAAE,KAAK,CAAC;oBAC1C;kBACF;gBACF;cACF;cAEA,IAAIiE,qBAAqB,CAAC,CAAC,IAAI,CAACtE,qBAAqB,EAAE;gBACrD2D,IAAI,CAACjF,MAAM,CAAC,CAAC;cACf,CAAC,MAAM;gBACL,KAAK,MAAMgG,UAAU,IAAIP,eAAe,EAAE;kBACxCO,UAAU,CAAChG,MAAM,CAAC,CAAC;gBACrB;cACF;cAEA;YACF;YAEA,IAAI,CAACsB,qBAAqB,IAAI2D,IAAI,CAACgB,2BAA2B,CAAC,CAAC,EAAE;cAChE,MAAM;gBAAElD;cAAG,CAAC,GAAGkC,IAAI,CAACtD,IAAI;cACxB,MAAMlC,OAAO,GAAGwF,IAAI,CAACvF,KAAK,CAACC,UAAU,CAACoD,EAAE,CAACzD,IAAI,CAAC;cAC9C,IACEG,OAAO,IAGL,CAACwF,IAAI,CAACtD,IAAI,CAACuE,QAAQ,IACrBJ,gBAAgB,CAAC;gBACfrG,OAAO;gBACPsG,WAAW,EAAEtH,IAAI;gBACjBqG,gBAAgB;gBAChBE;cACF,CAAC,CAAC,EACF;gBACAC,IAAI,CAACjF,MAAM,CAAC,CAAC;gBACb;cACF;YACF;YAEA,IAAIiF,IAAI,CAACkB,mBAAmB,CAAC,CAAC,EAAE;cAC9BlB,IAAI,GAAGA,IAAI,CAAC1E,GAAG,CAAC,aAAa,CAAC;YAChC;YAEA,IAAI0E,IAAI,CAACmB,qBAAqB,CAAC;cAAExF,OAAO,EAAE;YAAK,CAAC,CAAC,EAAE;cACjD,KAAK,MAAMtB,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACyF,IAAI,CAAC5F,qBAAqB,CAAC,CAAC,CAAC,EAAE;gBAC5D,IAAAkG,+BAAkB,EAACjB,YAAY,EAAEhF,IAAI,CAAC;cACxC;YACF,CAAC,MAAM,IACL2F,IAAI,CAACoB,wBAAwB,CAAC,CAAC,IAC9BpB,IAAI,CAACqB,mBAAmB,CAAC,CAAC,IAAIrB,IAAI,CAAC1E,GAAG,CAAC,IAAI,CAAC,CAACyC,YAAY,CAAC,CAAE,IAC7DiC,IAAI,CAACsB,wBAAwB,CAAC,CAAC,IAC/BtB,IAAI,CAACuB,kBAAkB,CAAC;cAAE5F,OAAO,EAAE;YAAK,CAAC,CAAC,IAC1CqE,IAAI,CAACwB,mBAAmB,CAAC;cAAE7F,OAAO,EAAE;YAAK,CAAC,CAAC,IAC1CqE,IAAI,CAACyB,qBAAqB,CAAC;cAAE9F,OAAO,EAAE;YAAK,CAAC,CAAC,IAC5CqE,IAAI,CAAC1E,GAAG,CAAC,IAAI,CAAC,CAACyC,YAAY,CAAC,CAAE,EAChC;cACA,IAAAuC,+BAAkB,EAChBjB,YAAY,EACXW,IAAI,CAACtD,IAAI,CAACoB,EAAE,CAAkBzD,IACjC,CAAC;YACH;UACF;QACF,CAAC;QACDqH,IAAIA,CAAClI,IAAI,EAAE;UACT,IACEA,IAAI,CAACkD,IAAI,CAACiF,UAAU,KAAK,QAAQ,IACjC7H,kBAAkB,CAACwB,GAAG,CAAC9B,IAAI,CAACkD,IAAI,CAAC,EACjC;YAIAlD,IAAI,CAACoI,aAAa,CAAC,MAAM,EAAE9F,CAAC,CAAC+F,sBAAsB,CAAC,CAAC,CAAC;UACxD;QACF;MACF,CAAC;MAEDC,sBAAsBA,CAACtI,IAAI,EAAE0F,KAAK,EAAE;QAClC,IAAI,CAACpF,kBAAkB,CAAC8D,GAAG,CAACsB,KAAK,CAAC7D,IAAI,CAAC+C,GAAG,CAAC8B,OAAO,CAAC,EAAE;UACnDpG,kBAAkB,CAACyF,GAAG,CAACL,KAAK,CAAC7D,IAAI,CAAC+C,GAAG,CAAC8B,OAAO,EAAE,IAAI,CAAC;QACtD;QAEA,IAAI1G,IAAI,CAACkD,IAAI,CAAC7C,UAAU,KAAK,MAAM,EAAE;UACnCL,IAAI,CAACuB,MAAM,CAAC,CAAC;UACb;QACF;QAAC;QAWD,IACEvB,IAAI,CAACkD,IAAI,CAACqF,MAAM,IAChBvI,IAAI,CAACkD,IAAI,CAAC2D,UAAU,CAACK,MAAM,GAAG,CAAC,IAC/BlH,IAAI,CAACkD,IAAI,CAAC2D,UAAU,CAAC2B,KAAK,CACxB5B,SAAS,IACPA,SAAS,CAAC1G,IAAI,KAAK,iBAAiB,IACpC0G,SAAS,CAACvG,UAAU,KAAK,MAC7B,CAAC,EACD;UACAL,IAAI,CAACuB,MAAM,CAAC,CAAC;UACb;QACF;QASA,IACE,CAACvB,IAAI,CAACkD,IAAI,CAACqF,MAAM,IACjBvI,IAAI,CAACkD,IAAI,CAAC2D,UAAU,CAACK,MAAM,GAAG,CAAC,IAC/BlH,IAAI,CAACkD,IAAI,CAAC2D,UAAU,CAAC2B,KAAK,CACxB5B,SAAS,IACPtE,CAAC,CAACmG,iBAAiB,CAAC7B,SAAS,CAAC,IAC9B,IAAA8B,yBAAY,EAAC1I,IAAI,EAAE4G,SAAS,CAACG,KAAK,CAAClG,IAAI,CAC3C,CAAC,EACD;UACAb,IAAI,CAACuB,MAAM,CAAC,CAAC;UACb;QACF;QAKA,IAAIe,CAAC,CAAC2F,qBAAqB,CAACjI,IAAI,CAACkD,IAAI,CAACyF,WAAW,CAAC,EAAE;UAClD,MAAMC,SAAS,GAAG5I,IAAI,CAACkD,IAAI,CAACyF,WAAW;UACvC,IAAI,CAACrG,CAAC,CAACuG,eAAe,CAACD,SAAS,CAACtE,EAAE,CAAC,EAAE;YACpC,MAAMA,EAAE,GAAG,IAAAwE,6BAAkB,EAACF,SAAS,CAACtE,EAAE,CAAC;YAC3C,IAAItE,IAAI,CAACiB,KAAK,CAAC8H,aAAa,CAACzE,EAAE,CAACzD,IAAI,CAAC,EAAE;cACrCb,IAAI,CAAC8E,WAAW,CAAC8D,SAAS,CAAC;YAC7B,CAAC,MAAM;cACL,MAAM,CAACI,SAAS,CAAC,GAAGhJ,IAAI,CAACiJ,mBAAmB,CAAC,CAC3C3G,CAAC,CAAC+F,sBAAsB,CACtB/F,CAAC,CAAC4G,mBAAmB,CAAC,KAAK,EAAE,CAC3B5G,CAAC,CAAC6G,kBAAkB,CAAC7G,CAAC,CAACuC,SAAS,CAACP,EAAE,CAAC,CAAC,CACtC,CACH,CAAC,EACDsE,SAAS,CACV,CAAC;cACF5I,IAAI,CAACiB,KAAK,CAACmI,mBAAmB,CAACJ,SAAS,CAAC;YAC3C;UACF;QACF;QAEA1I,kBAAkB,CAACyF,GAAG,CAACL,KAAK,CAAC7D,IAAI,CAAC+C,GAAG,CAAC8B,OAAO,EAAE,KAAK,CAAC;MACvD,CAAC;MAED2C,oBAAoBA,CAACrJ,IAAI,EAAE;QACzB,IAAIA,IAAI,CAACkD,IAAI,CAAC7C,UAAU,KAAK,MAAM,EAAEL,IAAI,CAACuB,MAAM,CAAC,CAAC;MACpD,CAAC;MAED+H,eAAeA,CAACtJ,IAAI,EAAE;QAGpB,MAAMC,MAAM,GAAGD,IAAI,CAACC,MAAgB;QACpC,IACG,CAACA,MAAM,CAACsI,MAAM,IAAI,IAAAG,yBAAY,EAAC1I,IAAI,EAAEA,IAAI,CAACkD,IAAI,CAAC6D,KAAK,CAAClG,IAAI,CAAC,IAC3Db,IAAI,CAACkD,IAAI,CAAC7C,UAAU,KAAK,MAAM,EAC/B;UACAL,IAAI,CAACuB,MAAM,CAAC,CAAC;QACf;MACF,CAAC;MAEDgI,wBAAwBA,CAACvJ,IAAI,EAAE0F,KAAK,EAAE;QACpC,IAAI,CAACpF,kBAAkB,CAAC8D,GAAG,CAACsB,KAAK,CAAC7D,IAAI,CAAC+C,GAAG,CAAC8B,OAAO,CAAC,EAAE;UACnDpG,kBAAkB,CAACyF,GAAG,CAACL,KAAK,CAAC7D,IAAI,CAAC+C,GAAG,CAAC8B,OAAO,EAAE,IAAI,CAAC;QACtD;QAGA,IACEpE,CAAC,CAACiC,YAAY,CAACvE,IAAI,CAACkD,IAAI,CAACyF,WAAW,CAAC,IACrC,IAAAD,yBAAY,EAAC1I,IAAI,EAAEA,IAAI,CAACkD,IAAI,CAACyF,WAAW,CAAC9H,IAAI,CAAC,EAC9C;UACAb,IAAI,CAACuB,MAAM,CAAC,CAAC;UAEb;QACF;QAEAjB,kBAAkB,CAACyF,GAAG,CAACL,KAAK,CAAC7D,IAAI,CAAC+C,GAAG,CAAC8B,OAAO,EAAE,KAAK,CAAC;MACvD,CAAC;MAED8C,iBAAiBA,CAACxJ,IAAI,EAAE;QACtBU,UAAU,CAACV,IAAI,CAAC;MAClB,CAAC;MAEDyJ,eAAeA,CAACzJ,IAAI,EAAE;QACpBU,UAAU,CAACV,IAAI,CAAC;MAClB,CAAC;MAED0J,mBAAmBA,CAAC1J,IAAI,EAAE;QACxB,IAAIA,IAAI,CAACkD,IAAI,CAACf,OAAO,EAAE;UACrBzB,UAAU,CAACV,IAAI,CAAC;QAClB;MACF,CAAC;MAED2J,kBAAkBA,CAAC;QAAEzG;MAAK,CAAC,EAAE;QAC3B,IAAIA,IAAI,CAACG,QAAQ,EAAEH,IAAI,CAACG,QAAQ,GAAG,IAAI;MACzC,CAAC;MAEDuG,gBAAgBA,CAAC5J,IAAI,EAAE;QACrBA,IAAI,CAACuB,MAAM,CAAC,CAAC;MACf,CAAC;MAEDsI,gBAAgBA,CAAC7J,IAAI,EAAE;QACrB,MAAM;UAAEkD;QAAK,CAAC,GAAGlD,IAAI;QACrB,IAAIkD,IAAI,CAACf,OAAO,EAAE;UAChBzB,UAAU,CAACV,IAAI,CAAC;QAClB;MACF,CAAC;MAED8J,KAAKA,CAAC9J,IAAI,EAAE;QACV,MAAM;UAAEkD;QAAkD,CAAC,GAAGlD,IAAI;QAElE,IAAIkD,IAAI,CAAC6G,cAAc,EAAE7G,IAAI,CAAC6G,cAAc,GAAG,IAAI;QAI5C;UAEL,IAAI7G,IAAI,CAAC8G,mBAAmB,EAAE9G,IAAI,CAAC8G,mBAAmB,GAAG,IAAI;QAC/D;QACA,IAAI9G,IAAI,CAAC+G,UAAU,EAAE/G,IAAI,CAAC+G,UAAU,GAAG,IAAI;QAC3C,IAAI/G,IAAI,CAACK,QAAQ,EAAEL,IAAI,CAACK,QAAQ,GAAG,IAAI;QAMvCvD,IAAI,CAAC8B,GAAG,CAAC,WAAW,CAAC,CAACoI,OAAO,CAACC,KAAK,IAAI;UACrC,IAAIA,KAAK,CAACC,aAAa,CAAC,CAAC,IAAID,KAAK,CAACE,oBAAoB,CAAC,CAAC,EAAE;YACzD,IAAIF,KAAK,CAACjH,IAAI,CAACoH,IAAI,KAAK,aAAa,EAAE;cACrCtH,mBAAmB,CAACc,WAAW,CAE7BqG,KAAK,EACLnK,IACF,CAAC;YACH,CAAC,MAAM;cACLgD,mBAAmB,CAACa,MAAM,CAACsG,KAAK,CAAC;YACnC;UACF,CAAC,MAAM,IACLA,KAAK,CAACI,eAAe,CAAC,CAAC,IACvBJ,KAAK,CAAC7G,sBAAsB,CAAC,CAAC,IAC9B6G,KAAK,CAACK,uBAAuB,CAAC,CAAC,EAC/B;YACAxH,mBAAmB,CAACC,KAAK,CAACkH,KAAK,CAAC;UAClC;QACF,CAAC,CAAC;MACJ,CAAC;MAEDM,QAAQA,CAACzK,IAAI,EAAE;QACb,MAAM;UAAEkD;QAAK,CAAC,GAAGlD,IAAI;QACrB,IAAIkD,IAAI,CAAC6G,cAAc,EAAE7G,IAAI,CAAC6G,cAAc,GAAG,IAAI;QACnD,IAAI7G,IAAI,CAACwH,UAAU,EAAExH,IAAI,CAACwH,UAAU,GAAG,IAAI;QAE3C,MAAMC,MAAM,GAAGzH,IAAI,CAACyH,MAAM;QAC1B,IAAIA,MAAM,CAACzD,MAAM,GAAG,CAAC,IAAI5E,CAAC,CAACiC,YAAY,CAACoG,MAAM,CAAC,CAAC,CAAC,EAAE;UAAE9J,IAAI,EAAE;QAAO,CAAC,CAAC,EAAE;UACpE8J,MAAM,CAACC,KAAK,CAAC,CAAC;QAChB;MACF,CAAC;MAEDC,mBAAmBA,CAAC7K,IAAI,EAAE;QACxB,IAAA8K,kBAAkB,EAAC9K,IAAI,EAAE0C,eAAe,CAAC;MAC3C,CAAC;MAEDqI,sBAAsBA,CAAC/K,IAAI,EAAE;QAC3BA,IAAI,CAACuB,MAAM,CAAC,CAAC;MACf,CAAC;MAEDyJ,sBAAsBA,CAAChL,IAAI,EAAE;QAC3BA,IAAI,CAACuB,MAAM,CAAC,CAAC;MACf,CAAC;MAED0J,iBAAiBA,CAACjL,IAAI,EAAE;QACtB,IAAI8C,kBAAkB,IAAI9C,IAAI,CAACkD,IAAI,CAACgI,KAAK,EAAE;UACzC,IAAAC,kBAAkB,EAACnL,IAAI,EAAuBsC,CAAC,CAAC;QAClD,CAAC,MAAM;UACL,IAAA8I,aAAa,EAACpL,IAAI,EAAEsC,CAAC,CAAC;QACxB;MACF,CAAC;MAED+I,yBAAyBA,CACvBrL,IAA2C,EAC3CyB,IAAI,EACJ;QACA,MAAM;UAAE6C,EAAE;UAAEgH;QAAgB,CAAC,GAAGtL,IAAI,CAACkD,IAAI;QAEzC,IAAIqI,IAAkB;QACtB,IAAIC,OAAwB;QAC5B,IAAIlJ,CAAC,CAACmJ,2BAA2B,CAACH,eAAe,CAAC,EAAE;UAElD9J,yBAAyB,CACvBxB,IAAI,EACJyB,IAAI,EACJ,UAAU6C,EAAE,CAACzD,IAAI,kBAAkB,EACnC,UAAUyD,EAAE,CAACzD,IAAI,cAAc,EAC/B,+DACF,CAAC;UACD0K,IAAI,GAAGjJ,CAAC,CAACoJ,cAAc,CAACpJ,CAAC,CAACnB,UAAU,CAAC,SAAS,CAAC,EAAE,CAC/CmK,eAAe,CAACK,UAAU,CAC3B,CAAC;UACFH,OAAO,GAAG,OAAO;QACnB,CAAC,MAAM;UAELD,IAAI,GAAGK,gBAAgB,CAACN,eAAe,CAAC;UACxCE,OAAO,GAAG,KAAK;QACjB;QACA,MAAMK,OAAO,GAAGvJ,CAAC,CAAC4G,mBAAmB,CAACsC,OAAO,EAAE,CAC7ClJ,CAAC,CAAC6G,kBAAkB,CAAC7E,EAAE,EAAEiH,IAAI,CAAC,CAC/B,CAAC;QAIK;UACLvL,IAAI,CAAC8E,WAAW,CAEd9E,IAAI,CAACkD,IAAI,CAACuE,QAAQ,GAAGnF,CAAC,CAAC+F,sBAAsB,CAACwD,OAAO,CAAC,GAAGA,OAC3D,CAAC;QACH;QACA7L,IAAI,CAACiB,KAAK,CAACmI,mBAAmB,CAACpJ,IAAI,CAAC;MACtC,CAAC;MAED8L,kBAAkBA,CAAC9L,IAAI,EAAEyB,IAAI,EAAE;QAC7BD,yBAAyB,CACvBxB,IAAI,EACJyB,IAAI,EACJ,mBAAmB,EACnB,yBACF,CAAC;QACDzB,IAAI,CAAC8E,WAAW,CACdvC,QAAQ,CAACoC,SAAS,CAACC,GAAG,oBAAoB5E,IAAI,CAACkD,IAAI,CAACyI,UAAU,EAChE,CAAC;MACH,CAAC;MAEDI,eAAeA,CAAC/L,IAAI,EAAE;QACpBA,IAAI,CAAC8E,WAAW,CAAC9E,IAAI,CAACkD,IAAI,CAACyI,UAAU,CAAC;MACxC,CAAC;MAED,CAAC,iBAECrJ,CAAC,CAAC0J,qBAAqB,GAAG,wBAAwB,GAAG,EAAE,EACvD,EAAEhM,IAA0D,EAAE;QAC9D,IAAI;UAAEkD;QAA6B,CAAC,GAAGlD,IAAI;QAC3C,GAAG;UACDkD,IAAI,GAAGA,IAAI,CAACyI,UAAU;QACxB,CAAC,QAAQrJ,CAAC,CAAC2J,gBAAgB,CAAC/I,IAAI,CAAC,IAAIZ,CAAC,CAAC4J,uBAAuB,YAAzB5J,CAAC,CAAC4J,uBAAuB,CAAGhJ,IAAI,CAAC;QACtElD,IAAI,CAAC8E,WAAW,CAAC5B,IAAI,CAAC;MACxB,CAAC;MAED,CAWId,GAAG,CAACC,KAAK,CAAC8J,yBAAyB,GACjC,+CAA+C,GAC/C,qBAAqB,EACzBnM,IAAmE,EACnE;QACAA,IAAI,CAAC8E,WAAW,CAAC9E,IAAI,CAACkD,IAAI,CAACyI,UAAU,CAAC;MACxC,CAAC;MAEDS,cAAcA,CAACpM,IAAI,EAAE;QAGZ;UAELA,IAAI,CAACkD,IAAI,CAAC6G,cAAc,GAAG,IAAI;QACjC;MACF,CAAC;MAEDsC,sBAAsBA,CAACrM,IAAI,EAAE;QAGpB;UAELA,IAAI,CAACkD,IAAI,CAAC6G,cAAc,GAAG,IAAI;QACjC;MACF,CAAC;MAEDuC,aAAaA,CAACtM,IAAI,EAAE;QAGX;UAELA,IAAI,CAACkD,IAAI,CAAC6G,cAAc,GAAG,IAAI;QACjC;MACF,CAAC;MAEDwC,iBAAiBA,CAACvM,IAAI,EAAE;QAIf;UAELA,IAAI,CAACkD,IAAI,CAAC6G,cAAc,GAAG,IAAI;QACjC;MACF,CAAC;MAEDyC,wBAAwBA,CAACxM,IAAI,EAAE;QAItB;UAELA,IAAI,CAACkD,IAAI,CAAC6G,cAAc,GAAG,IAAI;QACjC;MACF;IACF;EACF,CAAC;EAED,SAAS6B,gBAAgBA,CAAC1I,IAAoB,EAAgB;IAC5D,IAAIZ,CAAC,CAACmK,iBAAiB,CAACvJ,IAAI,CAAC,EAAE;MAC7B,OAAOZ,CAAC,CAACoK,gBAAgB,CAACd,gBAAgB,CAAC1I,IAAI,CAACuB,IAAI,CAAC,EAAEvB,IAAI,CAACyJ,KAAK,CAAC;IACpE;IAEA,OAAOzJ,IAAI;EACb;EAEA,SAASmC,YAAYA,CAAC;IACpBnC;EACkD,CAAC,EAAE;IAErD,IAAIA,IAAI,CAACS,cAAc,EAAET,IAAI,CAACS,cAAc,GAAG,IAAI;IACnD,IAAIrB,CAAC,CAACiC,YAAY,CAACrB,IAAI,CAAC,IAAIA,IAAI,CAACQ,QAAQ,EAAER,IAAI,CAACQ,QAAQ,GAAG,IAAI;EAEjE;EAEA,SAAS2D,gBAAgBA,CAAC;IACxBrG,OAAO;IACPsG,WAAW;IACXjB,gBAAgB;IAChBE;EAMF,CAAC,EAAE;IACD,KAAK,MAAMvG,IAAI,IAAIgB,OAAO,CAAC4L,cAAc,EAAE;MACzC,IAAI,CAAC7M,QAAQ,CAACC,IAAI,CAAC,EAAE;QACnB,OAAO,KAAK;MACd;IACF;IAEA,IACEgB,OAAO,CAACG,UAAU,CAACN,IAAI,KAAKwF,gBAAgB,IAC5CrF,OAAO,CAACG,UAAU,CAACN,IAAI,KAAK0F,oBAAoB,EAChD;MACA,OAAO,IAAI;IACb;IAGA,IAAIsG,gBAAgB,GAAG,KAAK;IAC5BvF,WAAW,CAACwF,QAAQ,CAAC;MACnB,wBAAwBC,CAAC/M,IAAI,EAAE;QAC7B6M,gBAAgB,GAAG,IAAI;QACvB7M,IAAI,CAACgN,IAAI,CAAC,CAAC;MACb;IACF,CAAC,CAAC;IACF,OAAO,CAACH,gBAAgB;EAC1B;AACF,CAAC,CAAC", "ignoreList": []}