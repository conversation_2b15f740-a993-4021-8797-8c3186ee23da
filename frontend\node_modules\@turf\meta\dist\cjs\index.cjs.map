{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-meta/dist/cjs/index.cjs", "../../index.js"], "names": ["feature"], "mappings": "AAAA;ACAA,wCAAqD;AAoCrD,SAAS,SAAA,CAAU,OAAA,EAAS,QAAA,EAAU,gBAAA,EAAkB;AAEtD,EAAA,GAAA,CAAI,QAAA,IAAY,IAAA,EAAM,MAAA;AACtB,EAAA,IAAI,CAAA,EACF,CAAA,EACA,CAAA,EACA,QAAA,EACA,KAAA,EACA,MAAA,EACA,uBAAA,EACA,WAAA,EAAa,CAAA,EACb,WAAA,EAAa,CAAA,EACb,oBAAA,EACA,KAAA,EAAO,OAAA,CAAQ,IAAA,EACf,oBAAA,EAAsB,KAAA,IAAS,mBAAA,EAC/B,UAAA,EAAY,KAAA,IAAS,SAAA,EACrB,KAAA,EAAO,oBAAA,EAAsB,OAAA,CAAQ,QAAA,CAAS,OAAA,EAAS,CAAA;AAczD,EAAA,IAAA,CAAA,IAAS,aAAA,EAAe,CAAA,EAAG,aAAA,EAAe,IAAA,EAAM,YAAA,EAAA,EAAgB;AAC9D,IAAA,wBAAA,EAA0B,oBAAA,EACtB,OAAA,CAAQ,QAAA,CAAS,YAAY,CAAA,CAAE,SAAA,EAC/B,UAAA,EACE,OAAA,CAAQ,SAAA,EACR,OAAA;AACN,IAAA,qBAAA,EAAuB,wBAAA,EACnB,uBAAA,CAAwB,KAAA,IAAS,qBAAA,EACjC,KAAA;AACJ,IAAA,MAAA,EAAQ,qBAAA,EACJ,uBAAA,CAAwB,UAAA,CAAW,OAAA,EACnC,CAAA;AAEJ,IAAA,IAAA,CAAA,IAAS,UAAA,EAAY,CAAA,EAAG,UAAA,EAAY,KAAA,EAAO,SAAA,EAAA,EAAa;AACtD,MAAA,IAAI,kBAAA,EAAoB,CAAA;AACxB,MAAA,IAAI,cAAA,EAAgB,CAAA;AACpB,MAAA,SAAA,EAAW,qBAAA,EACP,uBAAA,CAAwB,UAAA,CAAW,SAAS,EAAA,EAC5C,uBAAA;AAGJ,MAAA,GAAA,CAAI,SAAA,IAAa,IAAA,EAAM,QAAA;AACvB,MAAA,OAAA,EAAS,QAAA,CAAS,WAAA;AAClB,MAAA,IAAI,SAAA,EAAW,QAAA,CAAS,IAAA;AAExB,MAAA,WAAA,EACE,iBAAA,GAAA,CACC,SAAA,IAAa,UAAA,GAAa,SAAA,IAAa,cAAA,EAAA,EACpC,EAAA,EACA,CAAA;AAEN,MAAA,OAAA,CAAQ,QAAA,EAAU;AAAA,QAChB,KAAK,IAAA;AACH,UAAA,KAAA;AAAA,QACF,KAAK,OAAA;AACH,UAAA,GAAA,CACE,QAAA;AAAA,YACE,MAAA;AAAA,YACA,UAAA;AAAA,YACA,YAAA;AAAA,YACA,iBAAA;AAAA,YACA;AAAA,UACF,EAAA,IAAM,KAAA;AAEN,YAAA,OAAO,KAAA;AACT,UAAA,UAAA,EAAA;AACA,UAAA,iBAAA,EAAA;AACA,UAAA,KAAA;AAAA,QACF,KAAK,YAAA;AAAA,QACL,KAAK,YAAA;AACH,UAAA,IAAA,CAAK,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,MAAA,EAAQ,CAAA,EAAA,EAAK;AAClC,YAAA,GAAA,CACE,QAAA;AAAA,cACE,MAAA,CAAO,CAAC,CAAA;AAAA,cACR,UAAA;AAAA,cACA,YAAA;AAAA,cACA,iBAAA;AAAA,cACA;AAAA,YACF,EAAA,IAAM,KAAA;AAEN,cAAA,OAAO,KAAA;AACT,YAAA,UAAA,EAAA;AACA,YAAA,GAAA,CAAI,SAAA,IAAa,YAAA,EAAc,iBAAA,EAAA;AAAA,UACjC;AACA,UAAA,GAAA,CAAI,SAAA,IAAa,YAAA,EAAc,iBAAA,EAAA;AAC/B,UAAA,KAAA;AAAA,QACF,KAAK,SAAA;AAAA,QACL,KAAK,iBAAA;AACH,UAAA,IAAA,CAAK,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,MAAA,EAAQ,CAAA,EAAA,EAAK;AAClC,YAAA,IAAA,CAAK,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA,EAAS,UAAA,EAAY,CAAA,EAAA,EAAK;AAClD,cAAA,GAAA,CACE,QAAA;AAAA,gBACE,MAAA,CAAO,CAAC,CAAA,CAAE,CAAC,CAAA;AAAA,gBACX,UAAA;AAAA,gBACA,YAAA;AAAA,gBACA,iBAAA;AAAA,gBACA;AAAA,cACF,EAAA,IAAM,KAAA;AAEN,gBAAA,OAAO,KAAA;AACT,cAAA,UAAA,EAAA;AAAA,YACF;AACA,YAAA,GAAA,CAAI,SAAA,IAAa,iBAAA,EAAmB,iBAAA,EAAA;AACpC,YAAA,GAAA,CAAI,SAAA,IAAa,SAAA,EAAW,aAAA,EAAA;AAAA,UAC9B;AACA,UAAA,GAAA,CAAI,SAAA,IAAa,SAAA,EAAW,iBAAA,EAAA;AAC5B,UAAA,KAAA;AAAA,QACF,KAAK,cAAA;AACH,UAAA,IAAA,CAAK,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,MAAA,EAAQ,CAAA,EAAA,EAAK;AAClC,YAAA,cAAA,EAAgB,CAAA;AAChB,YAAA,IAAA,CAAK,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,CAAC,CAAA,CAAE,MAAA,EAAQ,CAAA,EAAA,EAAK;AACrC,cAAA,IAAA,CAAK,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,CAAC,CAAA,CAAE,CAAC,CAAA,CAAE,OAAA,EAAS,UAAA,EAAY,CAAA,EAAA,EAAK;AACrD,gBAAA,GAAA,CACE,QAAA;AAAA,kBACE,MAAA,CAAO,CAAC,CAAA,CAAE,CAAC,CAAA,CAAE,CAAC,CAAA;AAAA,kBACd,UAAA;AAAA,kBACA,YAAA;AAAA,kBACA,iBAAA;AAAA,kBACA;AAAA,gBACF,EAAA,IAAM,KAAA;AAEN,kBAAA,OAAO,KAAA;AACT,gBAAA,UAAA,EAAA;AAAA,cACF;AACA,cAAA,aAAA,EAAA;AAAA,YACF;AACA,YAAA,iBAAA,EAAA;AAAA,UACF;AACA,UAAA,KAAA;AAAA,QACF,KAAK,oBAAA;AACH,UAAA,IAAA,CAAK,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,QAAA,CAAS,UAAA,CAAW,MAAA,EAAQ,CAAA,EAAA;AAC1C,YAAA,GAAA,CACE,SAAA,CAAU,QAAA,CAAS,UAAA,CAAW,CAAC,CAAA,EAAG,QAAA,EAAU,gBAAgB,EAAA,IAC5D,KAAA;AAEA,cAAA,OAAO,KAAA;AACX,UAAA,KAAA;AAAA,QACF,OAAA;AACE,UAAA,MAAM,IAAI,KAAA,CAAM,uBAAuB,CAAA;AAAA,MAC3C;AAAA,IACF;AAAA,EACF;AACF;AAqDA,SAAS,WAAA,CAAY,OAAA,EAAS,QAAA,EAAU,YAAA,EAAc,gBAAA,EAAkB;AACtE,EAAA,IAAI,cAAA,EAAgB,YAAA;AACpB,EAAA,SAAA;AAAA,IACE,OAAA;AAAA,IACA,QAAA,CACE,YAAA,EACA,UAAA,EACA,YAAA,EACA,iBAAA,EACA,aAAA,EACA;AACA,MAAA,GAAA,CAAI,WAAA,IAAe,EAAA,GAAK,aAAA,IAAiB,KAAA,CAAA;AACvC,QAAA,cAAA,EAAgB,YAAA;AAAA,MAAA;AAEhB,QAAA,cAAA,EAAgB,QAAA;AAAA,UACd,aAAA;AAAA,UACA,YAAA;AAAA,UACA,UAAA;AAAA,UACA,YAAA;AAAA,UACA,iBAAA;AAAA,UACA;AAAA,QACF,CAAA;AAAA,IACJ,CAAA;AAAA,IACA;AAAA,EACF,CAAA;AACA,EAAA,OAAO,aAAA;AACT;AA6BA,SAAS,QAAA,CAAS,OAAA,EAAS,QAAA,EAAU;AACnC,EAAA,IAAI,CAAA;AACJ,EAAA,OAAA,CAAQ,OAAA,CAAQ,IAAA,EAAM;AAAA,IACpB,KAAK,mBAAA;AACH,MAAA,IAAA,CAAK,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,OAAA,CAAQ,QAAA,CAAS,MAAA,EAAQ,CAAA,EAAA,EAAK;AAC5C,QAAA,GAAA,CAAI,QAAA,CAAS,OAAA,CAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,UAAA,EAAY,CAAC,EAAA,IAAM,KAAA,EAAO,KAAA;AAAA,MAC7D;AACA,MAAA,KAAA;AAAA,IACF,KAAK,SAAA;AACH,MAAA,QAAA,CAAS,OAAA,CAAQ,UAAA,EAAY,CAAC,CAAA;AAC9B,MAAA,KAAA;AAAA,EACJ;AACF;AA+CA,SAAS,UAAA,CAAW,OAAA,EAAS,QAAA,EAAU,YAAA,EAAc;AACnD,EAAA,IAAI,cAAA,EAAgB,YAAA;AACpB,EAAA,QAAA,CAAS,OAAA,EAAS,QAAA,CAAU,iBAAA,EAAmB,YAAA,EAAc;AAC3D,IAAA,GAAA,CAAI,aAAA,IAAiB,EAAA,GAAK,aAAA,IAAiB,KAAA,CAAA;AACzC,MAAA,cAAA,EAAgB,iBAAA;AAAA,IAAA;AAEhB,MAAA,cAAA,EAAgB,QAAA,CAAS,aAAA,EAAe,iBAAA,EAAmB,YAAY,CAAA;AAAA,EAC3E,CAAC,CAAA;AACD,EAAA,OAAO,aAAA;AACT;AA8BA,SAAS,WAAA,CAAY,OAAA,EAAS,QAAA,EAAU;AACtC,EAAA,GAAA,CAAI,OAAA,CAAQ,KAAA,IAAS,SAAA,EAAW;AAC9B,IAAA,QAAA,CAAS,OAAA,EAAS,CAAC,CAAA;AAAA,EACrB,EAAA,KAAA,GAAA,CAAW,OAAA,CAAQ,KAAA,IAAS,mBAAA,EAAqB;AAC/C,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,OAAA,CAAQ,QAAA,CAAS,MAAA,EAAQ,CAAA,EAAA,EAAK;AAChD,MAAA,GAAA,CAAI,QAAA,CAAS,OAAA,CAAQ,QAAA,CAAS,CAAC,CAAA,EAAG,CAAC,EAAA,IAAM,KAAA,EAAO,KAAA;AAAA,IAClD;AAAA,EACF;AACF;AA6CA,SAAS,aAAA,CAAc,OAAA,EAAS,QAAA,EAAU,YAAA,EAAc;AACtD,EAAA,IAAI,cAAA,EAAgB,YAAA;AACpB,EAAA,WAAA,CAAY,OAAA,EAAS,QAAA,CAAU,cAAA,EAAgB,YAAA,EAAc;AAC3D,IAAA,GAAA,CAAI,aAAA,IAAiB,EAAA,GAAK,aAAA,IAAiB,KAAA,CAAA;AACzC,MAAA,cAAA,EAAgB,cAAA;AAAA,IAAA,KACb,cAAA,EAAgB,QAAA,CAAS,aAAA,EAAe,cAAA,EAAgB,YAAY,CAAA;AAAA,EAC3E,CAAC,CAAA;AACD,EAAA,OAAO,aAAA;AACT;AAiBA,SAAS,QAAA,CAAS,OAAA,EAAS;AACzB,EAAA,IAAI,OAAA,EAAS,CAAC,CAAA;AACd,EAAA,SAAA,CAAU,OAAA,EAAS,QAAA,CAAU,KAAA,EAAO;AAClC,IAAA,MAAA,CAAO,IAAA,CAAK,KAAK,CAAA;AAAA,EACnB,CAAC,CAAA;AACD,EAAA,OAAO,MAAA;AACT;AAmCA,SAAS,QAAA,CAAS,OAAA,EAAS,QAAA,EAAU;AACnC,EAAA,IAAI,CAAA,EACF,CAAA,EACA,CAAA,EACA,QAAA,EACA,KAAA,EACA,uBAAA,EACA,oBAAA,EACA,iBAAA,EACA,WAAA,EACA,SAAA,EACA,aAAA,EAAe,CAAA,EACf,oBAAA,EAAsB,OAAA,CAAQ,KAAA,IAAS,mBAAA,EACvC,UAAA,EAAY,OAAA,CAAQ,KAAA,IAAS,SAAA,EAC7B,KAAA,EAAO,oBAAA,EAAsB,OAAA,CAAQ,QAAA,CAAS,OAAA,EAAS,CAAA;AAczD,EAAA,IAAA,CAAK,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,IAAA,EAAM,CAAA,EAAA,EAAK;AACzB,IAAA,wBAAA,EAA0B,oBAAA,EACtB,OAAA,CAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,SAAA,EACpB,UAAA,EACE,OAAA,CAAQ,SAAA,EACR,OAAA;AACN,IAAA,kBAAA,EAAoB,oBAAA,EAChB,OAAA,CAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,WAAA,EACpB,UAAA,EACE,OAAA,CAAQ,WAAA,EACR,CAAC,CAAA;AACP,IAAA,YAAA,EAAc,oBAAA,EACV,OAAA,CAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,EACpB,UAAA,EACE,OAAA,CAAQ,KAAA,EACR,KAAA,CAAA;AACN,IAAA,UAAA,EAAY,oBAAA,EACR,OAAA,CAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,GAAA,EACpB,UAAA,EACE,OAAA,CAAQ,GAAA,EACR,KAAA,CAAA;AACN,IAAA,qBAAA,EAAuB,wBAAA,EACnB,uBAAA,CAAwB,KAAA,IAAS,qBAAA,EACjC,KAAA;AACJ,IAAA,MAAA,EAAQ,qBAAA,EACJ,uBAAA,CAAwB,UAAA,CAAW,OAAA,EACnC,CAAA;AAEJ,IAAA,IAAA,CAAK,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,EAAO,CAAA,EAAA,EAAK;AAC1B,MAAA,SAAA,EAAW,qBAAA,EACP,uBAAA,CAAwB,UAAA,CAAW,CAAC,EAAA,EACpC,uBAAA;AAGJ,MAAA,GAAA,CAAI,SAAA,IAAa,IAAA,EAAM;AACrB,QAAA,GAAA,CACE,QAAA;AAAA,UACE,IAAA;AAAA,UACA,YAAA;AAAA,UACA,iBAAA;AAAA,UACA,WAAA;AAAA,UACA;AAAA,QACF,EAAA,IAAM,KAAA;AAEN,UAAA,OAAO,KAAA;AACT,QAAA,QAAA;AAAA,MACF;AACA,MAAA,OAAA,CAAQ,QAAA,CAAS,IAAA,EAAM;AAAA,QACrB,KAAK,OAAA;AAAA,QACL,KAAK,YAAA;AAAA,QACL,KAAK,YAAA;AAAA,QACL,KAAK,SAAA;AAAA,QACL,KAAK,iBAAA;AAAA,QACL,KAAK,cAAA,EAAgB;AACnB,UAAA,GAAA,CACE,QAAA;AAAA,YACE,QAAA;AAAA,YACA,YAAA;AAAA,YACA,iBAAA;AAAA,YACA,WAAA;AAAA,YACA;AAAA,UACF,EAAA,IAAM,KAAA;AAEN,YAAA,OAAO,KAAA;AACT,UAAA,KAAA;AAAA,QACF;AAAA,QACA,KAAK,oBAAA,EAAsB;AACzB,UAAA,IAAA,CAAK,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,QAAA,CAAS,UAAA,CAAW,MAAA,EAAQ,CAAA,EAAA,EAAK;AAC/C,YAAA,GAAA,CACE,QAAA;AAAA,cACE,QAAA,CAAS,UAAA,CAAW,CAAC,CAAA;AAAA,cACrB,YAAA;AAAA,cACA,iBAAA;AAAA,cACA,WAAA;AAAA,cACA;AAAA,YACF,EAAA,IAAM,KAAA;AAEN,cAAA,OAAO,KAAA;AAAA,UACX;AACA,UAAA,KAAA;AAAA,QACF;AAAA,QACA,OAAA;AACE,UAAA,MAAM,IAAI,KAAA,CAAM,uBAAuB,CAAA;AAAA,MAC3C;AAAA,IACF;AAEA,IAAA,YAAA,EAAA;AAAA,EACF;AACF;AAmDA,SAAS,UAAA,CAAW,OAAA,EAAS,QAAA,EAAU,YAAA,EAAc;AACnD,EAAA,IAAI,cAAA,EAAgB,YAAA;AACpB,EAAA,QAAA;AAAA,IACE,OAAA;AAAA,IACA,QAAA,CACE,eAAA,EACA,YAAA,EACA,iBAAA,EACA,WAAA,EACA,SAAA,EACA;AACA,MAAA,GAAA,CAAI,aAAA,IAAiB,EAAA,GAAK,aAAA,IAAiB,KAAA,CAAA;AACzC,QAAA,cAAA,EAAgB,eAAA;AAAA,MAAA;AAEhB,QAAA,cAAA,EAAgB,QAAA;AAAA,UACd,aAAA;AAAA,UACA,eAAA;AAAA,UACA,YAAA;AAAA,UACA,iBAAA;AAAA,UACA,WAAA;AAAA,UACA;AAAA,QACF,CAAA;AAAA,IACJ;AAAA,EACF,CAAA;AACA,EAAA,OAAO,aAAA;AACT;AAgCA,SAAS,WAAA,CAAY,OAAA,EAAS,QAAA,EAAU;AACtC,EAAA,QAAA,CAAS,OAAA,EAAS,QAAA,CAAU,QAAA,EAAU,YAAA,EAAc,UAAA,EAAY,IAAA,EAAM,EAAA,EAAI;AAExE,IAAA,IAAI,KAAA,EAAO,SAAA,IAAa,KAAA,EAAO,KAAA,EAAO,QAAA,CAAS,IAAA;AAC/C,IAAA,OAAA,CAAQ,IAAA,EAAM;AAAA,MACZ,KAAK,IAAA;AAAA,MACL,KAAK,OAAA;AAAA,MACL,KAAK,YAAA;AAAA,MACL,KAAK,SAAA;AACH,QAAA,GAAA,CACE,QAAA;AAAA,UACE,8BAAA,QAAQ,EAAU,UAAA,EAAY,EAAE,IAAA,EAAY,GAAO,CAAC,CAAA;AAAA,UACpD,YAAA;AAAA,UACA;AAAA,QACF,EAAA,IAAM,KAAA;AAEN,UAAA,OAAO,KAAA;AACT,QAAA,MAAA;AAAA,IACJ;AAEA,IAAA,IAAI,QAAA;AAGJ,IAAA,OAAA,CAAQ,IAAA,EAAM;AAAA,MACZ,KAAK,YAAA;AACH,QAAA,SAAA,EAAW,OAAA;AACX,QAAA,KAAA;AAAA,MACF,KAAK,iBAAA;AACH,QAAA,SAAA,EAAW,YAAA;AACX,QAAA,KAAA;AAAA,MACF,KAAK,cAAA;AACH,QAAA,SAAA,EAAW,SAAA;AACX,QAAA,KAAA;AAAA,IACJ;AAEA,IAAA,IAAA,CAAA,IACM,kBAAA,EAAoB,CAAA,EACxB,kBAAA,EAAoB,QAAA,CAAS,WAAA,CAAY,MAAA,EACzC,iBAAA,EAAA,EACA;AACA,MAAA,IAAI,WAAA,EAAa,QAAA,CAAS,WAAA,CAAY,iBAAiB,CAAA;AACvD,MAAA,IAAI,KAAA,EAAO;AAAA,QACT,IAAA,EAAM,QAAA;AAAA,QACN,WAAA,EAAa;AAAA,MACf,CAAA;AACA,MAAA,GAAA,CACE,QAAA,CAAS,8BAAA,IAAQ,EAAM,UAAU,CAAA,EAAG,YAAA,EAAc,iBAAiB,EAAA,IACnE,KAAA;AAEA,QAAA,OAAO,KAAA;AAAA,IACX;AAAA,EACF,CAAC,CAAA;AACH;AA+CA,SAAS,aAAA,CAAc,OAAA,EAAS,QAAA,EAAU,YAAA,EAAc;AACtD,EAAA,IAAI,cAAA,EAAgB,YAAA;AACpB,EAAA,WAAA;AAAA,IACE,OAAA;AAAA,IACA,QAAA,CAAU,cAAA,EAAgB,YAAA,EAAc,iBAAA,EAAmB;AACzD,MAAA,GAAA,CACE,aAAA,IAAiB,EAAA,GACjB,kBAAA,IAAsB,EAAA,GACtB,aAAA,IAAiB,KAAA,CAAA;AAEjB,QAAA,cAAA,EAAgB,cAAA;AAAA,MAAA;AAEhB,QAAA,cAAA,EAAgB,QAAA;AAAA,UACd,aAAA;AAAA,UACA,cAAA;AAAA,UACA,YAAA;AAAA,UACA;AAAA,QACF,CAAA;AAAA,IACJ;AAAA,EACF,CAAA;AACA,EAAA,OAAO,aAAA;AACT;AAuCA,SAAS,WAAA,CAAY,OAAA,EAAS,QAAA,EAAU;AACtC,EAAA,WAAA,CAAY,OAAA,EAAS,QAAA,CAAUA,QAAAA,EAAS,YAAA,EAAc,iBAAA,EAAmB;AACvE,IAAA,IAAI,aAAA,EAAe,CAAA;AAGnB,IAAA,GAAA,CAAI,CAACA,QAAAA,CAAQ,QAAA,EAAU,MAAA;AAEvB,IAAA,IAAI,KAAA,EAAOA,QAAAA,CAAQ,QAAA,CAAS,IAAA;AAC5B,IAAA,GAAA,CAAI,KAAA,IAAS,QAAA,GAAW,KAAA,IAAS,YAAA,EAAc,MAAA;AAG/C,IAAA,IAAI,cAAA;AACJ,IAAA,IAAI,qBAAA,EAAuB,CAAA;AAC3B,IAAA,IAAI,mBAAA,EAAqB,CAAA;AACzB,IAAA,IAAI,cAAA,EAAgB,CAAA;AACpB,IAAA,GAAA,CACE,SAAA;AAAA,MACEA,QAAAA;AAAA,MACA,QAAA,CACE,YAAA,EACA,UAAA,EACA,iBAAA,EACA,mBAAA,EACA,aAAA,EACA;AAEA,QAAA,GAAA,CACE,eAAA,IAAmB,KAAA,EAAA,GACnB,aAAA,EAAe,qBAAA,GACf,oBAAA,EAAsB,mBAAA,GACtB,cAAA,EAAgB,aAAA,EAChB;AACA,UAAA,eAAA,EAAiB,YAAA;AACjB,UAAA,qBAAA,EAAuB,YAAA;AACvB,UAAA,mBAAA,EAAqB,mBAAA;AACrB,UAAA,cAAA,EAAgB,aAAA;AAChB,UAAA,aAAA,EAAe,CAAA;AACf,UAAA,MAAA;AAAA,QACF;AACA,QAAA,IAAI,eAAA,EAAiB,iCAAA;AAAA,UACnB,CAAC,cAAA,EAAgB,YAAY,CAAA;AAAA,UAC7BA,QAAAA,CAAQ;AAAA,QACV,CAAA;AACA,QAAA,GAAA,CACE,QAAA;AAAA,UACE,cAAA;AAAA,UACA,YAAA;AAAA,UACA,iBAAA;AAAA,UACA,aAAA;AAAA,UACA;AAAA,QACF,EAAA,IAAM,KAAA;AAEN,UAAA,OAAO,KAAA;AACT,QAAA,YAAA,EAAA;AACA,QAAA,eAAA,EAAiB,YAAA;AAAA,MACnB;AAAA,IACF,EAAA,IAAM,KAAA;AAEN,MAAA,OAAO,KAAA;AAAA,EACX,CAAC,CAAA;AACH;AAwDA,SAAS,aAAA,CAAc,OAAA,EAAS,QAAA,EAAU,YAAA,EAAc;AACtD,EAAA,IAAI,cAAA,EAAgB,YAAA;AACpB,EAAA,IAAI,QAAA,EAAU,KAAA;AACd,EAAA,WAAA;AAAA,IACE,OAAA;AAAA,IACA,QAAA,CACE,cAAA,EACA,YAAA,EACA,iBAAA,EACA,aAAA,EACA,YAAA,EACA;AACA,MAAA,GAAA,CAAI,QAAA,IAAY,MAAA,GAAS,aAAA,IAAiB,KAAA,CAAA;AACxC,QAAA,cAAA,EAAgB,cAAA;AAAA,MAAA;AAEhB,QAAA,cAAA,EAAgB,QAAA;AAAA,UACd,aAAA;AAAA,UACA,cAAA;AAAA,UACA,YAAA;AAAA,UACA,iBAAA;AAAA,UACA,aAAA;AAAA,UACA;AAAA,QACF,CAAA;AACF,MAAA,QAAA,EAAU,IAAA;AAAA,IACZ;AAAA,EACF,CAAA;AACA,EAAA,OAAO,aAAA;AACT;AAkCA,SAAS,QAAA,CAAS,OAAA,EAAS,QAAA,EAAU;AAEnC,EAAA,GAAA,CAAI,CAAC,OAAA,EAAS,MAAM,IAAI,KAAA,CAAM,qBAAqB,CAAA;AAEnD,EAAA,WAAA,CAAY,OAAA,EAAS,QAAA,CAAUA,QAAAA,EAAS,YAAA,EAAc,iBAAA,EAAmB;AACvE,IAAA,GAAA,CAAIA,QAAAA,CAAQ,SAAA,IAAa,IAAA,EAAM,MAAA;AAC/B,IAAA,IAAI,KAAA,EAAOA,QAAAA,CAAQ,QAAA,CAAS,IAAA;AAC5B,IAAA,IAAI,OAAA,EAASA,QAAAA,CAAQ,QAAA,CAAS,WAAA;AAC9B,IAAA,OAAA,CAAQ,IAAA,EAAM;AAAA,MACZ,KAAK,YAAA;AACH,QAAA,GAAA,CAAI,QAAA,CAASA,QAAAA,EAAS,YAAA,EAAc,iBAAA,EAAmB,CAAA,EAAG,CAAC,EAAA,IAAM,KAAA;AAC/D,UAAA,OAAO,KAAA;AACT,QAAA,KAAA;AAAA,MACF,KAAK,SAAA;AACH,QAAA,IAAA,CAAA,IACM,cAAA,EAAgB,CAAA,EACpB,cAAA,EAAgB,MAAA,CAAO,MAAA,EACvB,aAAA,EAAA,EACA;AACA,UAAA,GAAA,CACE,QAAA;AAAA,YACE,iCAAA,MAAW,CAAO,aAAa,CAAA,EAAGA,QAAAA,CAAQ,UAAU,CAAA;AAAA,YACpD,YAAA;AAAA,YACA,iBAAA;AAAA,YACA;AAAA,UACF,EAAA,IAAM,KAAA;AAEN,YAAA,OAAO,KAAA;AAAA,QACX;AACA,QAAA,KAAA;AAAA,IACJ;AAAA,EACF,CAAC,CAAA;AACH;AAiDA,SAAS,UAAA,CAAW,OAAA,EAAS,QAAA,EAAU,YAAA,EAAc;AACnD,EAAA,IAAI,cAAA,EAAgB,YAAA;AACpB,EAAA,QAAA;AAAA,IACE,OAAA;AAAA,IACA,QAAA,CAAU,WAAA,EAAa,YAAA,EAAc,iBAAA,EAAmB,aAAA,EAAe;AACrE,MAAA,GAAA,CAAI,aAAA,IAAiB,EAAA,GAAK,aAAA,IAAiB,KAAA,CAAA;AACzC,QAAA,cAAA,EAAgB,WAAA;AAAA,MAAA;AAEhB,QAAA,cAAA,EAAgB,QAAA;AAAA,UACd,aAAA;AAAA,UACA,WAAA;AAAA,UACA,YAAA;AAAA,UACA,iBAAA;AAAA,UACA;AAAA,QACF,CAAA;AAAA,IACJ;AAAA,EACF,CAAA;AACA,EAAA,OAAO,aAAA;AACT;AAoCA,SAAS,WAAA,CAAY,OAAA,EAAS,OAAA,EAAS;AAErC,EAAA,QAAA,EAAU,QAAA,GAAW,CAAC,CAAA;AACtB,EAAA,GAAA,CAAI,CAAC,+BAAA,OAAgB,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AAC5D,EAAA,IAAI,aAAA,EAAe,OAAA,CAAQ,aAAA,GAAgB,CAAA;AAC3C,EAAA,IAAI,kBAAA,EAAoB,OAAA,CAAQ,kBAAA,GAAqB,CAAA;AACrD,EAAA,IAAI,cAAA,EAAgB,OAAA,CAAQ,cAAA,GAAiB,CAAA;AAC7C,EAAA,IAAI,aAAA,EAAe,OAAA,CAAQ,aAAA,GAAgB,CAAA;AAG3C,EAAA,IAAI,WAAA,EAAa,OAAA,CAAQ,UAAA;AACzB,EAAA,IAAI,QAAA;AAEJ,EAAA,OAAA,CAAQ,OAAA,CAAQ,IAAA,EAAM;AAAA,IACpB,KAAK,mBAAA;AACH,MAAA,GAAA,CAAI,aAAA,EAAe,CAAA;AACjB,QAAA,aAAA,EAAe,OAAA,CAAQ,QAAA,CAAS,OAAA,EAAS,YAAA;AAC3C,MAAA,WAAA,EAAa,WAAA,GAAc,OAAA,CAAQ,QAAA,CAAS,YAAY,CAAA,CAAE,UAAA;AAC1D,MAAA,SAAA,EAAW,OAAA,CAAQ,QAAA,CAAS,YAAY,CAAA,CAAE,QAAA;AAC1C,MAAA,KAAA;AAAA,IACF,KAAK,SAAA;AACH,MAAA,WAAA,EAAa,WAAA,GAAc,OAAA,CAAQ,UAAA;AACnC,MAAA,SAAA,EAAW,OAAA,CAAQ,QAAA;AACnB,MAAA,KAAA;AAAA,IACF,KAAK,OAAA;AAAA,IACL,KAAK,YAAA;AACH,MAAA,OAAO,IAAA;AAAA,IACT,KAAK,YAAA;AAAA,IACL,KAAK,SAAA;AAAA,IACL,KAAK,iBAAA;AAAA,IACL,KAAK,cAAA;AACH,MAAA,SAAA,EAAW,OAAA;AACX,MAAA,KAAA;AAAA,IACF,OAAA;AACE,MAAA,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AAAA,EACxC;AAGA,EAAA,GAAA,CAAI,SAAA,IAAa,IAAA,EAAM,OAAO,IAAA;AAC9B,EAAA,IAAI,OAAA,EAAS,QAAA,CAAS,WAAA;AACtB,EAAA,OAAA,CAAQ,QAAA,CAAS,IAAA,EAAM;AAAA,IACrB,KAAK,OAAA;AAAA,IACL,KAAK,YAAA;AACH,MAAA,OAAO,IAAA;AAAA,IACT,KAAK,YAAA;AACH,MAAA,GAAA,CAAI,aAAA,EAAe,CAAA,EAAG,aAAA,EAAe,MAAA,CAAO,OAAA,EAAS,aAAA,EAAe,CAAA;AACpE,MAAA,OAAO,iCAAA;AAAA,QACL,CAAC,MAAA,CAAO,YAAY,CAAA,EAAG,MAAA,CAAO,aAAA,EAAe,CAAC,CAAC,CAAA;AAAA,QAC/C,UAAA;AAAA,QACA;AAAA,MACF,CAAA;AAAA,IACF,KAAK,SAAA;AACH,MAAA,GAAA,CAAI,cAAA,EAAgB,CAAA,EAAG,cAAA,EAAgB,MAAA,CAAO,OAAA,EAAS,aAAA;AACvD,MAAA,GAAA,CAAI,aAAA,EAAe,CAAA;AACjB,QAAA,aAAA,EAAe,MAAA,CAAO,aAAa,CAAA,CAAE,OAAA,EAAS,aAAA,EAAe,CAAA;AAC/D,MAAA,OAAO,iCAAA;AAAA,QACL;AAAA,UACE,MAAA,CAAO,aAAa,CAAA,CAAE,YAAY,CAAA;AAAA,UAClC,MAAA,CAAO,aAAa,CAAA,CAAE,aAAA,EAAe,CAAC;AAAA,QACxC,CAAA;AAAA,QACA,UAAA;AAAA,QACA;AAAA,MACF,CAAA;AAAA,IACF,KAAK,iBAAA;AACH,MAAA,GAAA,CAAI,kBAAA,EAAoB,CAAA;AACtB,QAAA,kBAAA,EAAoB,MAAA,CAAO,OAAA,EAAS,iBAAA;AACtC,MAAA,GAAA,CAAI,aAAA,EAAe,CAAA;AACjB,QAAA,aAAA,EAAe,MAAA,CAAO,iBAAiB,CAAA,CAAE,OAAA,EAAS,aAAA,EAAe,CAAA;AACnE,MAAA,OAAO,iCAAA;AAAA,QACL;AAAA,UACE,MAAA,CAAO,iBAAiB,CAAA,CAAE,YAAY,CAAA;AAAA,UACtC,MAAA,CAAO,iBAAiB,CAAA,CAAE,aAAA,EAAe,CAAC;AAAA,QAC5C,CAAA;AAAA,QACA,UAAA;AAAA,QACA;AAAA,MACF,CAAA;AAAA,IACF,KAAK,cAAA;AACH,MAAA,GAAA,CAAI,kBAAA,EAAoB,CAAA;AACtB,QAAA,kBAAA,EAAoB,MAAA,CAAO,OAAA,EAAS,iBAAA;AACtC,MAAA,GAAA,CAAI,cAAA,EAAgB,CAAA;AAClB,QAAA,cAAA,EAAgB,MAAA,CAAO,iBAAiB,CAAA,CAAE,OAAA,EAAS,aAAA;AACrD,MAAA,GAAA,CAAI,aAAA,EAAe,CAAA;AACjB,QAAA,aAAA,EACE,MAAA,CAAO,iBAAiB,CAAA,CAAE,aAAa,CAAA,CAAE,OAAA,EAAS,aAAA,EAAe,CAAA;AACrE,MAAA,OAAO,iCAAA;AAAA,QACL;AAAA,UACE,MAAA,CAAO,iBAAiB,CAAA,CAAE,aAAa,CAAA,CAAE,YAAY,CAAA;AAAA,UACrD,MAAA,CAAO,iBAAiB,CAAA,CAAE,aAAa,CAAA,CAAE,aAAA,EAAe,CAAC;AAAA,QAC3D,CAAA;AAAA,QACA,UAAA;AAAA,QACA;AAAA,MACF,CAAA;AAAA,EACJ;AACA,EAAA,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AACtC;AAmCA,SAAS,SAAA,CAAU,OAAA,EAAS,OAAA,EAAS;AAEnC,EAAA,QAAA,EAAU,QAAA,GAAW,CAAC,CAAA;AACtB,EAAA,GAAA,CAAI,CAAC,+BAAA,OAAgB,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AAC5D,EAAA,IAAI,aAAA,EAAe,OAAA,CAAQ,aAAA,GAAgB,CAAA;AAC3C,EAAA,IAAI,kBAAA,EAAoB,OAAA,CAAQ,kBAAA,GAAqB,CAAA;AACrD,EAAA,IAAI,cAAA,EAAgB,OAAA,CAAQ,cAAA,GAAiB,CAAA;AAC7C,EAAA,IAAI,WAAA,EAAa,OAAA,CAAQ,WAAA,GAAc,CAAA;AAGvC,EAAA,IAAI,WAAA,EAAa,OAAA,CAAQ,UAAA;AACzB,EAAA,IAAI,QAAA;AAEJ,EAAA,OAAA,CAAQ,OAAA,CAAQ,IAAA,EAAM;AAAA,IACpB,KAAK,mBAAA;AACH,MAAA,GAAA,CAAI,aAAA,EAAe,CAAA;AACjB,QAAA,aAAA,EAAe,OAAA,CAAQ,QAAA,CAAS,OAAA,EAAS,YAAA;AAC3C,MAAA,WAAA,EAAa,WAAA,GAAc,OAAA,CAAQ,QAAA,CAAS,YAAY,CAAA,CAAE,UAAA;AAC1D,MAAA,SAAA,EAAW,OAAA,CAAQ,QAAA,CAAS,YAAY,CAAA,CAAE,QAAA;AAC1C,MAAA,KAAA;AAAA,IACF,KAAK,SAAA;AACH,MAAA,WAAA,EAAa,WAAA,GAAc,OAAA,CAAQ,UAAA;AACnC,MAAA,SAAA,EAAW,OAAA,CAAQ,QAAA;AACnB,MAAA,KAAA;AAAA,IACF,KAAK,OAAA;AAAA,IACL,KAAK,YAAA;AACH,MAAA,OAAO,IAAA;AAAA,IACT,KAAK,YAAA;AAAA,IACL,KAAK,SAAA;AAAA,IACL,KAAK,iBAAA;AAAA,IACL,KAAK,cAAA;AACH,MAAA,SAAA,EAAW,OAAA;AACX,MAAA,KAAA;AAAA,IACF,OAAA;AACE,MAAA,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AAAA,EACxC;AAGA,EAAA,GAAA,CAAI,SAAA,IAAa,IAAA,EAAM,OAAO,IAAA;AAC9B,EAAA,IAAI,OAAA,EAAS,QAAA,CAAS,WAAA;AACtB,EAAA,OAAA,CAAQ,QAAA,CAAS,IAAA,EAAM;AAAA,IACrB,KAAK,OAAA;AACH,MAAA,OAAO,4BAAA,MAAM,EAAQ,UAAA,EAAY,OAAO,CAAA;AAAA,IAC1C,KAAK,YAAA;AACH,MAAA,GAAA,CAAI,kBAAA,EAAoB,CAAA;AACtB,QAAA,kBAAA,EAAoB,MAAA,CAAO,OAAA,EAAS,iBAAA;AACtC,MAAA,OAAO,4BAAA,MAAM,CAAO,iBAAiB,CAAA,EAAG,UAAA,EAAY,OAAO,CAAA;AAAA,IAC7D,KAAK,YAAA;AACH,MAAA,GAAA,CAAI,WAAA,EAAa,CAAA,EAAG,WAAA,EAAa,MAAA,CAAO,OAAA,EAAS,UAAA;AACjD,MAAA,OAAO,4BAAA,MAAM,CAAO,UAAU,CAAA,EAAG,UAAA,EAAY,OAAO,CAAA;AAAA,IACtD,KAAK,SAAA;AACH,MAAA,GAAA,CAAI,cAAA,EAAgB,CAAA,EAAG,cAAA,EAAgB,MAAA,CAAO,OAAA,EAAS,aAAA;AACvD,MAAA,GAAA,CAAI,WAAA,EAAa,CAAA;AACf,QAAA,WAAA,EAAa,MAAA,CAAO,aAAa,CAAA,CAAE,OAAA,EAAS,UAAA;AAC9C,MAAA,OAAO,4BAAA,MAAM,CAAO,aAAa,CAAA,CAAE,UAAU,CAAA,EAAG,UAAA,EAAY,OAAO,CAAA;AAAA,IACrE,KAAK,iBAAA;AACH,MAAA,GAAA,CAAI,kBAAA,EAAoB,CAAA;AACtB,QAAA,kBAAA,EAAoB,MAAA,CAAO,OAAA,EAAS,iBAAA;AACtC,MAAA,GAAA,CAAI,WAAA,EAAa,CAAA;AACf,QAAA,WAAA,EAAa,MAAA,CAAO,iBAAiB,CAAA,CAAE,OAAA,EAAS,UAAA;AAClD,MAAA,OAAO,4BAAA,MAAM,CAAO,iBAAiB,CAAA,CAAE,UAAU,CAAA,EAAG,UAAA,EAAY,OAAO,CAAA;AAAA,IACzE,KAAK,cAAA;AACH,MAAA,GAAA,CAAI,kBAAA,EAAoB,CAAA;AACtB,QAAA,kBAAA,EAAoB,MAAA,CAAO,OAAA,EAAS,iBAAA;AACtC,MAAA,GAAA,CAAI,cAAA,EAAgB,CAAA;AAClB,QAAA,cAAA,EAAgB,MAAA,CAAO,iBAAiB,CAAA,CAAE,OAAA,EAAS,aAAA;AACrD,MAAA,GAAA,CAAI,WAAA,EAAa,CAAA;AACf,QAAA,WAAA,EACE,MAAA,CAAO,iBAAiB,CAAA,CAAE,aAAa,CAAA,CAAE,OAAA,EAAS,UAAA;AACtD,MAAA,OAAO,4BAAA;AAAA,QACL,MAAA,CAAO,iBAAiB,CAAA,CAAE,aAAa,CAAA,CAAE,UAAU,CAAA;AAAA,QACnD,UAAA;AAAA,QACA;AAAA,MACF,CAAA;AAAA,EACJ;AACA,EAAA,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AACtC;AD90BA;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACF,wjBAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-meta/dist/cjs/index.cjs", "sourcesContent": [null, "import { feature, point, lineString, isObject } from \"@turf/helpers\";\n\n/**\n * Callback for coordEach\n *\n * @callback coordEachCallback\n * @param {number[]} currentCoord The current coordinate being processed.\n * @param {number} coordIndex The current index of the coordinate being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @param {number} multiFeatureIndex The current index of the Multi-Feature being processed.\n * @param {number} geometryIndex The current index of the Geometry being processed.\n * @returns {void}\n */\n\n/**\n * Iterate over coordinates in any GeoJSON object, similar to Array.forEach()\n *\n * @function\n * @param {AllGeoJSON} geojson any GeoJSON object\n * @param {coordEachCallback} callback a method that takes (currentCoord, coordIndex, featureIndex, multiFeatureIndex)\n * @param {boolean} [excludeWrapCoord=false] whether or not to include the final coordinate of LinearRings that wraps the ring in its iteration.\n * @returns {void}\n * @example\n * var features = turf.featureCollection([\n *   turf.point([26, 37], {\"foo\": \"bar\"}),\n *   turf.point([36, 53], {\"hello\": \"world\"})\n * ]);\n *\n * turf.coordEach(features, function (currentCoord, coordIndex, featureIndex, multiFeatureIndex, geometryIndex) {\n *   //=currentCoord\n *   //=coordIndex\n *   //=featureIndex\n *   //=multiFeatureIndex\n *   //=geometryIndex\n * });\n */\nfunction coordEach(geojson, callback, excludeWrapCoord) {\n  // Handles null Geometry -- Skips this GeoJSON\n  if (geojson === null) return;\n  var j,\n    k,\n    l,\n    geometry,\n    stopG,\n    coords,\n    geometryMaybeCollection,\n    wrapShrink = 0,\n    coordIndex = 0,\n    isGeometryCollection,\n    type = geojson.type,\n    isFeatureCollection = type === \"FeatureCollection\",\n    isFeature = type === \"Feature\",\n    stop = isFeatureCollection ? geojson.features.length : 1;\n\n  // This logic may look a little weird. The reason why it is that way\n  // is because it's trying to be fast. GeoJSON supports multiple kinds\n  // of objects at its root: FeatureCollection, Features, Geometries.\n  // This function has the responsibility of handling all of them, and that\n  // means that some of the `for` loops you see below actually just don't apply\n  // to certain inputs. For instance, if you give this just a\n  // Point geometry, then both loops are short-circuited and all we do\n  // is gradually rename the input until it's called 'geometry'.\n  //\n  // This also aims to allocate as few resources as possible: just a\n  // few numbers and booleans, rather than any temporary arrays as would\n  // be required with the normalization approach.\n  for (var featureIndex = 0; featureIndex < stop; featureIndex++) {\n    geometryMaybeCollection = isFeatureCollection\n      ? geojson.features[featureIndex].geometry\n      : isFeature\n        ? geojson.geometry\n        : geojson;\n    isGeometryCollection = geometryMaybeCollection\n      ? geometryMaybeCollection.type === \"GeometryCollection\"\n      : false;\n    stopG = isGeometryCollection\n      ? geometryMaybeCollection.geometries.length\n      : 1;\n\n    for (var geomIndex = 0; geomIndex < stopG; geomIndex++) {\n      var multiFeatureIndex = 0;\n      var geometryIndex = 0;\n      geometry = isGeometryCollection\n        ? geometryMaybeCollection.geometries[geomIndex]\n        : geometryMaybeCollection;\n\n      // Handles null Geometry -- Skips this geometry\n      if (geometry === null) continue;\n      coords = geometry.coordinates;\n      var geomType = geometry.type;\n\n      wrapShrink =\n        excludeWrapCoord &&\n        (geomType === \"Polygon\" || geomType === \"MultiPolygon\")\n          ? 1\n          : 0;\n\n      switch (geomType) {\n        case null:\n          break;\n        case \"Point\":\n          if (\n            callback(\n              coords,\n              coordIndex,\n              featureIndex,\n              multiFeatureIndex,\n              geometryIndex\n            ) === false\n          )\n            return false;\n          coordIndex++;\n          multiFeatureIndex++;\n          break;\n        case \"LineString\":\n        case \"MultiPoint\":\n          for (j = 0; j < coords.length; j++) {\n            if (\n              callback(\n                coords[j],\n                coordIndex,\n                featureIndex,\n                multiFeatureIndex,\n                geometryIndex\n              ) === false\n            )\n              return false;\n            coordIndex++;\n            if (geomType === \"MultiPoint\") multiFeatureIndex++;\n          }\n          if (geomType === \"LineString\") multiFeatureIndex++;\n          break;\n        case \"Polygon\":\n        case \"MultiLineString\":\n          for (j = 0; j < coords.length; j++) {\n            for (k = 0; k < coords[j].length - wrapShrink; k++) {\n              if (\n                callback(\n                  coords[j][k],\n                  coordIndex,\n                  featureIndex,\n                  multiFeatureIndex,\n                  geometryIndex\n                ) === false\n              )\n                return false;\n              coordIndex++;\n            }\n            if (geomType === \"MultiLineString\") multiFeatureIndex++;\n            if (geomType === \"Polygon\") geometryIndex++;\n          }\n          if (geomType === \"Polygon\") multiFeatureIndex++;\n          break;\n        case \"MultiPolygon\":\n          for (j = 0; j < coords.length; j++) {\n            geometryIndex = 0;\n            for (k = 0; k < coords[j].length; k++) {\n              for (l = 0; l < coords[j][k].length - wrapShrink; l++) {\n                if (\n                  callback(\n                    coords[j][k][l],\n                    coordIndex,\n                    featureIndex,\n                    multiFeatureIndex,\n                    geometryIndex\n                  ) === false\n                )\n                  return false;\n                coordIndex++;\n              }\n              geometryIndex++;\n            }\n            multiFeatureIndex++;\n          }\n          break;\n        case \"GeometryCollection\":\n          for (j = 0; j < geometry.geometries.length; j++)\n            if (\n              coordEach(geometry.geometries[j], callback, excludeWrapCoord) ===\n              false\n            )\n              return false;\n          break;\n        default:\n          throw new Error(\"Unknown Geometry Type\");\n      }\n    }\n  }\n}\n\n/**\n * Callback for coordReduce\n *\n * The first time the callback function is called, the values provided as arguments depend\n * on whether the reduce method has an initialValue argument.\n *\n * If an initialValue is provided to the reduce method:\n *  - The previousValue argument is initialValue.\n *  - The currentValue argument is the value of the first element present in the array.\n *\n * If an initialValue is not provided:\n *  - The previousValue argument is the value of the first element present in the array.\n *  - The currentValue argument is the value of the second element present in the array.\n *\n * @callback coordReduceCallback\n * @param {Reducer} previousValue The accumulated value previously returned in the last invocation\n * of the callback, or initialValue, if supplied.\n * @param {number[]} currentCoord The current coordinate being processed.\n * @param {number} coordIndex The current index of the coordinate being processed.\n * Starts at index 0, if an initialValue is provided, and at index 1 otherwise.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @param {number} multiFeatureIndex The current index of the Multi-Feature being processed.\n * @param {number} geometryIndex The current index of the Geometry being processed.\n * @returns {Reducer}\n */\n\n/**\n * Reduce coordinates in any GeoJSON object, similar to Array.reduce()\n *\n * @function\n * @param {AllGeoJSON} geojson any GeoJSON object\n * @param {coordReduceCallback} callback a method that takes (previousValue, currentCoord, coordIndex)\n * @param {Reducer} [initialValue] Value to use as the first argument to the first call of the callback.\n * @param {boolean} [excludeWrapCoord=false] whether or not to include the final coordinate of LinearRings that wraps the ring in its iteration.\n * @returns {Reducer} The value that results from the reduction.\n * @example\n * var features = turf.featureCollection([\n *   turf.point([26, 37], {\"foo\": \"bar\"}),\n *   turf.point([36, 53], {\"hello\": \"world\"})\n * ]);\n *\n * turf.coordReduce(features, function (previousValue, currentCoord, coordIndex, featureIndex, multiFeatureIndex, geometryIndex) {\n *   //=previousValue\n *   //=currentCoord\n *   //=coordIndex\n *   //=featureIndex\n *   //=multiFeatureIndex\n *   //=geometryIndex\n *   return currentCoord;\n * });\n */\nfunction coordReduce(geojson, callback, initialValue, excludeWrapCoord) {\n  var previousValue = initialValue;\n  coordEach(\n    geojson,\n    function (\n      currentCoord,\n      coordIndex,\n      featureIndex,\n      multiFeatureIndex,\n      geometryIndex\n    ) {\n      if (coordIndex === 0 && initialValue === undefined)\n        previousValue = currentCoord;\n      else\n        previousValue = callback(\n          previousValue,\n          currentCoord,\n          coordIndex,\n          featureIndex,\n          multiFeatureIndex,\n          geometryIndex\n        );\n    },\n    excludeWrapCoord\n  );\n  return previousValue;\n}\n\n/**\n * Callback for propEach\n *\n * @callback propEachCallback\n * @param {GeoJsonProperties} currentProperties The current Properties being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @returns {void}\n */\n\n/**\n * Iterate over properties in any GeoJSON object, similar to Array.forEach()\n *\n * @function\n * @param {FeatureCollection|Feature} geojson any GeoJSON object\n * @param {propEachCallback} callback a method that takes (currentProperties, featureIndex)\n * @returns {void}\n * @example\n * var features = turf.featureCollection([\n *     turf.point([26, 37], {foo: 'bar'}),\n *     turf.point([36, 53], {hello: 'world'})\n * ]);\n *\n * turf.propEach(features, function (currentProperties, featureIndex) {\n *   //=currentProperties\n *   //=featureIndex\n * });\n */\nfunction propEach(geojson, callback) {\n  var i;\n  switch (geojson.type) {\n    case \"FeatureCollection\":\n      for (i = 0; i < geojson.features.length; i++) {\n        if (callback(geojson.features[i].properties, i) === false) break;\n      }\n      break;\n    case \"Feature\":\n      callback(geojson.properties, 0);\n      break;\n  }\n}\n\n/**\n * Callback for propReduce\n *\n * The first time the callback function is called, the values provided as arguments depend\n * on whether the reduce method has an initialValue argument.\n *\n * If an initialValue is provided to the reduce method:\n *  - The previousValue argument is initialValue.\n *  - The currentValue argument is the value of the first element present in the array.\n *\n * If an initialValue is not provided:\n *  - The previousValue argument is the value of the first element present in the array.\n *  - The currentValue argument is the value of the second element present in the array.\n *\n * @callback propReduceCallback\n * @param {Reducer} previousValue The accumulated value previously returned in the last invocation\n * of the callback, or initialValue, if supplied.\n * @param {GeoJsonProperties} currentProperties The current Properties being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @returns {Reducer}\n */\n\n/**\n * Reduce properties in any GeoJSON object into a single value,\n * similar to how Array.reduce works. However, in this case we lazily run\n * the reduction, so an array of all properties is unnecessary.\n *\n * @function\n * @param {FeatureCollection|Feature|Geometry} geojson any GeoJSON object\n * @param {propReduceCallback} callback a method that takes (previousValue, currentProperties, featureIndex)\n * @param {Reducer} [initialValue] Value to use as the first argument to the first call of the callback.\n * @returns {Reducer} The value that results from the reduction.\n * @example\n * var features = turf.featureCollection([\n *     turf.point([26, 37], {foo: 'bar'}),\n *     turf.point([36, 53], {hello: 'world'})\n * ]);\n *\n * turf.propReduce(features, function (previousValue, currentProperties, featureIndex) {\n *   //=previousValue\n *   //=currentProperties\n *   //=featureIndex\n *   return currentProperties\n * });\n */\nfunction propReduce(geojson, callback, initialValue) {\n  var previousValue = initialValue;\n  propEach(geojson, function (currentProperties, featureIndex) {\n    if (featureIndex === 0 && initialValue === undefined)\n      previousValue = currentProperties;\n    else\n      previousValue = callback(previousValue, currentProperties, featureIndex);\n  });\n  return previousValue;\n}\n\n/**\n * Callback for featureEach\n *\n * @callback featureEachCallback\n * @param {Feature<any>} currentFeature The current Feature being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @returns {void}\n */\n\n/**\n * Iterate over features in any GeoJSON object, similar to\n * Array.forEach.\n *\n * @function\n * @param {FeatureCollection|Feature|Feature<GeometryCollection>} geojson any GeoJSON object\n * @param {featureEachCallback} callback a method that takes (currentFeature, featureIndex)\n * @returns {void}\n * @example\n * var features = turf.featureCollection([\n *   turf.point([26, 37], {foo: 'bar'}),\n *   turf.point([36, 53], {hello: 'world'})\n * ]);\n *\n * turf.featureEach(features, function (currentFeature, featureIndex) {\n *   //=currentFeature\n *   //=featureIndex\n * });\n */\nfunction featureEach(geojson, callback) {\n  if (geojson.type === \"Feature\") {\n    callback(geojson, 0);\n  } else if (geojson.type === \"FeatureCollection\") {\n    for (var i = 0; i < geojson.features.length; i++) {\n      if (callback(geojson.features[i], i) === false) break;\n    }\n  }\n}\n\n/**\n * Callback for featureReduce\n *\n * The first time the callback function is called, the values provided as arguments depend\n * on whether the reduce method has an initialValue argument.\n *\n * If an initialValue is provided to the reduce method:\n *  - The previousValue argument is initialValue.\n *  - The currentValue argument is the value of the first element present in the array.\n *\n * If an initialValue is not provided:\n *  - The previousValue argument is the value of the first element present in the array.\n *  - The currentValue argument is the value of the second element present in the array.\n *\n * @callback featureReduceCallback\n * @param {Reducer} previousValue The accumulated value previously returned in the last invocation\n * of the callback, or initialValue, if supplied.\n * @param {Feature} currentFeature The current Feature being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @returns {Reducer}\n */\n\n/**\n * Reduce features in any GeoJSON object, similar to Array.reduce().\n *\n * @function\n * @param {FeatureCollection|Feature|Feature<GeometryCollection>} geojson any GeoJSON object\n * @param {featureReduceCallback} callback a method that takes (previousValue, currentFeature, featureIndex)\n * @param {Reducer} [initialValue] Value to use as the first argument to the first call of the callback.\n * @returns {Reducer} The value that results from the reduction.\n * @example\n * var features = turf.featureCollection([\n *   turf.point([26, 37], {\"foo\": \"bar\"}),\n *   turf.point([36, 53], {\"hello\": \"world\"})\n * ]);\n *\n * turf.featureReduce(features, function (previousValue, currentFeature, featureIndex) {\n *   //=previousValue\n *   //=currentFeature\n *   //=featureIndex\n *   return currentFeature\n * });\n */\nfunction featureReduce(geojson, callback, initialValue) {\n  var previousValue = initialValue;\n  featureEach(geojson, function (currentFeature, featureIndex) {\n    if (featureIndex === 0 && initialValue === undefined)\n      previousValue = currentFeature;\n    else previousValue = callback(previousValue, currentFeature, featureIndex);\n  });\n  return previousValue;\n}\n\n/**\n * Get all coordinates from any GeoJSON object.\n *\n * @function\n * @param {AllGeoJSON} geojson any GeoJSON object\n * @returns {Array<Array<number>>} coordinate position array\n * @example\n * var features = turf.featureCollection([\n *   turf.point([26, 37], {foo: 'bar'}),\n *   turf.point([36, 53], {hello: 'world'})\n * ]);\n *\n * var coords = turf.coordAll(features);\n * //= [[26, 37], [36, 53]]\n */\nfunction coordAll(geojson) {\n  var coords = [];\n  coordEach(geojson, function (coord) {\n    coords.push(coord);\n  });\n  return coords;\n}\n\n/**\n * Callback for geomEach\n *\n * @callback geomEachCallback\n * @param {GeometryObject} currentGeometry The current Geometry being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @param {GeoJsonProperties} featureProperties The current Feature Properties being processed.\n * @param {BBox} featureBBox The current Feature BBox being processed.\n * @param {Id} featureId The current Feature Id being processed.\n * @returns {void}\n */\n\n/**\n * Iterate over each geometry in any GeoJSON object, similar to Array.forEach()\n *\n * @function\n * @param {FeatureCollection|Feature|Geometry|GeometryObject|Feature<GeometryCollection>} geojson any GeoJSON object\n * @param {geomEachCallback} callback a method that takes (currentGeometry, featureIndex, featureProperties, featureBBox, featureId)\n * @returns {void}\n * @example\n * var features = turf.featureCollection([\n *     turf.point([26, 37], {foo: 'bar'}),\n *     turf.point([36, 53], {hello: 'world'})\n * ]);\n *\n * turf.geomEach(features, function (currentGeometry, featureIndex, featureProperties, featureBBox, featureId) {\n *   //=currentGeometry\n *   //=featureIndex\n *   //=featureProperties\n *   //=featureBBox\n *   //=featureId\n * });\n */\nfunction geomEach(geojson, callback) {\n  var i,\n    j,\n    g,\n    geometry,\n    stopG,\n    geometryMaybeCollection,\n    isGeometryCollection,\n    featureProperties,\n    featureBBox,\n    featureId,\n    featureIndex = 0,\n    isFeatureCollection = geojson.type === \"FeatureCollection\",\n    isFeature = geojson.type === \"Feature\",\n    stop = isFeatureCollection ? geojson.features.length : 1;\n\n  // This logic may look a little weird. The reason why it is that way\n  // is because it's trying to be fast. GeoJSON supports multiple kinds\n  // of objects at its root: FeatureCollection, Features, Geometries.\n  // This function has the responsibility of handling all of them, and that\n  // means that some of the `for` loops you see below actually just don't apply\n  // to certain inputs. For instance, if you give this just a\n  // Point geometry, then both loops are short-circuited and all we do\n  // is gradually rename the input until it's called 'geometry'.\n  //\n  // This also aims to allocate as few resources as possible: just a\n  // few numbers and booleans, rather than any temporary arrays as would\n  // be required with the normalization approach.\n  for (i = 0; i < stop; i++) {\n    geometryMaybeCollection = isFeatureCollection\n      ? geojson.features[i].geometry\n      : isFeature\n        ? geojson.geometry\n        : geojson;\n    featureProperties = isFeatureCollection\n      ? geojson.features[i].properties\n      : isFeature\n        ? geojson.properties\n        : {};\n    featureBBox = isFeatureCollection\n      ? geojson.features[i].bbox\n      : isFeature\n        ? geojson.bbox\n        : undefined;\n    featureId = isFeatureCollection\n      ? geojson.features[i].id\n      : isFeature\n        ? geojson.id\n        : undefined;\n    isGeometryCollection = geometryMaybeCollection\n      ? geometryMaybeCollection.type === \"GeometryCollection\"\n      : false;\n    stopG = isGeometryCollection\n      ? geometryMaybeCollection.geometries.length\n      : 1;\n\n    for (g = 0; g < stopG; g++) {\n      geometry = isGeometryCollection\n        ? geometryMaybeCollection.geometries[g]\n        : geometryMaybeCollection;\n\n      // Handle null Geometry\n      if (geometry === null) {\n        if (\n          callback(\n            null,\n            featureIndex,\n            featureProperties,\n            featureBBox,\n            featureId\n          ) === false\n        )\n          return false;\n        continue;\n      }\n      switch (geometry.type) {\n        case \"Point\":\n        case \"LineString\":\n        case \"MultiPoint\":\n        case \"Polygon\":\n        case \"MultiLineString\":\n        case \"MultiPolygon\": {\n          if (\n            callback(\n              geometry,\n              featureIndex,\n              featureProperties,\n              featureBBox,\n              featureId\n            ) === false\n          )\n            return false;\n          break;\n        }\n        case \"GeometryCollection\": {\n          for (j = 0; j < geometry.geometries.length; j++) {\n            if (\n              callback(\n                geometry.geometries[j],\n                featureIndex,\n                featureProperties,\n                featureBBox,\n                featureId\n              ) === false\n            )\n              return false;\n          }\n          break;\n        }\n        default:\n          throw new Error(\"Unknown Geometry Type\");\n      }\n    }\n    // Only increase `featureIndex` per each feature\n    featureIndex++;\n  }\n}\n\n/**\n * Callback for geomReduce\n *\n * The first time the callback function is called, the values provided as arguments depend\n * on whether the reduce method has an initialValue argument.\n *\n * If an initialValue is provided to the reduce method:\n *  - The previousValue argument is initialValue.\n *  - The currentValue argument is the value of the first element present in the array.\n *\n * If an initialValue is not provided:\n *  - The previousValue argument is the value of the first element present in the array.\n *  - The currentValue argument is the value of the second element present in the array.\n *\n * @callback geomReduceCallback\n * @param {Reducer} previousValue The accumulated value previously returned in the last invocation\n * of the callback, or initialValue, if supplied.\n * @param {GeometryObject} currentGeometry The current Geometry being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @param {GeoJsonProperties} featureProperties The current Feature Properties being processed.\n * @param {BBox} featureBBox The current Feature BBox being processed.\n * @param {Id} featureId The current Feature Id being processed.\n * @returns {Reducer}\n */\n\n/**\n * Reduce geometry in any GeoJSON object, similar to Array.reduce().\n *\n * @function\n * @param {FeatureCollection|Feature|GeometryObject|GeometryCollection|Feature<GeometryCollection>} geojson any GeoJSON object\n * @param {geomReduceCallback} callback a method that takes (previousValue, currentGeometry, featureIndex, featureProperties, featureBBox, featureId)\n * @param {Reducer} [initialValue] Value to use as the first argument to the first call of the callback.\n * @returns {Reducer} The value that results from the reduction.\n * @example\n * var features = turf.featureCollection([\n *     turf.point([26, 37], {foo: 'bar'}),\n *     turf.point([36, 53], {hello: 'world'})\n * ]);\n *\n * turf.geomReduce(features, function (previousValue, currentGeometry, featureIndex, featureProperties, featureBBox, featureId) {\n *   //=previousValue\n *   //=currentGeometry\n *   //=featureIndex\n *   //=featureProperties\n *   //=featureBBox\n *   //=featureId\n *   return currentGeometry\n * });\n */\nfunction geomReduce(geojson, callback, initialValue) {\n  var previousValue = initialValue;\n  geomEach(\n    geojson,\n    function (\n      currentGeometry,\n      featureIndex,\n      featureProperties,\n      featureBBox,\n      featureId\n    ) {\n      if (featureIndex === 0 && initialValue === undefined)\n        previousValue = currentGeometry;\n      else\n        previousValue = callback(\n          previousValue,\n          currentGeometry,\n          featureIndex,\n          featureProperties,\n          featureBBox,\n          featureId\n        );\n    }\n  );\n  return previousValue;\n}\n\n/**\n * Callback for flattenEach\n *\n * @callback flattenEachCallback\n * @param {Feature} currentFeature The current flattened feature being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @param {number} multiFeatureIndex The current index of the Multi-Feature being processed.\n * @returns {void}\n */\n\n/**\n * Iterate over flattened features in any GeoJSON object, similar to\n * Array.forEach.\n *\n * @function\n * @param {FeatureCollection|Feature|GeometryObject|GeometryCollection|Feature<GeometryCollection>} geojson any GeoJSON object\n * @param {flattenEachCallback} callback a method that takes (currentFeature, featureIndex, multiFeatureIndex)\n * @returns {void}\n * @example\n * var features = turf.featureCollection([\n *     turf.point([26, 37], {foo: 'bar'}),\n *     turf.multiPoint([[40, 30], [36, 53]], {hello: 'world'})\n * ]);\n *\n * turf.flattenEach(features, function (currentFeature, featureIndex, multiFeatureIndex) {\n *   //=currentFeature\n *   //=featureIndex\n *   //=multiFeatureIndex\n * });\n */\nfunction flattenEach(geojson, callback) {\n  geomEach(geojson, function (geometry, featureIndex, properties, bbox, id) {\n    // Callback for single geometry\n    var type = geometry === null ? null : geometry.type;\n    switch (type) {\n      case null:\n      case \"Point\":\n      case \"LineString\":\n      case \"Polygon\":\n        if (\n          callback(\n            feature(geometry, properties, { bbox: bbox, id: id }),\n            featureIndex,\n            0\n          ) === false\n        )\n          return false;\n        return;\n    }\n\n    var geomType;\n\n    // Callback for multi-geometry\n    switch (type) {\n      case \"MultiPoint\":\n        geomType = \"Point\";\n        break;\n      case \"MultiLineString\":\n        geomType = \"LineString\";\n        break;\n      case \"MultiPolygon\":\n        geomType = \"Polygon\";\n        break;\n    }\n\n    for (\n      var multiFeatureIndex = 0;\n      multiFeatureIndex < geometry.coordinates.length;\n      multiFeatureIndex++\n    ) {\n      var coordinate = geometry.coordinates[multiFeatureIndex];\n      var geom = {\n        type: geomType,\n        coordinates: coordinate,\n      };\n      if (\n        callback(feature(geom, properties), featureIndex, multiFeatureIndex) ===\n        false\n      )\n        return false;\n    }\n  });\n}\n\n/**\n * Callback for flattenReduce\n *\n * The first time the callback function is called, the values provided as arguments depend\n * on whether the reduce method has an initialValue argument.\n *\n * If an initialValue is provided to the reduce method:\n *  - The previousValue argument is initialValue.\n *  - The currentValue argument is the value of the first element present in the array.\n *\n * If an initialValue is not provided:\n *  - The previousValue argument is the value of the first element present in the array.\n *  - The currentValue argument is the value of the second element present in the array.\n *\n * @callback flattenReduceCallback\n * @param {Reducer} previousValue The accumulated value previously returned in the last invocation\n * of the callback, or initialValue, if supplied.\n * @param {Feature} currentFeature The current Feature being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @param {number} multiFeatureIndex The current index of the Multi-Feature being processed.\n * @returns {Reducer}\n */\n\n/**\n * Reduce flattened features in any GeoJSON object, similar to Array.reduce().\n *\n * @function\n * @param {FeatureCollection|Feature|GeometryObject|GeometryCollection|Feature<GeometryCollection>} geojson any GeoJSON object\n * @param {flattenReduceCallback} callback a method that takes (previousValue, currentFeature, featureIndex, multiFeatureIndex)\n * @param {Reducer} [initialValue] Value to use as the first argument to the first call of the callback.\n * @returns {Reducer} The value that results from the reduction.\n * @example\n * var features = turf.featureCollection([\n *     turf.point([26, 37], {foo: 'bar'}),\n *     turf.multiPoint([[40, 30], [36, 53]], {hello: 'world'})\n * ]);\n *\n * turf.flattenReduce(features, function (previousValue, currentFeature, featureIndex, multiFeatureIndex) {\n *   //=previousValue\n *   //=currentFeature\n *   //=featureIndex\n *   //=multiFeatureIndex\n *   return currentFeature\n * });\n */\nfunction flattenReduce(geojson, callback, initialValue) {\n  var previousValue = initialValue;\n  flattenEach(\n    geojson,\n    function (currentFeature, featureIndex, multiFeatureIndex) {\n      if (\n        featureIndex === 0 &&\n        multiFeatureIndex === 0 &&\n        initialValue === undefined\n      )\n        previousValue = currentFeature;\n      else\n        previousValue = callback(\n          previousValue,\n          currentFeature,\n          featureIndex,\n          multiFeatureIndex\n        );\n    }\n  );\n  return previousValue;\n}\n\n/**\n * Callback for segmentEach\n *\n * @callback segmentEachCallback\n * @param {Feature<LineString>} currentSegment The current Segment being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @param {number} multiFeatureIndex The current index of the Multi-Feature being processed.\n * @param {number} geometryIndex The current index of the Geometry being processed.\n * @param {number} segmentIndex The current index of the Segment being processed.\n * @returns {void}\n */\n\n/**\n * Iterate over 2-vertex line segment in any GeoJSON object, similar to Array.forEach()\n * (Multi)Point geometries do not contain segments therefore they are ignored during this operation.\n *\n * @param {AllGeoJSON} geojson any GeoJSON\n * @param {segmentEachCallback} callback a method that takes (currentSegment, featureIndex, multiFeatureIndex, geometryIndex, segmentIndex)\n * @returns {void}\n * @example\n * var polygon = turf.polygon([[[-50, 5], [-40, -10], [-50, -10], [-40, 5], [-50, 5]]]);\n *\n * // Iterate over GeoJSON by 2-vertex segments\n * turf.segmentEach(polygon, function (currentSegment, featureIndex, multiFeatureIndex, geometryIndex, segmentIndex) {\n *   //=currentSegment\n *   //=featureIndex\n *   //=multiFeatureIndex\n *   //=geometryIndex\n *   //=segmentIndex\n * });\n *\n * // Calculate the total number of segments\n * var total = 0;\n * turf.segmentEach(polygon, function () {\n *     total++;\n * });\n */\nfunction segmentEach(geojson, callback) {\n  flattenEach(geojson, function (feature, featureIndex, multiFeatureIndex) {\n    var segmentIndex = 0;\n\n    // Exclude null Geometries\n    if (!feature.geometry) return;\n    // (Multi)Point geometries do not contain segments therefore they are ignored during this operation.\n    var type = feature.geometry.type;\n    if (type === \"Point\" || type === \"MultiPoint\") return;\n\n    // Generate 2-vertex line segments\n    var previousCoords;\n    var previousFeatureIndex = 0;\n    var previousMultiIndex = 0;\n    var prevGeomIndex = 0;\n    if (\n      coordEach(\n        feature,\n        function (\n          currentCoord,\n          coordIndex,\n          featureIndexCoord,\n          multiPartIndexCoord,\n          geometryIndex\n        ) {\n          // Simulating a meta.coordReduce() since `reduce` operations cannot be stopped by returning `false`\n          if (\n            previousCoords === undefined ||\n            featureIndex > previousFeatureIndex ||\n            multiPartIndexCoord > previousMultiIndex ||\n            geometryIndex > prevGeomIndex\n          ) {\n            previousCoords = currentCoord;\n            previousFeatureIndex = featureIndex;\n            previousMultiIndex = multiPartIndexCoord;\n            prevGeomIndex = geometryIndex;\n            segmentIndex = 0;\n            return;\n          }\n          var currentSegment = lineString(\n            [previousCoords, currentCoord],\n            feature.properties\n          );\n          if (\n            callback(\n              currentSegment,\n              featureIndex,\n              multiFeatureIndex,\n              geometryIndex,\n              segmentIndex\n            ) === false\n          )\n            return false;\n          segmentIndex++;\n          previousCoords = currentCoord;\n        }\n      ) === false\n    )\n      return false;\n  });\n}\n\n/**\n * Callback for segmentReduce\n *\n * The first time the callback function is called, the values provided as arguments depend\n * on whether the reduce method has an initialValue argument.\n *\n * If an initialValue is provided to the reduce method:\n *  - The previousValue argument is initialValue.\n *  - The currentValue argument is the value of the first element present in the array.\n *\n * If an initialValue is not provided:\n *  - The previousValue argument is the value of the first element present in the array.\n *  - The currentValue argument is the value of the second element present in the array.\n *\n * @callback segmentReduceCallback\n * @param {Reducer} previousValue The accumulated value previously returned in the last invocation\n * of the callback, or initialValue, if supplied.\n * @param {Feature<LineString>} currentSegment The current Segment being processed.\n * @param {number} featureIndex The current index of the Feature being processed.\n * @param {number} multiFeatureIndex The current index of the Multi-Feature being processed.\n * @param {number} geometryIndex The current index of the Geometry being processed.\n * @param {number} segmentIndex The current index of the Segment being processed.\n * @returns {Reducer}\n */\n\n/**\n * Reduce 2-vertex line segment in any GeoJSON object, similar to Array.reduce()\n * (Multi)Point geometries do not contain segments therefore they are ignored during this operation.\n *\n * @param {FeatureCollection|Feature|Geometry} geojson any GeoJSON\n * @param {segmentReduceCallback} callback a method that takes (previousValue, currentSegment, currentIndex)\n * @param {Reducer} [initialValue] Value to use as the first argument to the first call of the callback.\n * @returns {Reducer}\n * @example\n * var polygon = turf.polygon([[[-50, 5], [-40, -10], [-50, -10], [-40, 5], [-50, 5]]]);\n *\n * // Iterate over GeoJSON by 2-vertex segments\n * turf.segmentReduce(polygon, function (previousSegment, currentSegment, featureIndex, multiFeatureIndex, geometryIndex, segmentIndex) {\n *   //= previousSegment\n *   //= currentSegment\n *   //= featureIndex\n *   //= multiFeatureIndex\n *   //= geometryIndex\n *   //= segmentIndex\n *   return currentSegment\n * });\n *\n * // Calculate the total number of segments\n * var initialValue = 0\n * var total = turf.segmentReduce(polygon, function (previousValue) {\n *     previousValue++;\n *     return previousValue;\n * }, initialValue);\n */\nfunction segmentReduce(geojson, callback, initialValue) {\n  var previousValue = initialValue;\n  var started = false;\n  segmentEach(\n    geojson,\n    function (\n      currentSegment,\n      featureIndex,\n      multiFeatureIndex,\n      geometryIndex,\n      segmentIndex\n    ) {\n      if (started === false && initialValue === undefined)\n        previousValue = currentSegment;\n      else\n        previousValue = callback(\n          previousValue,\n          currentSegment,\n          featureIndex,\n          multiFeatureIndex,\n          geometryIndex,\n          segmentIndex\n        );\n      started = true;\n    }\n  );\n  return previousValue;\n}\n\n/**\n * Callback for lineEach\n *\n * @callback lineEachCallback\n * @param {Feature<LineString>} currentLine The current LineString|LinearRing being processed\n * @param {number} featureIndex The current index of the Feature being processed\n * @param {number} multiFeatureIndex The current index of the Multi-Feature being processed\n * @param {number} geometryIndex The current index of the Geometry being processed\n * @returns {void}\n */\n\n/**\n * Iterate over line or ring coordinates in LineString, Polygon, MultiLineString, MultiPolygon Features or Geometries,\n * similar to Array.forEach.\n *\n * @function\n * @param {FeatureCollection<Lines>|Feature<Lines>|Lines|Feature<GeometryCollection>|GeometryCollection} geojson object\n * @param {lineEachCallback} callback a method that takes (currentLine, featureIndex, multiFeatureIndex, geometryIndex)\n * @returns {void}\n * @example\n * var multiLine = turf.multiLineString([\n *   [[26, 37], [35, 45]],\n *   [[36, 53], [38, 50], [41, 55]]\n * ]);\n *\n * turf.lineEach(multiLine, function (currentLine, featureIndex, multiFeatureIndex, geometryIndex) {\n *   //=currentLine\n *   //=featureIndex\n *   //=multiFeatureIndex\n *   //=geometryIndex\n * });\n */\nfunction lineEach(geojson, callback) {\n  // validation\n  if (!geojson) throw new Error(\"geojson is required\");\n\n  flattenEach(geojson, function (feature, featureIndex, multiFeatureIndex) {\n    if (feature.geometry === null) return;\n    var type = feature.geometry.type;\n    var coords = feature.geometry.coordinates;\n    switch (type) {\n      case \"LineString\":\n        if (callback(feature, featureIndex, multiFeatureIndex, 0, 0) === false)\n          return false;\n        break;\n      case \"Polygon\":\n        for (\n          var geometryIndex = 0;\n          geometryIndex < coords.length;\n          geometryIndex++\n        ) {\n          if (\n            callback(\n              lineString(coords[geometryIndex], feature.properties),\n              featureIndex,\n              multiFeatureIndex,\n              geometryIndex\n            ) === false\n          )\n            return false;\n        }\n        break;\n    }\n  });\n}\n\n/**\n * Callback for lineReduce\n *\n * The first time the callback function is called, the values provided as arguments depend\n * on whether the reduce method has an initialValue argument.\n *\n * If an initialValue is provided to the reduce method:\n *  - The previousValue argument is initialValue.\n *  - The currentValue argument is the value of the first element present in the array.\n *\n * If an initialValue is not provided:\n *  - The previousValue argument is the value of the first element present in the array.\n *  - The currentValue argument is the value of the second element present in the array.\n *\n * @callback lineReduceCallback\n * @param {Reducer} previousValue The accumulated value previously returned in the last invocation\n * of the callback, or initialValue, if supplied.\n * @param {Feature<LineString>} currentLine The current LineString|LinearRing being processed.\n * @param {number} featureIndex The current index of the Feature being processed\n * @param {number} multiFeatureIndex The current index of the Multi-Feature being processed\n * @param {number} geometryIndex The current index of the Geometry being processed\n * @returns {Reducer}\n */\n\n/**\n * Reduce features in any GeoJSON object, similar to Array.reduce().\n *\n * @function\n * @param {FeatureCollection<Lines>|Feature<Lines>|Lines|Feature<GeometryCollection>|GeometryCollection} geojson object\n * @param {Function} callback a method that takes (previousValue, currentLine, featureIndex, multiFeatureIndex, geometryIndex)\n * @param {Reducer} [initialValue] Value to use as the first argument to the first call of the callback.\n * @returns {Reducer} The value that results from the reduction.\n * @example\n * var multiPoly = turf.multiPolygon([\n *   turf.polygon([[[12,48],[2,41],[24,38],[12,48]], [[9,44],[13,41],[13,45],[9,44]]]),\n *   turf.polygon([[[5, 5], [0, 0], [2, 2], [4, 4], [5, 5]]])\n * ]);\n *\n * turf.lineReduce(multiPoly, function (previousValue, currentLine, featureIndex, multiFeatureIndex, geometryIndex) {\n *   //=previousValue\n *   //=currentLine\n *   //=featureIndex\n *   //=multiFeatureIndex\n *   //=geometryIndex\n *   return currentLine\n * });\n */\nfunction lineReduce(geojson, callback, initialValue) {\n  var previousValue = initialValue;\n  lineEach(\n    geojson,\n    function (currentLine, featureIndex, multiFeatureIndex, geometryIndex) {\n      if (featureIndex === 0 && initialValue === undefined)\n        previousValue = currentLine;\n      else\n        previousValue = callback(\n          previousValue,\n          currentLine,\n          featureIndex,\n          multiFeatureIndex,\n          geometryIndex\n        );\n    }\n  );\n  return previousValue;\n}\n\n/**\n * Finds a particular 2-vertex LineString Segment from a GeoJSON using `@turf/meta` indexes.\n *\n * Negative indexes are permitted.\n * Point & MultiPoint will always return null.\n *\n * @param {FeatureCollection|Feature|Geometry} geojson Any GeoJSON Feature or Geometry\n * @param {Object} [options={}] Optional parameters\n * @param {number} [options.featureIndex=0] Feature Index\n * @param {number} [options.multiFeatureIndex=0] Multi-Feature Index\n * @param {number} [options.geometryIndex=0] Geometry Index\n * @param {number} [options.segmentIndex=0] Segment Index\n * @param {Object} [options.properties={}] Translate Properties to output LineString\n * @param {BBox} [options.bbox={}] Translate BBox to output LineString\n * @param {number|string} [options.id={}] Translate Id to output LineString\n * @returns {Feature<LineString>} 2-vertex GeoJSON Feature LineString\n * @example\n * var multiLine = turf.multiLineString([\n *     [[10, 10], [50, 30], [30, 40]],\n *     [[-10, -10], [-50, -30], [-30, -40]]\n * ]);\n *\n * // First Segment (defaults are 0)\n * turf.findSegment(multiLine);\n * // => Feature<LineString<[[10, 10], [50, 30]]>>\n *\n * // First Segment of 2nd Multi Feature\n * turf.findSegment(multiLine, {multiFeatureIndex: 1});\n * // => Feature<LineString<[[-10, -10], [-50, -30]]>>\n *\n * // Last Segment of Last Multi Feature\n * turf.findSegment(multiLine, {multiFeatureIndex: -1, segmentIndex: -1});\n * // => Feature<LineString<[[-50, -30], [-30, -40]]>>\n */\nfunction findSegment(geojson, options) {\n  // Optional Parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n  var featureIndex = options.featureIndex || 0;\n  var multiFeatureIndex = options.multiFeatureIndex || 0;\n  var geometryIndex = options.geometryIndex || 0;\n  var segmentIndex = options.segmentIndex || 0;\n\n  // Find FeatureIndex\n  var properties = options.properties;\n  var geometry;\n\n  switch (geojson.type) {\n    case \"FeatureCollection\":\n      if (featureIndex < 0)\n        featureIndex = geojson.features.length + featureIndex;\n      properties = properties || geojson.features[featureIndex].properties;\n      geometry = geojson.features[featureIndex].geometry;\n      break;\n    case \"Feature\":\n      properties = properties || geojson.properties;\n      geometry = geojson.geometry;\n      break;\n    case \"Point\":\n    case \"MultiPoint\":\n      return null;\n    case \"LineString\":\n    case \"Polygon\":\n    case \"MultiLineString\":\n    case \"MultiPolygon\":\n      geometry = geojson;\n      break;\n    default:\n      throw new Error(\"geojson is invalid\");\n  }\n\n  // Find SegmentIndex\n  if (geometry === null) return null;\n  var coords = geometry.coordinates;\n  switch (geometry.type) {\n    case \"Point\":\n    case \"MultiPoint\":\n      return null;\n    case \"LineString\":\n      if (segmentIndex < 0) segmentIndex = coords.length + segmentIndex - 1;\n      return lineString(\n        [coords[segmentIndex], coords[segmentIndex + 1]],\n        properties,\n        options\n      );\n    case \"Polygon\":\n      if (geometryIndex < 0) geometryIndex = coords.length + geometryIndex;\n      if (segmentIndex < 0)\n        segmentIndex = coords[geometryIndex].length + segmentIndex - 1;\n      return lineString(\n        [\n          coords[geometryIndex][segmentIndex],\n          coords[geometryIndex][segmentIndex + 1],\n        ],\n        properties,\n        options\n      );\n    case \"MultiLineString\":\n      if (multiFeatureIndex < 0)\n        multiFeatureIndex = coords.length + multiFeatureIndex;\n      if (segmentIndex < 0)\n        segmentIndex = coords[multiFeatureIndex].length + segmentIndex - 1;\n      return lineString(\n        [\n          coords[multiFeatureIndex][segmentIndex],\n          coords[multiFeatureIndex][segmentIndex + 1],\n        ],\n        properties,\n        options\n      );\n    case \"MultiPolygon\":\n      if (multiFeatureIndex < 0)\n        multiFeatureIndex = coords.length + multiFeatureIndex;\n      if (geometryIndex < 0)\n        geometryIndex = coords[multiFeatureIndex].length + geometryIndex;\n      if (segmentIndex < 0)\n        segmentIndex =\n          coords[multiFeatureIndex][geometryIndex].length - segmentIndex - 1;\n      return lineString(\n        [\n          coords[multiFeatureIndex][geometryIndex][segmentIndex],\n          coords[multiFeatureIndex][geometryIndex][segmentIndex + 1],\n        ],\n        properties,\n        options\n      );\n  }\n  throw new Error(\"geojson is invalid\");\n}\n\n/**\n * Finds a particular Point from a GeoJSON using `@turf/meta` indexes.\n *\n * Negative indexes are permitted.\n *\n * @param {FeatureCollection|Feature|Geometry} geojson Any GeoJSON Feature or Geometry\n * @param {Object} [options={}] Optional parameters\n * @param {number} [options.featureIndex=0] Feature Index\n * @param {number} [options.multiFeatureIndex=0] Multi-Feature Index\n * @param {number} [options.geometryIndex=0] Geometry Index\n * @param {number} [options.coordIndex=0] Coord Index\n * @param {Object} [options.properties={}] Translate Properties to output Point\n * @param {BBox} [options.bbox={}] Translate BBox to output Point\n * @param {number|string} [options.id={}] Translate Id to output Point\n * @returns {Feature<Point>} 2-vertex GeoJSON Feature Point\n * @example\n * var multiLine = turf.multiLineString([\n *     [[10, 10], [50, 30], [30, 40]],\n *     [[-10, -10], [-50, -30], [-30, -40]]\n * ]);\n *\n * // First Segment (defaults are 0)\n * turf.findPoint(multiLine);\n * // => Feature<Point<[10, 10]>>\n *\n * // First Segment of the 2nd Multi-Feature\n * turf.findPoint(multiLine, {multiFeatureIndex: 1});\n * // => Feature<Point<[-10, -10]>>\n *\n * // Last Segment of last Multi-Feature\n * turf.findPoint(multiLine, {multiFeatureIndex: -1, coordIndex: -1});\n * // => Feature<Point<[-30, -40]>>\n */\nfunction findPoint(geojson, options) {\n  // Optional Parameters\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n  var featureIndex = options.featureIndex || 0;\n  var multiFeatureIndex = options.multiFeatureIndex || 0;\n  var geometryIndex = options.geometryIndex || 0;\n  var coordIndex = options.coordIndex || 0;\n\n  // Find FeatureIndex\n  var properties = options.properties;\n  var geometry;\n\n  switch (geojson.type) {\n    case \"FeatureCollection\":\n      if (featureIndex < 0)\n        featureIndex = geojson.features.length + featureIndex;\n      properties = properties || geojson.features[featureIndex].properties;\n      geometry = geojson.features[featureIndex].geometry;\n      break;\n    case \"Feature\":\n      properties = properties || geojson.properties;\n      geometry = geojson.geometry;\n      break;\n    case \"Point\":\n    case \"MultiPoint\":\n      return null;\n    case \"LineString\":\n    case \"Polygon\":\n    case \"MultiLineString\":\n    case \"MultiPolygon\":\n      geometry = geojson;\n      break;\n    default:\n      throw new Error(\"geojson is invalid\");\n  }\n\n  // Find Coord Index\n  if (geometry === null) return null;\n  var coords = geometry.coordinates;\n  switch (geometry.type) {\n    case \"Point\":\n      return point(coords, properties, options);\n    case \"MultiPoint\":\n      if (multiFeatureIndex < 0)\n        multiFeatureIndex = coords.length + multiFeatureIndex;\n      return point(coords[multiFeatureIndex], properties, options);\n    case \"LineString\":\n      if (coordIndex < 0) coordIndex = coords.length + coordIndex;\n      return point(coords[coordIndex], properties, options);\n    case \"Polygon\":\n      if (geometryIndex < 0) geometryIndex = coords.length + geometryIndex;\n      if (coordIndex < 0)\n        coordIndex = coords[geometryIndex].length + coordIndex;\n      return point(coords[geometryIndex][coordIndex], properties, options);\n    case \"MultiLineString\":\n      if (multiFeatureIndex < 0)\n        multiFeatureIndex = coords.length + multiFeatureIndex;\n      if (coordIndex < 0)\n        coordIndex = coords[multiFeatureIndex].length + coordIndex;\n      return point(coords[multiFeatureIndex][coordIndex], properties, options);\n    case \"MultiPolygon\":\n      if (multiFeatureIndex < 0)\n        multiFeatureIndex = coords.length + multiFeatureIndex;\n      if (geometryIndex < 0)\n        geometryIndex = coords[multiFeatureIndex].length + geometryIndex;\n      if (coordIndex < 0)\n        coordIndex =\n          coords[multiFeatureIndex][geometryIndex].length - coordIndex;\n      return point(\n        coords[multiFeatureIndex][geometryIndex][coordIndex],\n        properties,\n        options\n      );\n  }\n  throw new Error(\"geojson is invalid\");\n}\n\nexport {\n  coordReduce,\n  coordEach,\n  propEach,\n  propReduce,\n  featureReduce,\n  featureEach,\n  coordAll,\n  geomReduce,\n  geomEach,\n  flattenReduce,\n  flattenEach,\n  segmentReduce,\n  segmentEach,\n  lineReduce,\n  lineEach,\n  findSegment,\n  findPoint,\n};\n"]}