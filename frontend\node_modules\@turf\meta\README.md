# @turf/meta

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## coordEachCallback

Callback for coordEach

Type: [Function][1]

### Parameters

*   `currentCoord` **[Array][2]<[number][3]>** The current coordinate being processed.
*   `coordIndex` **[number][3]** The current index of the coordinate being processed.
*   `featureIndex` **[number][3]** The current index of the Feature being processed.
*   `multiFeatureIndex` **[number][3]** The current index of the Multi-Feature being processed.
*   `geometryIndex` **[number][3]** The current index of the Geometry being processed.

Returns **void**&#x20;

## coordEach

Iterate over coordinates in any GeoJSON object, similar to Array.forEach()

### Parameters

*   `geojson` **AllGeoJSON** any GeoJSON object
*   `callback` **[coordEachCallback][4]** a method that takes (currentCoord, coordIndex, featureIndex, multiFeatureIndex)
*   `excludeWrapCoord` **[boolean][5]** whether or not to include the final coordinate of LinearRings that wraps the ring in its iteration. (optional, default `false`)

### Examples

```javascript
var features = turf.featureCollection([
  turf.point([26, 37], {"foo": "bar"}),
  turf.point([36, 53], {"hello": "world"})
]);

turf.coordEach(features, function (currentCoord, coordIndex, featureIndex, multiFeatureIndex, geometryIndex) {
  //=currentCoord
  //=coordIndex
  //=featureIndex
  //=multiFeatureIndex
  //=geometryIndex
});
```

Returns **void**&#x20;

## coordReduceCallback

Callback for coordReduce

The first time the callback function is called, the values provided as arguments depend
on whether the reduce method has an initialValue argument.

If an initialValue is provided to the reduce method:

*   The previousValue argument is initialValue.
*   The currentValue argument is the value of the first element present in the array.

If an initialValue is not provided:

*   The previousValue argument is the value of the first element present in the array.
*   The currentValue argument is the value of the second element present in the array.

Type: [Function][1]

### Parameters

*   `previousValue` **Reducer** The accumulated value previously returned in the last invocation
    of the callback, or initialValue, if supplied.
*   `currentCoord` **[Array][2]<[number][3]>** The current coordinate being processed.
*   `coordIndex` **[number][3]** The current index of the coordinate being processed.
    Starts at index 0, if an initialValue is provided, and at index 1 otherwise.
*   `featureIndex` **[number][3]** The current index of the Feature being processed.
*   `multiFeatureIndex` **[number][3]** The current index of the Multi-Feature being processed.
*   `geometryIndex` **[number][3]** The current index of the Geometry being processed.

Returns **Reducer**&#x20;

## coordReduce

Reduce coordinates in any GeoJSON object, similar to Array.reduce()

### Parameters

*   `geojson` **AllGeoJSON** any GeoJSON object
*   `callback` **[coordReduceCallback][6]** a method that takes (previousValue, currentCoord, coordIndex)
*   `initialValue` **Reducer?** Value to use as the first argument to the first call of the callback.
*   `excludeWrapCoord` **[boolean][5]** whether or not to include the final coordinate of LinearRings that wraps the ring in its iteration. (optional, default `false`)

### Examples

```javascript
var features = turf.featureCollection([
  turf.point([26, 37], {"foo": "bar"}),
  turf.point([36, 53], {"hello": "world"})
]);

turf.coordReduce(features, function (previousValue, currentCoord, coordIndex, featureIndex, multiFeatureIndex, geometryIndex) {
  //=previousValue
  //=currentCoord
  //=coordIndex
  //=featureIndex
  //=multiFeatureIndex
  //=geometryIndex
  return currentCoord;
});
```

Returns **Reducer** The value that results from the reduction.

## propEachCallback

Callback for propEach

Type: [Function][1]

### Parameters

*   `currentProperties` **[GeoJsonProperties][7]** The current Properties being processed.
*   `featureIndex` **[number][3]** The current index of the Feature being processed.

Returns **void**&#x20;

## propEach

Iterate over properties in any GeoJSON object, similar to Array.forEach()

### Parameters

*   `geojson` **([FeatureCollection][8] | [Feature][7])** any GeoJSON object
*   `callback` **[propEachCallback][9]** a method that takes (currentProperties, featureIndex)

### Examples

```javascript
var features = turf.featureCollection([
    turf.point([26, 37], {foo: 'bar'}),
    turf.point([36, 53], {hello: 'world'})
]);

turf.propEach(features, function (currentProperties, featureIndex) {
  //=currentProperties
  //=featureIndex
});
```

Returns **void**&#x20;

## propReduceCallback

Callback for propReduce

The first time the callback function is called, the values provided as arguments depend
on whether the reduce method has an initialValue argument.

If an initialValue is provided to the reduce method:

*   The previousValue argument is initialValue.
*   The currentValue argument is the value of the first element present in the array.

If an initialValue is not provided:

*   The previousValue argument is the value of the first element present in the array.
*   The currentValue argument is the value of the second element present in the array.

Type: [Function][1]

### Parameters

*   `previousValue` **Reducer** The accumulated value previously returned in the last invocation
    of the callback, or initialValue, if supplied.
*   `currentProperties` **[GeoJsonProperties][7]** The current Properties being processed.
*   `featureIndex` **[number][3]** The current index of the Feature being processed.

Returns **Reducer**&#x20;

## propReduce

Reduce properties in any GeoJSON object into a single value,
similar to how Array.reduce works. However, in this case we lazily run
the reduction, so an array of all properties is unnecessary.

### Parameters

*   `geojson` **([FeatureCollection][8] | [Feature][7] | [Geometry][10])** any GeoJSON object
*   `callback` **[propReduceCallback][11]** a method that takes (previousValue, currentProperties, featureIndex)
*   `initialValue` **Reducer?** Value to use as the first argument to the first call of the callback.

### Examples

```javascript
var features = turf.featureCollection([
    turf.point([26, 37], {foo: 'bar'}),
    turf.point([36, 53], {hello: 'world'})
]);

turf.propReduce(features, function (previousValue, currentProperties, featureIndex) {
  //=previousValue
  //=currentProperties
  //=featureIndex
  return currentProperties
});
```

Returns **Reducer** The value that results from the reduction.

## featureEachCallback

Callback for featureEach

Type: [Function][1]

### Parameters

*   `currentFeature` **[Feature][7]\<any>** The current Feature being processed.
*   `featureIndex` **[number][3]** The current index of the Feature being processed.

Returns **void**&#x20;

## featureEach

Iterate over features in any GeoJSON object, similar to
Array.forEach.

### Parameters

*   `geojson` **([FeatureCollection][8] | [Feature][7] | [Feature][7]<[GeometryCollection][12]>)** any GeoJSON object
*   `callback` **[featureEachCallback][13]** a method that takes (currentFeature, featureIndex)

### Examples

```javascript
var features = turf.featureCollection([
  turf.point([26, 37], {foo: 'bar'}),
  turf.point([36, 53], {hello: 'world'})
]);

turf.featureEach(features, function (currentFeature, featureIndex) {
  //=currentFeature
  //=featureIndex
});
```

Returns **void**&#x20;

## featureReduceCallback

Callback for featureReduce

The first time the callback function is called, the values provided as arguments depend
on whether the reduce method has an initialValue argument.

If an initialValue is provided to the reduce method:

*   The previousValue argument is initialValue.
*   The currentValue argument is the value of the first element present in the array.

If an initialValue is not provided:

*   The previousValue argument is the value of the first element present in the array.
*   The currentValue argument is the value of the second element present in the array.

Type: [Function][1]

### Parameters

*   `previousValue` **Reducer** The accumulated value previously returned in the last invocation
    of the callback, or initialValue, if supplied.
*   `currentFeature` **[Feature][7]** The current Feature being processed.
*   `featureIndex` **[number][3]** The current index of the Feature being processed.

Returns **Reducer**&#x20;

## featureReduce

Reduce features in any GeoJSON object, similar to Array.reduce().

### Parameters

*   `geojson` **([FeatureCollection][8] | [Feature][7] | [Feature][7]<[GeometryCollection][12]>)** any GeoJSON object
*   `callback` **[featureReduceCallback][14]** a method that takes (previousValue, currentFeature, featureIndex)
*   `initialValue` **Reducer?** Value to use as the first argument to the first call of the callback.

### Examples

```javascript
var features = turf.featureCollection([
  turf.point([26, 37], {"foo": "bar"}),
  turf.point([36, 53], {"hello": "world"})
]);

turf.featureReduce(features, function (previousValue, currentFeature, featureIndex) {
  //=previousValue
  //=currentFeature
  //=featureIndex
  return currentFeature
});
```

Returns **Reducer** The value that results from the reduction.

## coordAll

Get all coordinates from any GeoJSON object.

### Parameters

*   `geojson` **AllGeoJSON** any GeoJSON object

### Examples

```javascript
var features = turf.featureCollection([
  turf.point([26, 37], {foo: 'bar'}),
  turf.point([36, 53], {hello: 'world'})
]);

var coords = turf.coordAll(features);
//= [[26, 37], [36, 53]]
```

Returns **[Array][2]<[Array][2]<[number][3]>>** coordinate position array

## geomEachCallback

Callback for geomEach

Type: [Function][1]

### Parameters

*   `currentGeometry` **[GeometryObject][10]** The current Geometry being processed.
*   `featureIndex` **[number][3]** The current index of the Feature being processed.
*   `featureProperties` **[GeoJsonProperties][7]** The current Feature Properties being processed.
*   `featureBBox` **[BBox][15]** The current Feature BBox being processed.
*   `featureId` **Id** The current Feature Id being processed.

Returns **void**&#x20;

## geomEach

Iterate over each geometry in any GeoJSON object, similar to Array.forEach()

### Parameters

*   `geojson` **([FeatureCollection][8] | [Feature][7] | [Geometry][10] | [GeometryObject][10] | [Feature][7]<[GeometryCollection][12]>)** any GeoJSON object
*   `callback` **[geomEachCallback][16]** a method that takes (currentGeometry, featureIndex, featureProperties, featureBBox, featureId)

### Examples

```javascript
var features = turf.featureCollection([
    turf.point([26, 37], {foo: 'bar'}),
    turf.point([36, 53], {hello: 'world'})
]);

turf.geomEach(features, function (currentGeometry, featureIndex, featureProperties, featureBBox, featureId) {
  //=currentGeometry
  //=featureIndex
  //=featureProperties
  //=featureBBox
  //=featureId
});
```

Returns **void**&#x20;

## geomReduceCallback

Callback for geomReduce

The first time the callback function is called, the values provided as arguments depend
on whether the reduce method has an initialValue argument.

If an initialValue is provided to the reduce method:

*   The previousValue argument is initialValue.
*   The currentValue argument is the value of the first element present in the array.

If an initialValue is not provided:

*   The previousValue argument is the value of the first element present in the array.
*   The currentValue argument is the value of the second element present in the array.

Type: [Function][1]

### Parameters

*   `previousValue` **Reducer** The accumulated value previously returned in the last invocation
    of the callback, or initialValue, if supplied.
*   `currentGeometry` **[GeometryObject][10]** The current Geometry being processed.
*   `featureIndex` **[number][3]** The current index of the Feature being processed.
*   `featureProperties` **[GeoJsonProperties][7]** The current Feature Properties being processed.
*   `featureBBox` **[BBox][15]** The current Feature BBox being processed.
*   `featureId` **Id** The current Feature Id being processed.

Returns **Reducer**&#x20;

## geomReduce

Reduce geometry in any GeoJSON object, similar to Array.reduce().

### Parameters

*   `geojson` **([FeatureCollection][8] | [Feature][7] | [GeometryObject][10] | [GeometryCollection][12] | [Feature][7]<[GeometryCollection][12]>)** any GeoJSON object
*   `callback` **[geomReduceCallback][17]** a method that takes (previousValue, currentGeometry, featureIndex, featureProperties, featureBBox, featureId)
*   `initialValue` **Reducer?** Value to use as the first argument to the first call of the callback.

### Examples

```javascript
var features = turf.featureCollection([
    turf.point([26, 37], {foo: 'bar'}),
    turf.point([36, 53], {hello: 'world'})
]);

turf.geomReduce(features, function (previousValue, currentGeometry, featureIndex, featureProperties, featureBBox, featureId) {
  //=previousValue
  //=currentGeometry
  //=featureIndex
  //=featureProperties
  //=featureBBox
  //=featureId
  return currentGeometry
});
```

Returns **Reducer** The value that results from the reduction.

## flattenEachCallback

Callback for flattenEach

Type: [Function][1]

### Parameters

*   `currentFeature` **[Feature][7]** The current flattened feature being processed.
*   `featureIndex` **[number][3]** The current index of the Feature being processed.
*   `multiFeatureIndex` **[number][3]** The current index of the Multi-Feature being processed.

Returns **void**&#x20;

## flattenEach

Iterate over flattened features in any GeoJSON object, similar to
Array.forEach.

### Parameters

*   `geojson` **([FeatureCollection][8] | [Feature][7] | [GeometryObject][10] | [GeometryCollection][12] | [Feature][7]<[GeometryCollection][12]>)** any GeoJSON object
*   `callback` **[flattenEachCallback][18]** a method that takes (currentFeature, featureIndex, multiFeatureIndex)

### Examples

```javascript
var features = turf.featureCollection([
    turf.point([26, 37], {foo: 'bar'}),
    turf.multiPoint([[40, 30], [36, 53]], {hello: 'world'})
]);

turf.flattenEach(features, function (currentFeature, featureIndex, multiFeatureIndex) {
  //=currentFeature
  //=featureIndex
  //=multiFeatureIndex
});
```

Returns **void**&#x20;

## flattenReduceCallback

Callback for flattenReduce

The first time the callback function is called, the values provided as arguments depend
on whether the reduce method has an initialValue argument.

If an initialValue is provided to the reduce method:

*   The previousValue argument is initialValue.
*   The currentValue argument is the value of the first element present in the array.

If an initialValue is not provided:

*   The previousValue argument is the value of the first element present in the array.
*   The currentValue argument is the value of the second element present in the array.

Type: [Function][1]

### Parameters

*   `previousValue` **Reducer** The accumulated value previously returned in the last invocation
    of the callback, or initialValue, if supplied.
*   `currentFeature` **[Feature][7]** The current Feature being processed.
*   `featureIndex` **[number][3]** The current index of the Feature being processed.
*   `multiFeatureIndex` **[number][3]** The current index of the Multi-Feature being processed.

Returns **Reducer**&#x20;

## flattenReduce

Reduce flattened features in any GeoJSON object, similar to Array.reduce().

### Parameters

*   `geojson` **([FeatureCollection][8] | [Feature][7] | [GeometryObject][10] | [GeometryCollection][12] | [Feature][7]<[GeometryCollection][12]>)** any GeoJSON object
*   `callback` **[flattenReduceCallback][19]** a method that takes (previousValue, currentFeature, featureIndex, multiFeatureIndex)
*   `initialValue` **Reducer?** Value to use as the first argument to the first call of the callback.

### Examples

```javascript
var features = turf.featureCollection([
    turf.point([26, 37], {foo: 'bar'}),
    turf.multiPoint([[40, 30], [36, 53]], {hello: 'world'})
]);

turf.flattenReduce(features, function (previousValue, currentFeature, featureIndex, multiFeatureIndex) {
  //=previousValue
  //=currentFeature
  //=featureIndex
  //=multiFeatureIndex
  return currentFeature
});
```

Returns **Reducer** The value that results from the reduction.

## segmentEachCallback

Callback for segmentEach

Type: [Function][1]

### Parameters

*   `currentSegment` **[Feature][7]<[LineString][20]>** The current Segment being processed.
*   `featureIndex` **[number][3]** The current index of the Feature being processed.
*   `multiFeatureIndex` **[number][3]** The current index of the Multi-Feature being processed.
*   `geometryIndex` **[number][3]** The current index of the Geometry being processed.
*   `segmentIndex` **[number][3]** The current index of the Segment being processed.

Returns **void**&#x20;

## segmentEach

Iterate over 2-vertex line segment in any GeoJSON object, similar to Array.forEach()
(Multi)Point geometries do not contain segments therefore they are ignored during this operation.

### Parameters

*   `geojson` **AllGeoJSON** any GeoJSON
*   `callback` **[segmentEachCallback][21]** a method that takes (currentSegment, featureIndex, multiFeatureIndex, geometryIndex, segmentIndex)

### Examples

```javascript
var polygon = turf.polygon([[[-50, 5], [-40, -10], [-50, -10], [-40, 5], [-50, 5]]]);

// Iterate over GeoJSON by 2-vertex segments
turf.segmentEach(polygon, function (currentSegment, featureIndex, multiFeatureIndex, geometryIndex, segmentIndex) {
  //=currentSegment
  //=featureIndex
  //=multiFeatureIndex
  //=geometryIndex
  //=segmentIndex
});

// Calculate the total number of segments
var total = 0;
turf.segmentEach(polygon, function () {
    total++;
});
```

Returns **void**&#x20;

## segmentReduceCallback

Callback for segmentReduce

The first time the callback function is called, the values provided as arguments depend
on whether the reduce method has an initialValue argument.

If an initialValue is provided to the reduce method:

*   The previousValue argument is initialValue.
*   The currentValue argument is the value of the first element present in the array.

If an initialValue is not provided:

*   The previousValue argument is the value of the first element present in the array.
*   The currentValue argument is the value of the second element present in the array.

Type: [Function][1]

### Parameters

*   `previousValue` **Reducer** The accumulated value previously returned in the last invocation
    of the callback, or initialValue, if supplied.
*   `currentSegment` **[Feature][7]<[LineString][20]>** The current Segment being processed.
*   `featureIndex` **[number][3]** The current index of the Feature being processed.
*   `multiFeatureIndex` **[number][3]** The current index of the Multi-Feature being processed.
*   `geometryIndex` **[number][3]** The current index of the Geometry being processed.
*   `segmentIndex` **[number][3]** The current index of the Segment being processed.

Returns **Reducer**&#x20;

## segmentReduce

Reduce 2-vertex line segment in any GeoJSON object, similar to Array.reduce()
(Multi)Point geometries do not contain segments therefore they are ignored during this operation.

### Parameters

*   `geojson` **([FeatureCollection][8] | [Feature][7] | [Geometry][10])** any GeoJSON
*   `callback` **[segmentReduceCallback][22]** a method that takes (previousValue, currentSegment, currentIndex)
*   `initialValue` **Reducer?** Value to use as the first argument to the first call of the callback.

### Examples

```javascript
var polygon = turf.polygon([[[-50, 5], [-40, -10], [-50, -10], [-40, 5], [-50, 5]]]);

// Iterate over GeoJSON by 2-vertex segments
turf.segmentReduce(polygon, function (previousSegment, currentSegment, featureIndex, multiFeatureIndex, geometryIndex, segmentIndex) {
  //= previousSegment
  //= currentSegment
  //= featureIndex
  //= multiFeatureIndex
  //= geometryIndex
  //= segmentIndex
  return currentSegment
});

// Calculate the total number of segments
var initialValue = 0
var total = turf.segmentReduce(polygon, function (previousValue) {
    previousValue++;
    return previousValue;
}, initialValue);
```

Returns **Reducer**&#x20;

## lineEachCallback

Callback for lineEach

Type: [Function][1]

### Parameters

*   `currentLine` **[Feature][7]<[LineString][20]>** The current LineString|LinearRing being processed
*   `featureIndex` **[number][3]** The current index of the Feature being processed
*   `multiFeatureIndex` **[number][3]** The current index of the Multi-Feature being processed
*   `geometryIndex` **[number][3]** The current index of the Geometry being processed

Returns **void**&#x20;

## lineEach

Iterate over line or ring coordinates in LineString, Polygon, MultiLineString, MultiPolygon Features or Geometries,
similar to Array.forEach.

### Parameters

*   `geojson` **([FeatureCollection][8]\<Lines> | [Feature][7]\<Lines> | Lines | [Feature][7]<[GeometryCollection][12]> | [GeometryCollection][12])** object
*   `callback` **[lineEachCallback][23]** a method that takes (currentLine, featureIndex, multiFeatureIndex, geometryIndex)

### Examples

```javascript
var multiLine = turf.multiLineString([
  [[26, 37], [35, 45]],
  [[36, 53], [38, 50], [41, 55]]
]);

turf.lineEach(multiLine, function (currentLine, featureIndex, multiFeatureIndex, geometryIndex) {
  //=currentLine
  //=featureIndex
  //=multiFeatureIndex
  //=geometryIndex
});
```

Returns **void**&#x20;

## lineReduceCallback

Callback for lineReduce

The first time the callback function is called, the values provided as arguments depend
on whether the reduce method has an initialValue argument.

If an initialValue is provided to the reduce method:

*   The previousValue argument is initialValue.
*   The currentValue argument is the value of the first element present in the array.

If an initialValue is not provided:

*   The previousValue argument is the value of the first element present in the array.
*   The currentValue argument is the value of the second element present in the array.

Type: [Function][1]

### Parameters

*   `previousValue` **Reducer** The accumulated value previously returned in the last invocation
    of the callback, or initialValue, if supplied.
*   `currentLine` **[Feature][7]<[LineString][20]>** The current LineString|LinearRing being processed.
*   `featureIndex` **[number][3]** The current index of the Feature being processed
*   `multiFeatureIndex` **[number][3]** The current index of the Multi-Feature being processed
*   `geometryIndex` **[number][3]** The current index of the Geometry being processed

Returns **Reducer**&#x20;

## lineReduce

Reduce features in any GeoJSON object, similar to Array.reduce().

### Parameters

*   `geojson` **([FeatureCollection][8]\<Lines> | [Feature][7]\<Lines> | Lines | [Feature][7]<[GeometryCollection][12]> | [GeometryCollection][12])** object
*   `callback` **[Function][1]** a method that takes (previousValue, currentLine, featureIndex, multiFeatureIndex, geometryIndex)
*   `initialValue` **Reducer?** Value to use as the first argument to the first call of the callback.

### Examples

```javascript
var multiPoly = turf.multiPolygon([
  turf.polygon([[[12,48],[2,41],[24,38],[12,48]], [[9,44],[13,41],[13,45],[9,44]]]),
  turf.polygon([[[5, 5], [0, 0], [2, 2], [4, 4], [5, 5]]])
]);

turf.lineReduce(multiPoly, function (previousValue, currentLine, featureIndex, multiFeatureIndex, geometryIndex) {
  //=previousValue
  //=currentLine
  //=featureIndex
  //=multiFeatureIndex
  //=geometryIndex
  return currentLine
});
```

Returns **Reducer** The value that results from the reduction.

## findSegment

Finds a particular 2-vertex LineString Segment from a GeoJSON using `@turf/meta` indexes.

Negative indexes are permitted.
Point & MultiPoint will always return null.

### Parameters

*   `geojson` **([FeatureCollection][8] | [Feature][7] | [Geometry][10])** Any GeoJSON Feature or Geometry
*   `options` **[Object][24]** Optional parameters (optional, default `{}`)

    *   `options.featureIndex` **[number][3]** Feature Index (optional, default `0`)
    *   `options.multiFeatureIndex` **[number][3]** Multi-Feature Index (optional, default `0`)
    *   `options.geometryIndex` **[number][3]** Geometry Index (optional, default `0`)
    *   `options.segmentIndex` **[number][3]** Segment Index (optional, default `0`)
    *   `options.properties` **[Object][24]** Translate Properties to output LineString (optional, default `{}`)
    *   `options.bbox` **[BBox][15]** Translate BBox to output LineString (optional, default `{}`)
    *   `options.id` **([number][3] | [string][25])** Translate Id to output LineString (optional, default `{}`)

### Examples

```javascript
var multiLine = turf.multiLineString([
    [[10, 10], [50, 30], [30, 40]],
    [[-10, -10], [-50, -30], [-30, -40]]
]);

// First Segment (defaults are 0)
turf.findSegment(multiLine);
// => Feature<LineString<[[10, 10], [50, 30]]>>

// First Segment of 2nd Multi Feature
turf.findSegment(multiLine, {multiFeatureIndex: 1});
// => Feature<LineString<[[-10, -10], [-50, -30]]>>

// Last Segment of Last Multi Feature
turf.findSegment(multiLine, {multiFeatureIndex: -1, segmentIndex: -1});
// => Feature<LineString<[[-50, -30], [-30, -40]]>>
```

Returns **[Feature][7]<[LineString][20]>** 2-vertex GeoJSON Feature LineString

## findPoint

Finds a particular Point from a GeoJSON using `@turf/meta` indexes.

Negative indexes are permitted.

### Parameters

*   `geojson` **([FeatureCollection][8] | [Feature][7] | [Geometry][10])** Any GeoJSON Feature or Geometry
*   `options` **[Object][24]** Optional parameters (optional, default `{}`)

    *   `options.featureIndex` **[number][3]** Feature Index (optional, default `0`)
    *   `options.multiFeatureIndex` **[number][3]** Multi-Feature Index (optional, default `0`)
    *   `options.geometryIndex` **[number][3]** Geometry Index (optional, default `0`)
    *   `options.coordIndex` **[number][3]** Coord Index (optional, default `0`)
    *   `options.properties` **[Object][24]** Translate Properties to output Point (optional, default `{}`)
    *   `options.bbox` **[BBox][15]** Translate BBox to output Point (optional, default `{}`)
    *   `options.id` **([number][3] | [string][25])** Translate Id to output Point (optional, default `{}`)

### Examples

```javascript
var multiLine = turf.multiLineString([
    [[10, 10], [50, 30], [30, 40]],
    [[-10, -10], [-50, -30], [-30, -40]]
]);

// First Segment (defaults are 0)
turf.findPoint(multiLine);
// => Feature<Point<[10, 10]>>

// First Segment of the 2nd Multi-Feature
turf.findPoint(multiLine, {multiFeatureIndex: 1});
// => Feature<Point<[-10, -10]>>

// Last Segment of last Multi-Feature
turf.findPoint(multiLine, {multiFeatureIndex: -1, coordIndex: -1});
// => Feature<Point<[-30, -40]>>
```

Returns **[Feature][7]<[Point][26]>** 2-vertex GeoJSON Feature Point

[1]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function

[2]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[4]: #coordeachcallback

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

[6]: #coordreducecallback

[7]: https://tools.ietf.org/html/rfc7946#section-3.2

[8]: https://tools.ietf.org/html/rfc7946#section-3.3

[9]: #propeachcallback

[10]: https://tools.ietf.org/html/rfc7946#section-3.1

[11]: #propreducecallback

[12]: https://tools.ietf.org/html/rfc7946#section-3.1.8

[13]: #featureeachcallback

[14]: #featurereducecallback

[15]: https://tools.ietf.org/html/rfc7946#section-5

[16]: #geomeachcallback

[17]: #geomreducecallback

[18]: #flatteneachcallback

[19]: #flattenreducecallback

[20]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[21]: #segmenteachcallback

[22]: #segmentreducecallback

[23]: #lineeachcallback

[24]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[25]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[26]: https://tools.ietf.org/html/rfc7946#section-3.1.2

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/meta
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
