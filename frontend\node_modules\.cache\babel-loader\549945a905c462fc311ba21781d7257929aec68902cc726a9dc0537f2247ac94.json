{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIZekri\\\\frontend\\\\src\\\\components\\\\DragVisualPage.js\",\n  _s = $RefreshSig$();\n/**\n * DragVisualPage - Interface drag & drop pour créer des visualisations\n * Permet de glisser-déposer des colonnes pour construire des graphiques\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { DndContext, DragOverlay, closestCenter } from '@dnd-kit/core';\nimport axios from 'axios';\nimport Plot from 'react-plotly.js';\nimport { BrAInBIHeader, DarkCard, SectionHeader, PrimaryButton, SecondaryButton, DarkSpinner, DarkBadge } from './YellowMindUI';\nimport { ColumnsPanel, DropZonesPanel, FixedDropBar, AggregationSelector, ChartTypeSelector, DraggableColumn } from './DragDropComponents';\nimport { FiltersSection } from './FilterComponents';\nimport { NarrationSettings, DataStorytellingPanel, NetworkDiagnostic, useDataStorytelling } from './DataStorytellingComponents';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = 'http://localhost:8000';\nconst DragVisualPage = () => {\n  _s();\n  const navigate = useNavigate();\n\n  // États pour les données\n  const [tables, setTables] = useState([]);\n  const [columns, setColumns] = useState({});\n  const [chartData, setChartData] = useState(null);\n\n  // États pour les sélections\n  const [selectedTable, setSelectedTable] = useState('');\n  const [xAxis, setXAxis] = useState(null);\n  const [yAxis, setYAxis] = useState(null);\n  const [legend, setLegend] = useState(null);\n  const [values, setValues] = useState(null);\n  const [aggFunction, setAggFunction] = useState('SUM');\n  const [chartType, setChartType] = useState('bar');\n\n  // États pour l'interface\n  const [loading, setLoading] = useState(false);\n  const [loadingTables, setLoadingTables] = useState(true);\n  const [loadingColumns, setLoadingColumns] = useState(false);\n  const [error, setError] = useState('');\n  const [activeId, setActiveId] = useState(null);\n  const [showDebugJson, setShowDebugJson] = useState(false);\n  const [filters, setFilters] = useState([]);\n  const [showNetworkDiagnostic, setShowNetworkDiagnostic] = useState(false);\n  const [showFixedDropBar, setShowFixedDropBar] = useState(true);\n  const [lastScrollY, setLastScrollY] = useState(0);\n  const [autoHideBar, setAutoHideBar] = useState(false);\n\n  // Hook pour la narration intelligente\n  const {\n    narrative,\n    loading: narrativeLoading,\n    error: narrativeError,\n    debugInfo,\n    generateNarrative,\n    copyToClipboard,\n    exportNarrative\n  } = useDataStorytelling();\n\n  // Charger les tables au démarrage\n  useEffect(() => {\n    loadTables();\n  }, []);\n\n  // Gestion du scroll pour auto-masquer la barre fixe\n  useEffect(() => {\n    const handleScroll = () => {\n      const currentScrollY = window.scrollY;\n      if (autoHideBar) {\n        if (currentScrollY > lastScrollY && currentScrollY > 100) {\n          // Scroll vers le bas - masquer la barre\n          setShowFixedDropBar(false);\n        } else if (currentScrollY < lastScrollY) {\n          // Scroll vers le haut - montrer la barre\n          setShowFixedDropBar(true);\n        }\n      }\n      setLastScrollY(currentScrollY);\n    };\n    if (autoHideBar) {\n      window.addEventListener('scroll', handleScroll, {\n        passive: true\n      });\n      return () => window.removeEventListener('scroll', handleScroll);\n    }\n  }, [lastScrollY, autoHideBar]);\n\n  // Charger les colonnes quand une table est sélectionnée\n  useEffect(() => {\n    if (selectedTable) {\n      loadColumns(selectedTable);\n      // NE PAS reset les sélections pour permettre l'analyse multi-tables\n      // Les utilisateurs peuvent analyser des colonnes de différentes tables\n      setChartData(null); // Reset seulement le graphique\n    }\n  }, [selectedTable]);\n  const loadTables = async () => {\n    try {\n      setLoadingTables(true);\n      setError('');\n      const response = await axios.get(`${API_BASE_URL}/schema/tables`);\n      setTables(response.data.tables || []);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError('Erreur lors du chargement des tables: ' + (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.detail) || err.message));\n    } finally {\n      setLoadingTables(false);\n    }\n  };\n  const loadColumns = async tableName => {\n    try {\n      setLoadingColumns(true);\n      const response = await axios.get(`${API_BASE_URL}/tables/${tableName}/columns`);\n      setColumns(response.data);\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setError('Erreur lors du chargement des colonnes: ' + (((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail) || err.message));\n    } finally {\n      setLoadingColumns(false);\n    }\n  };\n  const handleDragStart = event => {\n    setActiveId(event.active.id);\n  };\n  const handleDragEnd = event => {\n    const {\n      active,\n      over\n    } = event;\n    setActiveId(null);\n    if (!over) return;\n    const columnId = active.id;\n    const dropZoneId = over.id;\n\n    // Trouver la colonne correspondante\n    const allColumns = [...(columns.all_columns || []), ...(columns.numeric_columns || []), ...(columns.categorical_columns || []), ...(columns.date_columns || [])];\n    const column = allColumns.find(col => `column-${col.name}` === columnId);\n    if (!column) return;\n\n    // Ajouter la table source à la colonne\n    const columnWithTable = {\n      ...column,\n      table: selectedTable\n    };\n\n    // Assigner la colonne à la zone appropriée\n    assignColumnToAxis(dropZoneId, columnWithTable);\n  };\n\n  // Fonction pour assigner une colonne à un axe (utilisée par drag & drop et menu contextuel)\n  const assignColumnToAxis = (axisId, columnWithTable) => {\n    switch (axisId) {\n      case 'x-axis':\n      case 'x-axis-fixed':\n        setXAxis(columnWithTable);\n        break;\n      case 'y-axis':\n      case 'y-axis-fixed':\n        setYAxis(columnWithTable);\n        break;\n      case 'legend':\n      case 'legend-fixed':\n        setLegend(columnWithTable);\n        break;\n      case 'values':\n      case 'values-fixed':\n        setValues(columnWithTable);\n        break;\n      default:\n        break;\n    }\n  };\n\n  // Fonction pour le menu contextuel\n  const handleAddToAxis = (axisId, columnWithTable) => {\n    assignColumnToAxis(axisId, columnWithTable);\n  };\n  const generateVisualization = async () => {\n    if (!selectedTable) {\n      setError('Veuillez sélectionner une table');\n      return;\n    }\n    if (!xAxis && !values) {\n      setError('Veuillez glisser au moins une colonne dans Axe X ou Valeurs');\n      return;\n    }\n    try {\n      setLoading(true);\n      setError('');\n      const requestData = {\n        table: selectedTable,\n        // Table principale pour compatibilité\n        x_axis: (xAxis === null || xAxis === void 0 ? void 0 : xAxis.name) || null,\n        y_axis: (yAxis === null || yAxis === void 0 ? void 0 : yAxis.name) || null,\n        legend: (legend === null || legend === void 0 ? void 0 : legend.name) || null,\n        values: (values === null || values === void 0 ? void 0 : values.name) || null,\n        agg_function: aggFunction,\n        chart_type: chartType,\n        // Nouvelles propriétés pour multi-tables\n        x_axis_table: (xAxis === null || xAxis === void 0 ? void 0 : xAxis.table) || null,\n        y_axis_table: (yAxis === null || yAxis === void 0 ? void 0 : yAxis.table) || null,\n        legend_table: (legend === null || legend === void 0 ? void 0 : legend.table) || null,\n        values_table: (values === null || values === void 0 ? void 0 : values.table) || null,\n        // Filtres\n        filters: filters.filter(f => f.filter_type === 'range' && (f.min_value || f.max_value) || f.filter_type !== 'range' && f.values && f.values.length > 0).map(f => ({\n          column_name: f.column_name,\n          table_name: f.table_name,\n          filter_type: f.filter_type,\n          values: f.values || [],\n          min_value: f.min_value || null,\n          max_value: f.max_value || null\n        }))\n      };\n      const response = await axios.post(`${API_BASE_URL}/drag-visual`, requestData);\n      setChartData(response.data);\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      setError('Erreur lors de la génération: ' + (((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.detail) || err.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n  const clearAll = () => {\n    setXAxis(null);\n    setYAxis(null);\n    setLegend(null);\n    setValues(null);\n    setChartData(null);\n  };\n  const renderChart = () => {\n    if (!chartData) return null;\n    const plotData = [];\n\n    // Construire le titre avec les filtres actifs\n    let title = chartData.title;\n    if (activeFiltersCount > 0) {\n      title += ` (${activeFiltersCount} filtre${activeFiltersCount > 1 ? 's' : ''} appliqué${activeFiltersCount > 1 ? 's' : ''})`;\n    }\n    const layout = {\n      title: title,\n      autosize: true,\n      height: 500,\n      margin: {\n        l: 50,\n        r: 50,\n        t: 80,\n        b: 50\n      },\n      paper_bgcolor: 'rgba(0,0,0,0)',\n      plot_bgcolor: 'rgba(0,0,0,0)',\n      font: {\n        color: '#F3F4F6'\n      }\n    };\n    switch (chartData.type) {\n      case 'bar':\n        if (chartData.legend && Array.isArray(chartData.y[0])) {\n          // Graphique avec légende\n          chartData.legend.forEach((legendItem, index) => {\n            plotData.push({\n              x: chartData.x,\n              y: chartData.y.map(row => row[index] || 0),\n              type: 'bar',\n              name: legendItem,\n              marker: {\n                color: `hsl(${index * 60 % 360}, 70%, 60%)`\n              }\n            });\n          });\n        } else {\n          plotData.push({\n            x: chartData.x,\n            y: chartData.y,\n            type: 'bar',\n            marker: {\n              color: '#a74eff'\n            }\n          });\n        }\n        layout.xaxis = {\n          title: chartData.xlabel\n        };\n        layout.yaxis = {\n          title: chartData.ylabel\n        };\n        break;\n      case 'line':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'scatter',\n          mode: 'lines+markers',\n          line: {\n            color: '#a74eff',\n            width: 3\n          },\n          marker: {\n            color: '#3db5ff',\n            size: 8\n          }\n        });\n        layout.xaxis = {\n          title: chartData.xlabel\n        };\n        layout.yaxis = {\n          title: chartData.ylabel\n        };\n        break;\n      case 'pie':\n        plotData.push({\n          labels: chartData.x,\n          values: chartData.y,\n          type: 'pie',\n          marker: {\n            colors: ['#a74eff', '#3db5ff', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#43e97b']\n          },\n          textinfo: 'label+percent',\n          textposition: 'outside'\n        });\n        break;\n      case 'scatter':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'scatter',\n          mode: 'markers',\n          marker: {\n            color: '#a74eff',\n            size: 10,\n            line: {\n              color: '#3db5ff',\n              width: 1\n            }\n          }\n        });\n        layout.xaxis = {\n          title: chartData.xlabel\n        };\n        layout.yaxis = {\n          title: chartData.ylabel\n        };\n        break;\n      default:\n        return /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-400\",\n          children: [\"Type de graphique non support\\xE9: \", chartData.type]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 16\n        }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Plot, {\n      data: plotData,\n      layout: layout,\n      config: {\n        responsive: true,\n        displayModeBar: true\n      },\n      style: {\n        width: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 7\n    }, this);\n  };\n  const canGenerate = selectedTable && (xAxis || values);\n\n  // Détecter si c'est une analyse multi-tables\n  const isMultiTable = () => {\n    const tables = new Set();\n    [xAxis, yAxis, legend, values].forEach(col => {\n      if (col !== null && col !== void 0 && col.table) tables.add(col.table);\n    });\n    return tables.size > 1;\n  };\n\n  // Compter les filtres actifs\n  const activeFiltersCount = filters.filter(f => f.filter_type === 'range' && (f.min_value || f.max_value) || f.filter_type !== 'range' && f.values && f.values.length > 0).length;\n  return /*#__PURE__*/_jsxDEV(DndContext, {\n    collisionDetection: closestCenter,\n    onDragStart: handleDragStart,\n    onDragEnd: handleDragEnd,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-950 text-gray-100\",\n      children: [/*#__PURE__*/_jsxDEV(BrAInBIHeader, {\n        hasData: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `max-w-7xl mx-auto px-6 py-8 ${showFixedDropBar ? 'pb-32' : 'pb-8'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xl\",\n                    children: \"\\uD83C\\uDFAF\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\",\n                  children: \"Cr\\xE9ateur Drag & Drop\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-400\",\n                children: \"Glissez-d\\xE9posez vos colonnes pour cr\\xE9er des visualisations interactives\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-purple-400 mt-1\",\n                children: \"\\uD83D\\uDCA1 Jointures automatiques : analysez des colonnes de diff\\xE9rentes tables\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-blue-400 mt-1\",\n                children: \"\\uD83D\\uDCD6 Narration IA : g\\xE9n\\xE9rez des histoires intelligentes \\xE0 partir de vos donn\\xE9es\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: () => setShowFixedDropBar(!showFixedDropBar),\n                icon: showFixedDropBar ? \"👁️\" : \"👁️‍🗨️\",\n                className: showFixedDropBar ? \"!border-purple-500 !text-purple-400\" : \"\",\n                children: \"Barre fixe\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: () => navigate('/visual-builder'),\n                icon: \"\\uD83D\\uDCCA\",\n                children: \"Visual Builder\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: () => navigate('/tables'),\n                icon: \"\\uD83D\\uDCCB\",\n                children: \"Tables\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: () => navigate('/'),\n                icon: \"\\uD83C\\uDFE0\",\n                children: \"Accueil\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: /*#__PURE__*/_jsxDEV(DarkCard, {\n            children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n              title: \"1. S\\xE9lectionner la table\",\n              subtitle: \"Choisissez la source de donn\\xE9es\",\n              icon: \"\\uD83D\\uDDC2\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 15\n            }, this), loadingTables ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center py-4\",\n              children: [/*#__PURE__*/_jsxDEV(DarkSpinner, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 text-gray-400\",\n                children: \"Chargement...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedTable,\n              onChange: e => setSelectedTable(e.target.value),\n              className: \"w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-gray-100 focus:border-purple-500 focus:outline-none\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"-- S\\xE9lectionner une table --\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 19\n              }, this), tables.map((table, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: table.TABLE_NAME,\n                children: [table.TABLE_NAME, \" (\", table.COLUMN_COUNT, \" colonnes)\"]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this), selectedTable && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 xl:grid-cols-5 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"xl:col-span-2\",\n            children: /*#__PURE__*/_jsxDEV(DarkCard, {\n              className: \"h-fit\",\n              children: loadingColumns ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: [/*#__PURE__*/_jsxDEV(DarkSpinner, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-gray-400\",\n                  children: \"Chargement des colonnes...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(ColumnsPanel, {\n                columns: columns.all_columns || [],\n                title: \"Colonnes disponibles\",\n                onAddToAxis: handleAddToAxis,\n                selectedTable: selectedTable\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"xl:col-span-3 space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(FiltersSection, {\n              filters: filters,\n              onFiltersChange: setFilters\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(DarkCard, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n                  title: \"2. Configurer les axes\",\n                  subtitle: \"Glissez les colonnes dans les zones appropri\\xE9es\",\n                  icon: \"\\uD83C\\uDFAF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(SecondaryButton, {\n                    onClick: () => generateNarrative({\n                      focus: null,\n                      time_period: '12_months',\n                      include_recommendations: true\n                    }),\n                    icon: \"\\uD83D\\uDCD6\",\n                    disabled: narrativeLoading,\n                    className: \"!text-blue-400 !border-blue-600 hover:!bg-blue-600/20\",\n                    children: narrativeLoading ? 'Génération...' : 'Narration rapide'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                    onClick: clearAll,\n                    icon: \"\\uD83D\\uDDD1\\uFE0F\",\n                    className: \"!text-red-400 !border-red-600 hover:!bg-red-600/20\",\n                    children: \"Tout effacer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 513,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(DropZonesPanel, {\n                xAxis: xAxis,\n                yAxis: yAxis,\n                legend: legend,\n                values: values,\n                onClearX: () => setXAxis(null),\n                onClearY: () => setYAxis(null),\n                onClearLegend: () => setLegend(null),\n                onClearValues: () => setValues(null)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(DarkCard, {\n              children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n                title: \"3. Configuration\",\n                subtitle: \"Choisissez l'agr\\xE9gation et le type de graphique\",\n                icon: \"\\u2699\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(AggregationSelector, {\n                  value: aggFunction,\n                  onChange: setAggFunction,\n                  disabled: !values && aggFunction !== 'COUNT'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ChartTypeSelector, {\n                  value: chartType,\n                  onChange: setChartType\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-6 flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(PrimaryButton, {\n                      onClick: generateVisualization,\n                      disabled: !canGenerate || loading,\n                      loading: loading,\n                      icon: loading ? null : \"🚀\",\n                      className: \"!py-3 !px-6\",\n                      children: loading ? 'Génération...' : 'Générer la visualisation'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 555,\n                      columnNumber: 25\n                    }, this), isMultiTable() && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2 px-3 py-2 bg-blue-600/20 border border-blue-500 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-blue-400\",\n                        children: \"\\uD83D\\uDD17\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 567,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-blue-300 font-medium\",\n                        children: \"Analyse multi-tables\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 568,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 566,\n                      columnNumber: 27\n                    }, this), activeFiltersCount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2 px-3 py-2 bg-green-600/20 border border-green-500 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-green-400\",\n                        children: \"\\uD83E\\uDDE9\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 574,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-green-300 font-medium\",\n                        children: [activeFiltersCount, \" filtre\", activeFiltersCount > 1 ? 's' : '', \" actif\", activeFiltersCount > 1 ? 's' : '']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 575,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 573,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 554,\n                    columnNumber: 23\n                  }, this), chartData && /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                    onClick: () => setShowDebugJson(!showDebugJson),\n                    icon: \"\\uD83D\\uDD0D\",\n                    className: \"!py-3 !px-4\",\n                    children: showDebugJson ? 'Masquer JSON' : 'Voir JSON'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 583,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 21\n                }, this), !canGenerate && selectedTable && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-500 text-sm\",\n                  children: \"Glissez au moins une colonne dans Axe X ou Valeurs\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(DarkCard, {\n              children: [!chartData ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-center justify-center py-16 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-24 h-24 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl mb-6 flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-4xl\",\n                    children: \"\\uD83C\\uDFAF\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 606,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 605,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-semibold text-gray-100 mb-4\",\n                  children: \"Pr\\xEAt \\xE0 cr\\xE9er votre visualisation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-400 max-w-md\",\n                  children: \"Glissez vos colonnes dans les zones appropri\\xE9es et cliquez sur \\\"G\\xE9n\\xE9rer\\\" pour cr\\xE9er votre graphique\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n                  title: \"Visualisation g\\xE9n\\xE9r\\xE9e\",\n                  subtitle: `${chartData.type.toUpperCase()} - ${chartData.data_points} points de données`,\n                  icon: \"\\uD83D\\uDCCA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 617,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-6\",\n                  children: renderChart()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 23\n                }, this), chartData.success && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 p-3 bg-green-900/20 border border-green-700 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-green-400 text-sm\",\n                    children: [\"\\u2705 \", chartData.message]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 629,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 25\n                }, this), showDebugJson && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 p-4 bg-gray-900 border border-gray-700 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-300 mb-2\",\n                    children: \"JSON de r\\xE9ponse :\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 638,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                    className: \"text-xs text-gray-400 overflow-x-auto\",\n                    children: JSON.stringify(chartData, null, 2)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 637,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 21\n              }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 p-4 bg-red-900/20 border border-red-700 rounded-lg\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-red-400\",\n                  children: [\"\\u274C \", error]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 602,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8 space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(NarrationSettings, {\n                onGenerate: generateNarrative,\n                loading: narrativeLoading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 19\n              }, this), narrativeError && /*#__PURE__*/_jsxDEV(DarkCard, {\n                className: \"border-red-500\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3 text-red-400\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xl\",\n                      children: \"\\u26A0\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 667,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"font-medium\",\n                        children: \"Erreur de g\\xE9n\\xE9ration de narration\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 669,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-red-300\",\n                        children: \"La g\\xE9n\\xE9ration automatique de l'histoire des donn\\xE9es a \\xE9chou\\xE9\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 670,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 668,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 666,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-red-900/20 p-4 rounded border border-red-700\",\n                    children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                      className: \"text-sm text-red-300 whitespace-pre-wrap font-mono leading-relaxed\",\n                      children: narrativeError\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 677,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 676,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-3\",\n                    children: [/*#__PURE__*/_jsxDEV(SecondaryButton, {\n                      onClick: () => generateNarrative({\n                        focus: null,\n                        time_period: '12_months',\n                        include_recommendations: true\n                      }),\n                      icon: \"\\uD83D\\uDD04\",\n                      className: \"!py-2 !px-3 !text-sm\",\n                      disabled: narrativeLoading,\n                      children: \"R\\xE9essayer\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 683,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                      onClick: () => generateNarrative({\n                        focus: null,\n                        time_period: '6_months',\n                        include_recommendations: false\n                      }),\n                      icon: \"\\u26A1\",\n                      className: \"!py-2 !px-3 !text-sm\",\n                      disabled: narrativeLoading,\n                      children: \"Mode simplifi\\xE9\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 691,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                      onClick: () => window.open('http://localhost:8000/health', '_blank'),\n                      icon: \"\\uD83D\\uDD0D\",\n                      className: \"!py-2 !px-3 !text-sm !text-blue-400 !border-blue-600\",\n                      children: \"Test backend\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 699,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                      onClick: () => window.open('http://localhost:8000/narrate/test', '_blank'),\n                      icon: \"\\uD83E\\uDDEA\",\n                      className: \"!py-2 !px-3 !text-sm !text-green-400 !border-green-600\",\n                      children: \"Test narration\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 706,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                      onClick: () => setShowNetworkDiagnostic(true),\n                      icon: \"\\uD83D\\uDD27\",\n                      className: \"!py-2 !px-3 !text-sm !text-purple-400 !border-purple-600\",\n                      children: \"Diagnostic r\\xE9seau\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 713,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 682,\n                    columnNumber: 25\n                  }, this), (debugInfo === null || debugInfo === void 0 ? void 0 : debugInfo.error_type) === 'connection' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-yellow-900/20 p-3 rounded border border-yellow-700\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-start space-x-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-yellow-400 mt-1\",\n                        children: \"\\uD83D\\uDCA1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 725,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-yellow-300\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"font-medium mb-2\",\n                          children: \"Probl\\xE8me de connexion d\\xE9tect\\xE9\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 727,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                          className: \"space-y-1 text-xs\",\n                          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                            children: \"\\u2022 V\\xE9rifiez que le backend est d\\xE9marr\\xE9 sur le port 8000\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 729,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [\"\\u2022 Testez l'URL : \", /*#__PURE__*/_jsxDEV(\"code\", {\n                              className: \"bg-yellow-800/30 px-1 rounded\",\n                              children: \"http://localhost:8000\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 730,\n                              columnNumber: 56\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 730,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: \"\\u2022 Consultez les logs du serveur backend\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 731,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 728,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 726,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 724,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 723,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 21\n              }, this), debugInfo && (debugInfo.status === 'error' || process.env.NODE_ENV === 'development') && /*#__PURE__*/_jsxDEV(DarkCard, {\n                className: \"border-yellow-500\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3 text-yellow-400\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-lg\",\n                      children: \"\\uD83D\\uDD27\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 746,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium\",\n                      children: \"Informations de debug\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 747,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 745,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-yellow-900/20 p-3 rounded border border-yellow-700\",\n                    children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                      className: \"text-xs text-yellow-300 whitespace-pre-wrap font-mono\",\n                      children: JSON.stringify(debugInfo, null, 2)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 751,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 750,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 21\n              }, this), showNetworkDiagnostic && /*#__PURE__*/_jsxDEV(NetworkDiagnostic, {\n                onClose: () => setShowNetworkDiagnostic(false)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(DataStorytellingPanel, {\n                narrative: narrative,\n                dataSummary: (narrative === null || narrative === void 0 ? void 0 : narrative.data_summary) || {},\n                onCopy: copyToClipboard,\n                onExport: exportNarrative\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FixedDropBar, {\n      xAxis: xAxis,\n      yAxis: yAxis,\n      legend: legend,\n      values: values,\n      onClearX: () => setXAxis(null),\n      onClearY: () => setYAxis(null),\n      onClearLegend: () => setLegend(null),\n      onClearValues: () => setValues(null),\n      isVisible: showFixedDropBar,\n      position: \"bottom\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 780,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DragOverlay, {\n      children: activeId ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transform rotate-3 scale-105\",\n        children: /*#__PURE__*/_jsxDEV(DraggableColumn, {\n          id: activeId,\n          column: {\n            name: 'Glissement...',\n            type: 'unknown'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 797,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 796,\n        columnNumber: 11\n      }, this) : null\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 794,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 384,\n    columnNumber: 5\n  }, this);\n};\n_s(DragVisualPage, \"1Z4iUbbMivipLM5qjcnL6S8qlg0=\", false, function () {\n  return [useNavigate, useDataStorytelling];\n});\n_c = DragVisualPage;\nexport default DragVisualPage;\nvar _c;\n$RefreshReg$(_c, \"DragVisualPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "DndContext", "DragOverlay", "closestCenter", "axios", "Plot", "BrAInBIHeader", "DarkCard", "SectionHeader", "PrimaryButton", "SecondaryButton", "Dark<PERSON><PERSON>ner", "DarkBadge", "ColumnsPanel", "DropZonesPanel", "FixedDropBar", "AggregationSelector", "ChartTypeSelector", "DraggableColumn", "FiltersSection", "NarrationSettings", "DataStorytellingPanel", "NetworkDiagnostic", "useDataStorytelling", "jsxDEV", "_jsxDEV", "API_BASE_URL", "DragVisualPage", "_s", "navigate", "tables", "setTables", "columns", "setColumns", "chartData", "setChartData", "selectedTable", "setSelectedTable", "xAxis", "setXAxis", "yAxis", "setYAxis", "legend", "setLegend", "values", "set<PERSON><PERSON><PERSON>", "aggFunction", "setAggFunction", "chartType", "setChartType", "loading", "setLoading", "loadingTables", "setLoadingTables", "loadingColumns", "setLoadingColumns", "error", "setError", "activeId", "setActiveId", "showDebugJson", "setShowDebugJson", "filters", "setFilters", "showNetworkDiagnostic", "setShowNetworkDiagnostic", "showFixedDropBar", "setShowFixedDropBar", "lastScrollY", "setLastScrollY", "autoHideBar", "setAutoHideBar", "narrative", "narrativeLoading", "narrativeError", "debugInfo", "generateNarrative", "copyToClipboard", "exportNarrative", "loadTables", "handleScroll", "currentScrollY", "window", "scrollY", "addEventListener", "passive", "removeEventListener", "loadColumns", "response", "get", "data", "err", "_err$response", "_err$response$data", "detail", "message", "tableName", "_err$response2", "_err$response2$data", "handleDragStart", "event", "active", "id", "handleDragEnd", "over", "columnId", "dropZoneId", "allColumns", "all_columns", "numeric_columns", "categorical_columns", "date_columns", "column", "find", "col", "name", "columnWithTable", "table", "assignColumnToAxis", "axisId", "handleAddToAxis", "generateVisualization", "requestData", "x_axis", "y_axis", "agg_function", "chart_type", "x_axis_table", "y_axis_table", "legend_table", "values_table", "filter", "f", "filter_type", "min_value", "max_value", "length", "map", "column_name", "table_name", "post", "_err$response3", "_err$response3$data", "clearAll", "<PERSON><PERSON><PERSON>", "plotData", "title", "activeFiltersCount", "layout", "autosize", "height", "margin", "l", "r", "t", "b", "paper_bgcolor", "plot_bgcolor", "font", "color", "type", "Array", "isArray", "y", "for<PERSON>ach", "legendItem", "index", "push", "x", "row", "marker", "xaxis", "xlabel", "yaxis", "ylabel", "mode", "line", "width", "size", "labels", "colors", "textinfo", "textposition", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "config", "responsive", "displayModeBar", "style", "canGenerate", "isMultiTable", "Set", "add", "collisionDetection", "onDragStart", "onDragEnd", "hasData", "onClick", "icon", "subtitle", "value", "onChange", "e", "target", "TABLE_NAME", "COLUMN_COUNT", "onAddToAxis", "onFiltersChange", "focus", "time_period", "include_recommendations", "disabled", "onClearX", "onClearY", "onClearLegend", "onClearValues", "toUpperCase", "data_points", "success", "JSON", "stringify", "onGenerate", "open", "error_type", "status", "process", "env", "NODE_ENV", "onClose", "dataSummary", "data_summary", "onCopy", "onExport", "isVisible", "position", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/AIZekri/frontend/src/components/DragVisualPage.js"], "sourcesContent": ["/**\n * DragVisualPage - Interface drag & drop pour créer des visualisations\n * Permet de glisser-déposer des colonnes pour construire des graphiques\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { DndContext, DragOverlay, closestCenter } from '@dnd-kit/core';\nimport axios from 'axios';\nimport Plot from 'react-plotly.js';\nimport {\n  BrAInBIHeader,\n  DarkCard,\n  SectionHeader,\n  PrimaryButton,\n  SecondaryButton,\n  DarkSpinner,\n  DarkBadge\n} from './YellowMindUI';\nimport {\n  ColumnsPanel,\n  DropZonesPanel,\n  FixedDropBar,\n  AggregationSelector,\n  ChartTypeSelector,\n  DraggableColumn\n} from './DragDropComponents';\nimport { FiltersSection } from './FilterComponents';\nimport {\n  NarrationSettings,\n  DataStorytellingPanel,\n  NetworkDiagnostic,\n  useDataStorytelling\n} from './DataStorytellingComponents';\n\nconst API_BASE_URL = 'http://localhost:8000';\n\nconst DragVisualPage = () => {\n  const navigate = useNavigate();\n  \n  // États pour les données\n  const [tables, setTables] = useState([]);\n  const [columns, setColumns] = useState({});\n  const [chartData, setChartData] = useState(null);\n  \n  // États pour les sélections\n  const [selectedTable, setSelectedTable] = useState('');\n  const [xAxis, setXAxis] = useState(null);\n  const [yAxis, setYAxis] = useState(null);\n  const [legend, setLegend] = useState(null);\n  const [values, setValues] = useState(null);\n  const [aggFunction, setAggFunction] = useState('SUM');\n  const [chartType, setChartType] = useState('bar');\n  \n  // États pour l'interface\n  const [loading, setLoading] = useState(false);\n  const [loadingTables, setLoadingTables] = useState(true);\n  const [loadingColumns, setLoadingColumns] = useState(false);\n  const [error, setError] = useState('');\n  const [activeId, setActiveId] = useState(null);\n  const [showDebugJson, setShowDebugJson] = useState(false);\n  const [filters, setFilters] = useState([]);\n  const [showNetworkDiagnostic, setShowNetworkDiagnostic] = useState(false);\n  const [showFixedDropBar, setShowFixedDropBar] = useState(true);\n  const [lastScrollY, setLastScrollY] = useState(0);\n  const [autoHideBar, setAutoHideBar] = useState(false);\n\n  // Hook pour la narration intelligente\n  const {\n    narrative,\n    loading: narrativeLoading,\n    error: narrativeError,\n    debugInfo,\n    generateNarrative,\n    copyToClipboard,\n    exportNarrative\n  } = useDataStorytelling();\n\n  // Charger les tables au démarrage\n  useEffect(() => {\n    loadTables();\n  }, []);\n\n  // Gestion du scroll pour auto-masquer la barre fixe\n  useEffect(() => {\n    const handleScroll = () => {\n      const currentScrollY = window.scrollY;\n\n      if (autoHideBar) {\n        if (currentScrollY > lastScrollY && currentScrollY > 100) {\n          // Scroll vers le bas - masquer la barre\n          setShowFixedDropBar(false);\n        } else if (currentScrollY < lastScrollY) {\n          // Scroll vers le haut - montrer la barre\n          setShowFixedDropBar(true);\n        }\n      }\n\n      setLastScrollY(currentScrollY);\n    };\n\n    if (autoHideBar) {\n      window.addEventListener('scroll', handleScroll, { passive: true });\n      return () => window.removeEventListener('scroll', handleScroll);\n    }\n  }, [lastScrollY, autoHideBar]);\n\n  // Charger les colonnes quand une table est sélectionnée\n  useEffect(() => {\n    if (selectedTable) {\n      loadColumns(selectedTable);\n      // NE PAS reset les sélections pour permettre l'analyse multi-tables\n      // Les utilisateurs peuvent analyser des colonnes de différentes tables\n      setChartData(null); // Reset seulement le graphique\n    }\n  }, [selectedTable]);\n\n  const loadTables = async () => {\n    try {\n      setLoadingTables(true);\n      setError('');\n      const response = await axios.get(`${API_BASE_URL}/schema/tables`);\n      setTables(response.data.tables || []);\n    } catch (err) {\n      setError('Erreur lors du chargement des tables: ' + (err.response?.data?.detail || err.message));\n    } finally {\n      setLoadingTables(false);\n    }\n  };\n\n  const loadColumns = async (tableName) => {\n    try {\n      setLoadingColumns(true);\n      const response = await axios.get(`${API_BASE_URL}/tables/${tableName}/columns`);\n      setColumns(response.data);\n    } catch (err) {\n      setError('Erreur lors du chargement des colonnes: ' + (err.response?.data?.detail || err.message));\n    } finally {\n      setLoadingColumns(false);\n    }\n  };\n\n  const handleDragStart = (event) => {\n    setActiveId(event.active.id);\n  };\n\n  const handleDragEnd = (event) => {\n    const { active, over } = event;\n    setActiveId(null);\n\n    if (!over) return;\n\n    const columnId = active.id;\n    const dropZoneId = over.id;\n\n    // Trouver la colonne correspondante\n    const allColumns = [\n      ...(columns.all_columns || []),\n      ...(columns.numeric_columns || []),\n      ...(columns.categorical_columns || []),\n      ...(columns.date_columns || [])\n    ];\n\n    const column = allColumns.find(col => `column-${col.name}` === columnId);\n    if (!column) return;\n\n    // Ajouter la table source à la colonne\n    const columnWithTable = { ...column, table: selectedTable };\n\n    // Assigner la colonne à la zone appropriée\n    assignColumnToAxis(dropZoneId, columnWithTable);\n  };\n\n  // Fonction pour assigner une colonne à un axe (utilisée par drag & drop et menu contextuel)\n  const assignColumnToAxis = (axisId, columnWithTable) => {\n    switch (axisId) {\n      case 'x-axis':\n      case 'x-axis-fixed':\n        setXAxis(columnWithTable);\n        break;\n      case 'y-axis':\n      case 'y-axis-fixed':\n        setYAxis(columnWithTable);\n        break;\n      case 'legend':\n      case 'legend-fixed':\n        setLegend(columnWithTable);\n        break;\n      case 'values':\n      case 'values-fixed':\n        setValues(columnWithTable);\n        break;\n      default:\n        break;\n    }\n  };\n\n  // Fonction pour le menu contextuel\n  const handleAddToAxis = (axisId, columnWithTable) => {\n    assignColumnToAxis(axisId, columnWithTable);\n  };\n\n  const generateVisualization = async () => {\n    if (!selectedTable) {\n      setError('Veuillez sélectionner une table');\n      return;\n    }\n\n    if (!xAxis && !values) {\n      setError('Veuillez glisser au moins une colonne dans Axe X ou Valeurs');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError('');\n      \n      const requestData = {\n        table: selectedTable, // Table principale pour compatibilité\n        x_axis: xAxis?.name || null,\n        y_axis: yAxis?.name || null,\n        legend: legend?.name || null,\n        values: values?.name || null,\n        agg_function: aggFunction,\n        chart_type: chartType,\n        // Nouvelles propriétés pour multi-tables\n        x_axis_table: xAxis?.table || null,\n        y_axis_table: yAxis?.table || null,\n        legend_table: legend?.table || null,\n        values_table: values?.table || null,\n        // Filtres\n        filters: filters.filter(f =>\n          (f.filter_type === 'range' && (f.min_value || f.max_value)) ||\n          (f.filter_type !== 'range' && f.values && f.values.length > 0)\n        ).map(f => ({\n          column_name: f.column_name,\n          table_name: f.table_name,\n          filter_type: f.filter_type,\n          values: f.values || [],\n          min_value: f.min_value || null,\n          max_value: f.max_value || null\n        }))\n      };\n\n      const response = await axios.post(`${API_BASE_URL}/drag-visual`, requestData);\n      setChartData(response.data);\n    } catch (err) {\n      setError('Erreur lors de la génération: ' + (err.response?.data?.detail || err.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const clearAll = () => {\n    setXAxis(null);\n    setYAxis(null);\n    setLegend(null);\n    setValues(null);\n    setChartData(null);\n  };\n\n  const renderChart = () => {\n    if (!chartData) return null;\n\n    const plotData = [];\n\n    // Construire le titre avec les filtres actifs\n    let title = chartData.title;\n    if (activeFiltersCount > 0) {\n      title += ` (${activeFiltersCount} filtre${activeFiltersCount > 1 ? 's' : ''} appliqué${activeFiltersCount > 1 ? 's' : ''})`;\n    }\n\n    const layout = {\n      title: title,\n      autosize: true,\n      height: 500,\n      margin: { l: 50, r: 50, t: 80, b: 50 },\n      paper_bgcolor: 'rgba(0,0,0,0)',\n      plot_bgcolor: 'rgba(0,0,0,0)',\n      font: { color: '#F3F4F6' }\n    };\n\n    switch (chartData.type) {\n      case 'bar':\n        if (chartData.legend && Array.isArray(chartData.y[0])) {\n          // Graphique avec légende\n          chartData.legend.forEach((legendItem, index) => {\n            plotData.push({\n              x: chartData.x,\n              y: chartData.y.map(row => row[index] || 0),\n              type: 'bar',\n              name: legendItem,\n              marker: { \n                color: `hsl(${(index * 60) % 360}, 70%, 60%)`\n              }\n            });\n          });\n        } else {\n          plotData.push({\n            x: chartData.x,\n            y: chartData.y,\n            type: 'bar',\n            marker: { color: '#a74eff' }\n          });\n        }\n        layout.xaxis = { title: chartData.xlabel };\n        layout.yaxis = { title: chartData.ylabel };\n        break;\n\n      case 'line':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'scatter',\n          mode: 'lines+markers',\n          line: { color: '#a74eff', width: 3 },\n          marker: { color: '#3db5ff', size: 8 }\n        });\n        layout.xaxis = { title: chartData.xlabel };\n        layout.yaxis = { title: chartData.ylabel };\n        break;\n\n      case 'pie':\n        plotData.push({\n          labels: chartData.x,\n          values: chartData.y,\n          type: 'pie',\n          marker: { \n            colors: ['#a74eff', '#3db5ff', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#43e97b']\n          },\n          textinfo: 'label+percent',\n          textposition: 'outside'\n        });\n        break;\n\n      case 'scatter':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'scatter',\n          mode: 'markers',\n          marker: { \n            color: '#a74eff',\n            size: 10,\n            line: { color: '#3db5ff', width: 1 }\n          }\n        });\n        layout.xaxis = { title: chartData.xlabel };\n        layout.yaxis = { title: chartData.ylabel };\n        break;\n\n      default:\n        return <p className=\"text-red-400\">Type de graphique non supporté: {chartData.type}</p>;\n    }\n\n    return (\n      <Plot\n        data={plotData}\n        layout={layout}\n        config={{ responsive: true, displayModeBar: true }}\n        style={{ width: '100%' }}\n      />\n    );\n  };\n\n  const canGenerate = selectedTable && (xAxis || values);\n\n  // Détecter si c'est une analyse multi-tables\n  const isMultiTable = () => {\n    const tables = new Set();\n    [xAxis, yAxis, legend, values].forEach(col => {\n      if (col?.table) tables.add(col.table);\n    });\n    return tables.size > 1;\n  };\n\n  // Compter les filtres actifs\n  const activeFiltersCount = filters.filter(f =>\n    (f.filter_type === 'range' && (f.min_value || f.max_value)) ||\n    (f.filter_type !== 'range' && f.values && f.values.length > 0)\n  ).length;\n\n  return (\n    <DndContext\n      collisionDetection={closestCenter}\n      onDragStart={handleDragStart}\n      onDragEnd={handleDragEnd}\n    >\n      <div className=\"min-h-screen bg-gray-950 text-gray-100\">\n        <BrAInBIHeader hasData={true} />\n        \n        <div className={`max-w-7xl mx-auto px-6 py-8 ${showFixedDropBar ? 'pb-32' : 'pb-8'}`}>\n          {/* Header */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <div>\n                <div className=\"flex items-center space-x-3 mb-2\">\n                  <div className=\"w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center\">\n                    <span className=\"text-xl\">🎯</span>\n                  </div>\n                  <h1 className=\"text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\">\n                    Créateur Drag & Drop\n                  </h1>\n                </div>\n                <p className=\"text-gray-400\">\n                  Glissez-déposez vos colonnes pour créer des visualisations interactives\n                </p>\n                <p className=\"text-sm text-purple-400 mt-1\">\n                  💡 Jointures automatiques : analysez des colonnes de différentes tables\n                </p>\n                <p className=\"text-sm text-blue-400 mt-1\">\n                  📖 Narration IA : générez des histoires intelligentes à partir de vos données\n                </p>\n              </div>\n              <div className=\"flex space-x-3\">\n                <SecondaryButton\n                  onClick={() => setShowFixedDropBar(!showFixedDropBar)}\n                  icon={showFixedDropBar ? \"👁️\" : \"👁️‍🗨️\"}\n                  className={showFixedDropBar ? \"!border-purple-500 !text-purple-400\" : \"\"}\n                >\n                  Barre fixe\n                </SecondaryButton>\n                <SecondaryButton onClick={() => navigate('/visual-builder')} icon=\"📊\">\n                  Visual Builder\n                </SecondaryButton>\n                <SecondaryButton onClick={() => navigate('/tables')} icon=\"📋\">\n                  Tables\n                </SecondaryButton>\n                <SecondaryButton onClick={() => navigate('/')} icon=\"🏠\">\n                  Accueil\n                </SecondaryButton>\n              </div>\n            </div>\n          </div>\n\n          {/* Sélection de table */}\n          <div className=\"mb-8\">\n            <DarkCard>\n              <SectionHeader\n                title=\"1. Sélectionner la table\"\n                subtitle=\"Choisissez la source de données\"\n                icon=\"🗂️\"\n              />\n              \n              {loadingTables ? (\n                <div className=\"flex items-center justify-center py-4\">\n                  <DarkSpinner />\n                  <span className=\"ml-2 text-gray-400\">Chargement...</span>\n                </div>\n              ) : (\n                <select\n                  value={selectedTable}\n                  onChange={(e) => setSelectedTable(e.target.value)}\n                  className=\"w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-gray-100 focus:border-purple-500 focus:outline-none\"\n                >\n                  <option value=\"\">-- Sélectionner une table --</option>\n                  {tables.map((table, index) => (\n                    <option key={index} value={table.TABLE_NAME}>\n                      {table.TABLE_NAME} ({table.COLUMN_COUNT} colonnes)\n                    </option>\n                  ))}\n                </select>\n              )}\n            </DarkCard>\n          </div>\n\n          {selectedTable && (\n            <div className=\"grid grid-cols-1 xl:grid-cols-5 gap-8\">\n              {/* Panneau des colonnes - Plus large */}\n              <div className=\"xl:col-span-2\">\n                <DarkCard className=\"h-fit\">\n                  {loadingColumns ? (\n                    <div className=\"flex items-center justify-center py-8\">\n                      <DarkSpinner />\n                      <span className=\"ml-2 text-gray-400\">Chargement des colonnes...</span>\n                    </div>\n                  ) : (\n                    <ColumnsPanel\n                      columns={columns.all_columns || []}\n                      title=\"Colonnes disponibles\"\n                      onAddToAxis={handleAddToAxis}\n                      selectedTable={selectedTable}\n                    />\n                  )}\n                </DarkCard>\n              </div>\n\n              {/* Zone principale - Plus compacte */}\n              <div className=\"xl:col-span-3 space-y-6\">\n                {/* Section des filtres */}\n                <FiltersSection\n                  filters={filters}\n                  onFiltersChange={setFilters}\n                />\n\n                {/* Zones de drop */}\n                <DarkCard>\n                  <div className=\"flex items-center justify-between mb-6\">\n                    <SectionHeader\n                      title=\"2. Configurer les axes\"\n                      subtitle=\"Glissez les colonnes dans les zones appropriées\"\n                      icon=\"🎯\"\n                    />\n                    <div className=\"flex space-x-3\">\n                      <SecondaryButton\n                        onClick={() => generateNarrative({ focus: null, time_period: '12_months', include_recommendations: true })}\n                        icon=\"📖\"\n                        disabled={narrativeLoading}\n                        className=\"!text-blue-400 !border-blue-600 hover:!bg-blue-600/20\"\n                      >\n                        {narrativeLoading ? 'Génération...' : 'Narration rapide'}\n                      </SecondaryButton>\n                      <SecondaryButton onClick={clearAll} icon=\"🗑️\" className=\"!text-red-400 !border-red-600 hover:!bg-red-600/20\">\n                        Tout effacer\n                      </SecondaryButton>\n                    </div>\n                  </div>\n                  \n                  <DropZonesPanel\n                    xAxis={xAxis}\n                    yAxis={yAxis}\n                    legend={legend}\n                    values={values}\n                    onClearX={() => setXAxis(null)}\n                    onClearY={() => setYAxis(null)}\n                    onClearLegend={() => setLegend(null)}\n                    onClearValues={() => setValues(null)}\n                  />\n                </DarkCard>\n\n                {/* Configuration */}\n                <DarkCard>\n                  <SectionHeader\n                    title=\"3. Configuration\"\n                    subtitle=\"Choisissez l'agrégation et le type de graphique\"\n                    icon=\"⚙️\"\n                  />\n                  \n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <AggregationSelector\n                      value={aggFunction}\n                      onChange={setAggFunction}\n                      disabled={!values && aggFunction !== 'COUNT'}\n                    />\n                    \n                    <ChartTypeSelector\n                      value={chartType}\n                      onChange={setChartType}\n                    />\n                  </div>\n                  \n                  <div className=\"mt-6 flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-4\">\n                      <div className=\"flex items-center space-x-3\">\n                        <PrimaryButton\n                          onClick={generateVisualization}\n                          disabled={!canGenerate || loading}\n                          loading={loading}\n                          icon={loading ? null : \"🚀\"}\n                          className=\"!py-3 !px-6\"\n                        >\n                          {loading ? 'Génération...' : 'Générer la visualisation'}\n                        </PrimaryButton>\n\n                        {isMultiTable() && (\n                          <div className=\"flex items-center space-x-2 px-3 py-2 bg-blue-600/20 border border-blue-500 rounded-lg\">\n                            <span className=\"text-blue-400\">🔗</span>\n                            <span className=\"text-sm text-blue-300 font-medium\">Analyse multi-tables</span>\n                          </div>\n                        )}\n\n                        {activeFiltersCount > 0 && (\n                          <div className=\"flex items-center space-x-2 px-3 py-2 bg-green-600/20 border border-green-500 rounded-lg\">\n                            <span className=\"text-green-400\">🧩</span>\n                            <span className=\"text-sm text-green-300 font-medium\">\n                              {activeFiltersCount} filtre{activeFiltersCount > 1 ? 's' : ''} actif{activeFiltersCount > 1 ? 's' : ''}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                      \n                      {chartData && (\n                        <SecondaryButton\n                          onClick={() => setShowDebugJson(!showDebugJson)}\n                          icon=\"🔍\"\n                          className=\"!py-3 !px-4\"\n                        >\n                          {showDebugJson ? 'Masquer JSON' : 'Voir JSON'}\n                        </SecondaryButton>\n                      )}\n                    </div>\n                    \n                    {!canGenerate && selectedTable && (\n                      <p className=\"text-gray-500 text-sm\">\n                        Glissez au moins une colonne dans Axe X ou Valeurs\n                      </p>\n                    )}\n                  </div>\n                </DarkCard>\n\n                {/* Affichage du graphique */}\n                <DarkCard>\n                  {!chartData ? (\n                    <div className=\"flex flex-col items-center justify-center py-16 text-center\">\n                      <div className=\"w-24 h-24 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl mb-6 flex items-center justify-center\">\n                        <span className=\"text-4xl\">🎯</span>\n                      </div>\n                      <h3 className=\"text-xl font-semibold text-gray-100 mb-4\">\n                        Prêt à créer votre visualisation\n                      </h3>\n                      <p className=\"text-gray-400 max-w-md\">\n                        Glissez vos colonnes dans les zones appropriées et cliquez sur \"Générer\" pour créer votre graphique\n                      </p>\n                    </div>\n                  ) : (\n                    <div>\n                      <SectionHeader\n                        title=\"Visualisation générée\"\n                        subtitle={`${chartData.type.toUpperCase()} - ${chartData.data_points} points de données`}\n                        icon=\"📊\"\n                      />\n                      \n                      <div className=\"mt-6\">\n                        {renderChart()}\n                      </div>\n                      \n                      {chartData.success && (\n                        <div className=\"mt-4 p-3 bg-green-900/20 border border-green-700 rounded-lg\">\n                          <p className=\"text-green-400 text-sm\">\n                            ✅ {chartData.message}\n                          </p>\n                        </div>\n                      )}\n                      \n                      {/* Debug JSON */}\n                      {showDebugJson && (\n                        <div className=\"mt-4 p-4 bg-gray-900 border border-gray-700 rounded-lg\">\n                          <h4 className=\"text-sm font-medium text-gray-300 mb-2\">JSON de réponse :</h4>\n                          <pre className=\"text-xs text-gray-400 overflow-x-auto\">\n                            {JSON.stringify(chartData, null, 2)}\n                          </pre>\n                        </div>\n                      )}\n                    </div>\n                  )}\n                  \n                  {error && (\n                    <div className=\"mt-4 p-4 bg-red-900/20 border border-red-700 rounded-lg\">\n                      <p className=\"text-red-400\">\n                        ❌ {error}\n                      </p>\n                    </div>\n                  )}\n                </DarkCard>\n\n                {/* Section Narration intelligente */}\n                <div className=\"mt-8 space-y-6\">\n                  <NarrationSettings\n                    onGenerate={generateNarrative}\n                    loading={narrativeLoading}\n                  />\n\n                  {narrativeError && (\n                    <DarkCard className=\"border-red-500\">\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center space-x-3 text-red-400\">\n                          <span className=\"text-xl\">⚠️</span>\n                          <div>\n                            <p className=\"font-medium\">Erreur de génération de narration</p>\n                            <p className=\"text-sm text-red-300\">\n                              La génération automatique de l'histoire des données a échoué\n                            </p>\n                          </div>\n                        </div>\n\n                        <div className=\"bg-red-900/20 p-4 rounded border border-red-700\">\n                          <pre className=\"text-sm text-red-300 whitespace-pre-wrap font-mono leading-relaxed\">\n                            {narrativeError}\n                          </pre>\n                        </div>\n\n                        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-3\">\n                          <SecondaryButton\n                            onClick={() => generateNarrative({ focus: null, time_period: '12_months', include_recommendations: true })}\n                            icon=\"🔄\"\n                            className=\"!py-2 !px-3 !text-sm\"\n                            disabled={narrativeLoading}\n                          >\n                            Réessayer\n                          </SecondaryButton>\n                          <SecondaryButton\n                            onClick={() => generateNarrative({ focus: null, time_period: '6_months', include_recommendations: false })}\n                            icon=\"⚡\"\n                            className=\"!py-2 !px-3 !text-sm\"\n                            disabled={narrativeLoading}\n                          >\n                            Mode simplifié\n                          </SecondaryButton>\n                          <SecondaryButton\n                            onClick={() => window.open('http://localhost:8000/health', '_blank')}\n                            icon=\"🔍\"\n                            className=\"!py-2 !px-3 !text-sm !text-blue-400 !border-blue-600\"\n                          >\n                            Test backend\n                          </SecondaryButton>\n                          <SecondaryButton\n                            onClick={() => window.open('http://localhost:8000/narrate/test', '_blank')}\n                            icon=\"🧪\"\n                            className=\"!py-2 !px-3 !text-sm !text-green-400 !border-green-600\"\n                          >\n                            Test narration\n                          </SecondaryButton>\n                          <SecondaryButton\n                            onClick={() => setShowNetworkDiagnostic(true)}\n                            icon=\"🔧\"\n                            className=\"!py-2 !px-3 !text-sm !text-purple-400 !border-purple-600\"\n                          >\n                            Diagnostic réseau\n                          </SecondaryButton>\n                        </div>\n\n                        {debugInfo?.error_type === 'connection' && (\n                          <div className=\"bg-yellow-900/20 p-3 rounded border border-yellow-700\">\n                            <div className=\"flex items-start space-x-2\">\n                              <span className=\"text-yellow-400 mt-1\">💡</span>\n                              <div className=\"text-sm text-yellow-300\">\n                                <p className=\"font-medium mb-2\">Problème de connexion détecté</p>\n                                <ul className=\"space-y-1 text-xs\">\n                                  <li>• Vérifiez que le backend est démarré sur le port 8000</li>\n                                  <li>• Testez l'URL : <code className=\"bg-yellow-800/30 px-1 rounded\">http://localhost:8000</code></li>\n                                  <li>• Consultez les logs du serveur backend</li>\n                                </ul>\n                              </div>\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    </DarkCard>\n                  )}\n\n                  {/* Debug info pour diagnostiquer les problèmes */}\n                  {debugInfo && (debugInfo.status === 'error' || process.env.NODE_ENV === 'development') && (\n                    <DarkCard className=\"border-yellow-500\">\n                      <div className=\"space-y-3\">\n                        <div className=\"flex items-center space-x-3 text-yellow-400\">\n                          <span className=\"text-lg\">🔧</span>\n                          <h4 className=\"font-medium\">Informations de debug</h4>\n                        </div>\n\n                        <div className=\"bg-yellow-900/20 p-3 rounded border border-yellow-700\">\n                          <pre className=\"text-xs text-yellow-300 whitespace-pre-wrap font-mono\">\n                            {JSON.stringify(debugInfo, null, 2)}\n                          </pre>\n                        </div>\n                      </div>\n                    </DarkCard>\n                  )}\n\n                  {/* Diagnostic réseau */}\n                  {showNetworkDiagnostic && (\n                    <NetworkDiagnostic\n                      onClose={() => setShowNetworkDiagnostic(false)}\n                    />\n                  )}\n\n                  <DataStorytellingPanel\n                    narrative={narrative}\n                    dataSummary={narrative?.data_summary || {}}\n                    onCopy={copyToClipboard}\n                    onExport={exportNarrative}\n                  />\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Barre fixe avec zones de drop */}\n      <FixedDropBar\n        xAxis={xAxis}\n        yAxis={yAxis}\n        legend={legend}\n        values={values}\n        onClearX={() => setXAxis(null)}\n        onClearY={() => setYAxis(null)}\n        onClearLegend={() => setLegend(null)}\n        onClearValues={() => setValues(null)}\n        isVisible={showFixedDropBar}\n        position=\"bottom\"\n      />\n\n      {/* Drag Overlay */}\n      <DragOverlay>\n        {activeId ? (\n          <div className=\"transform rotate-3 scale-105\">\n            <DraggableColumn\n              id={activeId}\n              column={{ name: 'Glissement...', type: 'unknown' }}\n            />\n          </div>\n        ) : null}\n      </DragOverlay>\n    </DndContext>\n  );\n};\n\nexport default DragVisualPage;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,UAAU,EAAEC,WAAW,EAAEC,aAAa,QAAQ,eAAe;AACtE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SACEC,aAAa,EACbC,QAAQ,EACRC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,WAAW,EACXC,SAAS,QACJ,gBAAgB;AACvB,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,mBAAmB,EACnBC,iBAAiB,EACjBC,eAAe,QACV,sBAAsB;AAC7B,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SACEC,iBAAiB,EACjBC,qBAAqB,EACrBC,iBAAiB,EACjBC,mBAAmB,QACd,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,YAAY,GAAG,uBAAuB;AAE5C,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC8B,MAAM,EAAEC,SAAS,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACoC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAM,CAACsC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACwC,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4C,MAAM,EAAEC,SAAS,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC8C,MAAM,EAAEC,SAAS,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkD,SAAS,EAAEC,YAAY,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC0D,KAAK,EAAEC,QAAQ,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4D,QAAQ,EAAEC,WAAW,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC8D,aAAa,EAAEC,gBAAgB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACgE,OAAO,EAAEC,UAAU,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACkE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACoE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACsE,WAAW,EAAEC,cAAc,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACwE,WAAW,EAAEC,cAAc,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAM;IACJ0E,SAAS;IACTtB,OAAO,EAAEuB,gBAAgB;IACzBjB,KAAK,EAAEkB,cAAc;IACrBC,SAAS;IACTC,iBAAiB;IACjBC,eAAe;IACfC;EACF,CAAC,GAAGvD,mBAAmB,CAAC,CAAC;;EAEzB;EACAxB,SAAS,CAAC,MAAM;IACdgF,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAhF,SAAS,CAAC,MAAM;IACd,MAAMiF,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,cAAc,GAAGC,MAAM,CAACC,OAAO;MAErC,IAAIb,WAAW,EAAE;QACf,IAAIW,cAAc,GAAGb,WAAW,IAAIa,cAAc,GAAG,GAAG,EAAE;UACxD;UACAd,mBAAmB,CAAC,KAAK,CAAC;QAC5B,CAAC,MAAM,IAAIc,cAAc,GAAGb,WAAW,EAAE;UACvC;UACAD,mBAAmB,CAAC,IAAI,CAAC;QAC3B;MACF;MAEAE,cAAc,CAACY,cAAc,CAAC;IAChC,CAAC;IAED,IAAIX,WAAW,EAAE;MACfY,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEJ,YAAY,EAAE;QAAEK,OAAO,EAAE;MAAK,CAAC,CAAC;MAClE,OAAO,MAAMH,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAEN,YAAY,CAAC;IACjE;EACF,CAAC,EAAE,CAACZ,WAAW,EAAEE,WAAW,CAAC,CAAC;;EAE9B;EACAvE,SAAS,CAAC,MAAM;IACd,IAAIqC,aAAa,EAAE;MACjBmD,WAAW,CAACnD,aAAa,CAAC;MAC1B;MACA;MACAD,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACC,aAAa,CAAC,CAAC;EAEnB,MAAM2C,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF1B,gBAAgB,CAAC,IAAI,CAAC;MACtBI,QAAQ,CAAC,EAAE,CAAC;MACZ,MAAM+B,QAAQ,GAAG,MAAMpF,KAAK,CAACqF,GAAG,CAAC,GAAG/D,YAAY,gBAAgB,CAAC;MACjEK,SAAS,CAACyD,QAAQ,CAACE,IAAI,CAAC5D,MAAM,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAO6D,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZpC,QAAQ,CAAC,wCAAwC,IAAI,EAAAmC,aAAA,GAAAD,GAAG,CAACH,QAAQ,cAAAI,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcF,IAAI,cAAAG,kBAAA,uBAAlBA,kBAAA,CAAoBC,MAAM,KAAIH,GAAG,CAACI,OAAO,CAAC,CAAC;IAClG,CAAC,SAAS;MACR1C,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAMkC,WAAW,GAAG,MAAOS,SAAS,IAAK;IACvC,IAAI;MACFzC,iBAAiB,CAAC,IAAI,CAAC;MACvB,MAAMiC,QAAQ,GAAG,MAAMpF,KAAK,CAACqF,GAAG,CAAC,GAAG/D,YAAY,WAAWsE,SAAS,UAAU,CAAC;MAC/E/D,UAAU,CAACuD,QAAQ,CAACE,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAM,cAAA,EAAAC,mBAAA;MACZzC,QAAQ,CAAC,0CAA0C,IAAI,EAAAwC,cAAA,GAAAN,GAAG,CAACH,QAAQ,cAAAS,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcP,IAAI,cAAAQ,mBAAA,uBAAlBA,mBAAA,CAAoBJ,MAAM,KAAIH,GAAG,CAACI,OAAO,CAAC,CAAC;IACpG,CAAC,SAAS;MACRxC,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAM4C,eAAe,GAAIC,KAAK,IAAK;IACjCzC,WAAW,CAACyC,KAAK,CAACC,MAAM,CAACC,EAAE,CAAC;EAC9B,CAAC;EAED,MAAMC,aAAa,GAAIH,KAAK,IAAK;IAC/B,MAAM;MAAEC,MAAM;MAAEG;IAAK,CAAC,GAAGJ,KAAK;IAC9BzC,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI,CAAC6C,IAAI,EAAE;IAEX,MAAMC,QAAQ,GAAGJ,MAAM,CAACC,EAAE;IAC1B,MAAMI,UAAU,GAAGF,IAAI,CAACF,EAAE;;IAE1B;IACA,MAAMK,UAAU,GAAG,CACjB,IAAI3E,OAAO,CAAC4E,WAAW,IAAI,EAAE,CAAC,EAC9B,IAAI5E,OAAO,CAAC6E,eAAe,IAAI,EAAE,CAAC,EAClC,IAAI7E,OAAO,CAAC8E,mBAAmB,IAAI,EAAE,CAAC,EACtC,IAAI9E,OAAO,CAAC+E,YAAY,IAAI,EAAE,CAAC,CAChC;IAED,MAAMC,MAAM,GAAGL,UAAU,CAACM,IAAI,CAACC,GAAG,IAAI,UAAUA,GAAG,CAACC,IAAI,EAAE,KAAKV,QAAQ,CAAC;IACxE,IAAI,CAACO,MAAM,EAAE;;IAEb;IACA,MAAMI,eAAe,GAAG;MAAE,GAAGJ,MAAM;MAAEK,KAAK,EAAEjF;IAAc,CAAC;;IAE3D;IACAkF,kBAAkB,CAACZ,UAAU,EAAEU,eAAe,CAAC;EACjD,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAGA,CAACC,MAAM,EAAEH,eAAe,KAAK;IACtD,QAAQG,MAAM;MACZ,KAAK,QAAQ;MACb,KAAK,cAAc;QACjBhF,QAAQ,CAAC6E,eAAe,CAAC;QACzB;MACF,KAAK,QAAQ;MACb,KAAK,cAAc;QACjB3E,QAAQ,CAAC2E,eAAe,CAAC;QACzB;MACF,KAAK,QAAQ;MACb,KAAK,cAAc;QACjBzE,SAAS,CAACyE,eAAe,CAAC;QAC1B;MACF,KAAK,QAAQ;MACb,KAAK,cAAc;QACjBvE,SAAS,CAACuE,eAAe,CAAC;QAC1B;MACF;QACE;IACJ;EACF,CAAC;;EAED;EACA,MAAMI,eAAe,GAAGA,CAACD,MAAM,EAAEH,eAAe,KAAK;IACnDE,kBAAkB,CAACC,MAAM,EAAEH,eAAe,CAAC;EAC7C,CAAC;EAED,MAAMK,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAACrF,aAAa,EAAE;MAClBqB,QAAQ,CAAC,iCAAiC,CAAC;MAC3C;IACF;IAEA,IAAI,CAACnB,KAAK,IAAI,CAACM,MAAM,EAAE;MACrBa,QAAQ,CAAC,6DAA6D,CAAC;MACvE;IACF;IAEA,IAAI;MACFN,UAAU,CAAC,IAAI,CAAC;MAChBM,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMiE,WAAW,GAAG;QAClBL,KAAK,EAAEjF,aAAa;QAAE;QACtBuF,MAAM,EAAE,CAAArF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE6E,IAAI,KAAI,IAAI;QAC3BS,MAAM,EAAE,CAAApF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE2E,IAAI,KAAI,IAAI;QAC3BzE,MAAM,EAAE,CAAAA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEyE,IAAI,KAAI,IAAI;QAC5BvE,MAAM,EAAE,CAAAA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEuE,IAAI,KAAI,IAAI;QAC5BU,YAAY,EAAE/E,WAAW;QACzBgF,UAAU,EAAE9E,SAAS;QACrB;QACA+E,YAAY,EAAE,CAAAzF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE+E,KAAK,KAAI,IAAI;QAClCW,YAAY,EAAE,CAAAxF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE6E,KAAK,KAAI,IAAI;QAClCY,YAAY,EAAE,CAAAvF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE2E,KAAK,KAAI,IAAI;QACnCa,YAAY,EAAE,CAAAtF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEyE,KAAK,KAAI,IAAI;QACnC;QACAvD,OAAO,EAAEA,OAAO,CAACqE,MAAM,CAACC,CAAC,IACtBA,CAAC,CAACC,WAAW,KAAK,OAAO,KAAKD,CAAC,CAACE,SAAS,IAAIF,CAAC,CAACG,SAAS,CAAC,IACzDH,CAAC,CAACC,WAAW,KAAK,OAAO,IAAID,CAAC,CAACxF,MAAM,IAAIwF,CAAC,CAACxF,MAAM,CAAC4F,MAAM,GAAG,CAC9D,CAAC,CAACC,GAAG,CAACL,CAAC,KAAK;UACVM,WAAW,EAAEN,CAAC,CAACM,WAAW;UAC1BC,UAAU,EAAEP,CAAC,CAACO,UAAU;UACxBN,WAAW,EAAED,CAAC,CAACC,WAAW;UAC1BzF,MAAM,EAAEwF,CAAC,CAACxF,MAAM,IAAI,EAAE;UACtB0F,SAAS,EAAEF,CAAC,CAACE,SAAS,IAAI,IAAI;UAC9BC,SAAS,EAAEH,CAAC,CAACG,SAAS,IAAI;QAC5B,CAAC,CAAC;MACJ,CAAC;MAED,MAAM/C,QAAQ,GAAG,MAAMpF,KAAK,CAACwI,IAAI,CAAC,GAAGlH,YAAY,cAAc,EAAEgG,WAAW,CAAC;MAC7EvF,YAAY,CAACqD,QAAQ,CAACE,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAkD,cAAA,EAAAC,mBAAA;MACZrF,QAAQ,CAAC,gCAAgC,IAAI,EAAAoF,cAAA,GAAAlD,GAAG,CAACH,QAAQ,cAAAqD,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcnD,IAAI,cAAAoD,mBAAA,uBAAlBA,mBAAA,CAAoBhD,MAAM,KAAIH,GAAG,CAACI,OAAO,CAAC,CAAC;IAC1F,CAAC,SAAS;MACR5C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4F,QAAQ,GAAGA,CAAA,KAAM;IACrBxG,QAAQ,CAAC,IAAI,CAAC;IACdE,QAAQ,CAAC,IAAI,CAAC;IACdE,SAAS,CAAC,IAAI,CAAC;IACfE,SAAS,CAAC,IAAI,CAAC;IACfV,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAM6G,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAAC9G,SAAS,EAAE,OAAO,IAAI;IAE3B,MAAM+G,QAAQ,GAAG,EAAE;;IAEnB;IACA,IAAIC,KAAK,GAAGhH,SAAS,CAACgH,KAAK;IAC3B,IAAIC,kBAAkB,GAAG,CAAC,EAAE;MAC1BD,KAAK,IAAI,KAAKC,kBAAkB,UAAUA,kBAAkB,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,YAAYA,kBAAkB,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG;IAC7H;IAEA,MAAMC,MAAM,GAAG;MACbF,KAAK,EAAEA,KAAK;MACZG,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,GAAG;MACXC,MAAM,EAAE;QAAEC,CAAC,EAAE,EAAE;QAAEC,CAAC,EAAE,EAAE;QAAEC,CAAC,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAG,CAAC;MACtCC,aAAa,EAAE,eAAe;MAC9BC,YAAY,EAAE,eAAe;MAC7BC,IAAI,EAAE;QAAEC,KAAK,EAAE;MAAU;IAC3B,CAAC;IAED,QAAQ7H,SAAS,CAAC8H,IAAI;MACpB,KAAK,KAAK;QACR,IAAI9H,SAAS,CAACQ,MAAM,IAAIuH,KAAK,CAACC,OAAO,CAAChI,SAAS,CAACiI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACrD;UACAjI,SAAS,CAACQ,MAAM,CAAC0H,OAAO,CAAC,CAACC,UAAU,EAAEC,KAAK,KAAK;YAC9CrB,QAAQ,CAACsB,IAAI,CAAC;cACZC,CAAC,EAAEtI,SAAS,CAACsI,CAAC;cACdL,CAAC,EAAEjI,SAAS,CAACiI,CAAC,CAAC1B,GAAG,CAACgC,GAAG,IAAIA,GAAG,CAACH,KAAK,CAAC,IAAI,CAAC,CAAC;cAC1CN,IAAI,EAAE,KAAK;cACX7C,IAAI,EAAEkD,UAAU;cAChBK,MAAM,EAAE;gBACNX,KAAK,EAAE,OAAQO,KAAK,GAAG,EAAE,GAAI,GAAG;cAClC;YACF,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,MAAM;UACLrB,QAAQ,CAACsB,IAAI,CAAC;YACZC,CAAC,EAAEtI,SAAS,CAACsI,CAAC;YACdL,CAAC,EAAEjI,SAAS,CAACiI,CAAC;YACdH,IAAI,EAAE,KAAK;YACXU,MAAM,EAAE;cAAEX,KAAK,EAAE;YAAU;UAC7B,CAAC,CAAC;QACJ;QACAX,MAAM,CAACuB,KAAK,GAAG;UAAEzB,KAAK,EAAEhH,SAAS,CAAC0I;QAAO,CAAC;QAC1CxB,MAAM,CAACyB,KAAK,GAAG;UAAE3B,KAAK,EAAEhH,SAAS,CAAC4I;QAAO,CAAC;QAC1C;MAEF,KAAK,MAAM;QACT7B,QAAQ,CAACsB,IAAI,CAAC;UACZC,CAAC,EAAEtI,SAAS,CAACsI,CAAC;UACdL,CAAC,EAAEjI,SAAS,CAACiI,CAAC;UACdH,IAAI,EAAE,SAAS;UACfe,IAAI,EAAE,eAAe;UACrBC,IAAI,EAAE;YAAEjB,KAAK,EAAE,SAAS;YAAEkB,KAAK,EAAE;UAAE,CAAC;UACpCP,MAAM,EAAE;YAAEX,KAAK,EAAE,SAAS;YAAEmB,IAAI,EAAE;UAAE;QACtC,CAAC,CAAC;QACF9B,MAAM,CAACuB,KAAK,GAAG;UAAEzB,KAAK,EAAEhH,SAAS,CAAC0I;QAAO,CAAC;QAC1CxB,MAAM,CAACyB,KAAK,GAAG;UAAE3B,KAAK,EAAEhH,SAAS,CAAC4I;QAAO,CAAC;QAC1C;MAEF,KAAK,KAAK;QACR7B,QAAQ,CAACsB,IAAI,CAAC;UACZY,MAAM,EAAEjJ,SAAS,CAACsI,CAAC;UACnB5H,MAAM,EAAEV,SAAS,CAACiI,CAAC;UACnBH,IAAI,EAAE,KAAK;UACXU,MAAM,EAAE;YACNU,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;UACtF,CAAC;UACDC,QAAQ,EAAE,eAAe;UACzBC,YAAY,EAAE;QAChB,CAAC,CAAC;QACF;MAEF,KAAK,SAAS;QACZrC,QAAQ,CAACsB,IAAI,CAAC;UACZC,CAAC,EAAEtI,SAAS,CAACsI,CAAC;UACdL,CAAC,EAAEjI,SAAS,CAACiI,CAAC;UACdH,IAAI,EAAE,SAAS;UACfe,IAAI,EAAE,SAAS;UACfL,MAAM,EAAE;YACNX,KAAK,EAAE,SAAS;YAChBmB,IAAI,EAAE,EAAE;YACRF,IAAI,EAAE;cAAEjB,KAAK,EAAE,SAAS;cAAEkB,KAAK,EAAE;YAAE;UACrC;QACF,CAAC,CAAC;QACF7B,MAAM,CAACuB,KAAK,GAAG;UAAEzB,KAAK,EAAEhH,SAAS,CAAC0I;QAAO,CAAC;QAC1CxB,MAAM,CAACyB,KAAK,GAAG;UAAE3B,KAAK,EAAEhH,SAAS,CAAC4I;QAAO,CAAC;QAC1C;MAEF;QACE,oBAAOrJ,OAAA;UAAG8J,SAAS,EAAC,cAAc;UAAAC,QAAA,GAAC,qCAAgC,EAACtJ,SAAS,CAAC8H,IAAI;QAAA;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;IAC3F;IAEA,oBACEnK,OAAA,CAACpB,IAAI;MACHqF,IAAI,EAAEuD,QAAS;MACfG,MAAM,EAAEA,MAAO;MACfyC,MAAM,EAAE;QAAEC,UAAU,EAAE,IAAI;QAAEC,cAAc,EAAE;MAAK,CAAE;MACnDC,KAAK,EAAE;QAAEf,KAAK,EAAE;MAAO;IAAE;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAEN,CAAC;EAED,MAAMK,WAAW,GAAG7J,aAAa,KAAKE,KAAK,IAAIM,MAAM,CAAC;;EAEtD;EACA,MAAMsJ,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMpK,MAAM,GAAG,IAAIqK,GAAG,CAAC,CAAC;IACxB,CAAC7J,KAAK,EAAEE,KAAK,EAAEE,MAAM,EAAEE,MAAM,CAAC,CAACwH,OAAO,CAAClD,GAAG,IAAI;MAC5C,IAAIA,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEG,KAAK,EAAEvF,MAAM,CAACsK,GAAG,CAAClF,GAAG,CAACG,KAAK,CAAC;IACvC,CAAC,CAAC;IACF,OAAOvF,MAAM,CAACoJ,IAAI,GAAG,CAAC;EACxB,CAAC;;EAED;EACA,MAAM/B,kBAAkB,GAAGrF,OAAO,CAACqE,MAAM,CAACC,CAAC,IACxCA,CAAC,CAACC,WAAW,KAAK,OAAO,KAAKD,CAAC,CAACE,SAAS,IAAIF,CAAC,CAACG,SAAS,CAAC,IACzDH,CAAC,CAACC,WAAW,KAAK,OAAO,IAAID,CAAC,CAACxF,MAAM,IAAIwF,CAAC,CAACxF,MAAM,CAAC4F,MAAM,GAAG,CAC9D,CAAC,CAACA,MAAM;EAER,oBACE/G,OAAA,CAACxB,UAAU;IACToM,kBAAkB,EAAElM,aAAc;IAClCmM,WAAW,EAAEnG,eAAgB;IAC7BoG,SAAS,EAAEhG,aAAc;IAAAiF,QAAA,gBAEzB/J,OAAA;MAAK8J,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrD/J,OAAA,CAACnB,aAAa;QAACkM,OAAO,EAAE;MAAK;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEhCnK,OAAA;QAAK8J,SAAS,EAAE,+BAA+BrH,gBAAgB,GAAG,OAAO,GAAG,MAAM,EAAG;QAAAsH,QAAA,gBAEnF/J,OAAA;UAAK8J,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB/J,OAAA;YAAK8J,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD/J,OAAA;cAAA+J,QAAA,gBACE/J,OAAA;gBAAK8J,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/C/J,OAAA;kBAAK8J,SAAS,EAAC,oGAAoG;kBAAAC,QAAA,eACjH/J,OAAA;oBAAM8J,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eACNnK,OAAA;kBAAI8J,SAAS,EAAC,+FAA+F;kBAAAC,QAAA,EAAC;gBAE9G;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNnK,OAAA;gBAAG8J,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAE7B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJnK,OAAA;gBAAG8J,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAE5C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJnK,OAAA;gBAAG8J,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNnK,OAAA;cAAK8J,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B/J,OAAA,CAACf,eAAe;gBACd+L,OAAO,EAAEA,CAAA,KAAMtI,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;gBACtDwI,IAAI,EAAExI,gBAAgB,GAAG,KAAK,GAAG,SAAU;gBAC3CqH,SAAS,EAAErH,gBAAgB,GAAG,qCAAqC,GAAG,EAAG;gBAAAsH,QAAA,EAC1E;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAClBnK,OAAA,CAACf,eAAe;gBAAC+L,OAAO,EAAEA,CAAA,KAAM5K,QAAQ,CAAC,iBAAiB,CAAE;gBAAC6K,IAAI,EAAC,cAAI;gBAAAlB,QAAA,EAAC;cAEvE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAClBnK,OAAA,CAACf,eAAe;gBAAC+L,OAAO,EAAEA,CAAA,KAAM5K,QAAQ,CAAC,SAAS,CAAE;gBAAC6K,IAAI,EAAC,cAAI;gBAAAlB,QAAA,EAAC;cAE/D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAClBnK,OAAA,CAACf,eAAe;gBAAC+L,OAAO,EAAEA,CAAA,KAAM5K,QAAQ,CAAC,GAAG,CAAE;gBAAC6K,IAAI,EAAC,cAAI;gBAAAlB,QAAA,EAAC;cAEzD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnK,OAAA;UAAK8J,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB/J,OAAA,CAAClB,QAAQ;YAAAiL,QAAA,gBACP/J,OAAA,CAACjB,aAAa;cACZ0I,KAAK,EAAC,6BAA0B;cAChCyD,QAAQ,EAAC,oCAAiC;cAC1CD,IAAI,EAAC;YAAK;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,EAEDxI,aAAa,gBACZ3B,OAAA;cAAK8J,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD/J,OAAA,CAACd,WAAW;gBAAA8K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACfnK,OAAA;gBAAM8J,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,gBAENnK,OAAA;cACEmL,KAAK,EAAExK,aAAc;cACrByK,QAAQ,EAAGC,CAAC,IAAKzK,gBAAgB,CAACyK,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAClDrB,SAAS,EAAC,mHAAmH;cAAAC,QAAA,gBAE7H/J,OAAA;gBAAQmL,KAAK,EAAC,EAAE;gBAAApB,QAAA,EAAC;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACrD9J,MAAM,CAAC2G,GAAG,CAAC,CAACpB,KAAK,EAAEiD,KAAK,kBACvB7I,OAAA;gBAAoBmL,KAAK,EAAEvF,KAAK,CAAC2F,UAAW;gBAAAxB,QAAA,GACzCnE,KAAK,CAAC2F,UAAU,EAAC,IAAE,EAAC3F,KAAK,CAAC4F,YAAY,EAAC,YAC1C;cAAA,GAFa3C,KAAK;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAELxJ,aAAa,iBACZX,OAAA;UAAK8J,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAEpD/J,OAAA;YAAK8J,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B/J,OAAA,CAAClB,QAAQ;cAACgL,SAAS,EAAC,OAAO;cAAAC,QAAA,EACxBlI,cAAc,gBACb7B,OAAA;gBAAK8J,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD/J,OAAA,CAACd,WAAW;kBAAA8K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACfnK,OAAA;kBAAM8J,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,gBAENnK,OAAA,CAACZ,YAAY;gBACXmB,OAAO,EAAEA,OAAO,CAAC4E,WAAW,IAAI,EAAG;gBACnCsC,KAAK,EAAC,sBAAsB;gBAC5BgE,WAAW,EAAE1F,eAAgB;gBAC7BpF,aAAa,EAAEA;cAAc;gBAAAqJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAGNnK,OAAA;YAAK8J,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBAEtC/J,OAAA,CAACN,cAAc;cACb2C,OAAO,EAAEA,OAAQ;cACjBqJ,eAAe,EAAEpJ;YAAW;cAAA0H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eAGFnK,OAAA,CAAClB,QAAQ;cAAAiL,QAAA,gBACP/J,OAAA;gBAAK8J,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD/J,OAAA,CAACjB,aAAa;kBACZ0I,KAAK,EAAC,wBAAwB;kBAC9ByD,QAAQ,EAAC,oDAAiD;kBAC1DD,IAAI,EAAC;gBAAI;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACFnK,OAAA;kBAAK8J,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B/J,OAAA,CAACf,eAAe;oBACd+L,OAAO,EAAEA,CAAA,KAAM7H,iBAAiB,CAAC;sBAAEwI,KAAK,EAAE,IAAI;sBAAEC,WAAW,EAAE,WAAW;sBAAEC,uBAAuB,EAAE;oBAAK,CAAC,CAAE;oBAC3GZ,IAAI,EAAC,cAAI;oBACTa,QAAQ,EAAE9I,gBAAiB;oBAC3B8G,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,EAEhE/G,gBAAgB,GAAG,eAAe,GAAG;kBAAkB;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC,eAClBnK,OAAA,CAACf,eAAe;oBAAC+L,OAAO,EAAE1D,QAAS;oBAAC2D,IAAI,EAAC,oBAAK;oBAACnB,SAAS,EAAC,oDAAoD;oBAAAC,QAAA,EAAC;kBAE9G;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAiB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENnK,OAAA,CAACX,cAAc;gBACbwB,KAAK,EAAEA,KAAM;gBACbE,KAAK,EAAEA,KAAM;gBACbE,MAAM,EAAEA,MAAO;gBACfE,MAAM,EAAEA,MAAO;gBACf4K,QAAQ,EAAEA,CAAA,KAAMjL,QAAQ,CAAC,IAAI,CAAE;gBAC/BkL,QAAQ,EAAEA,CAAA,KAAMhL,QAAQ,CAAC,IAAI,CAAE;gBAC/BiL,aAAa,EAAEA,CAAA,KAAM/K,SAAS,CAAC,IAAI,CAAE;gBACrCgL,aAAa,EAAEA,CAAA,KAAM9K,SAAS,CAAC,IAAI;cAAE;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eAGXnK,OAAA,CAAClB,QAAQ;cAAAiL,QAAA,gBACP/J,OAAA,CAACjB,aAAa;gBACZ0I,KAAK,EAAC,kBAAkB;gBACxByD,QAAQ,EAAC,oDAAiD;gBAC1DD,IAAI,EAAC;cAAI;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAEFnK,OAAA;gBAAK8J,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD/J,OAAA,CAACT,mBAAmB;kBAClB4L,KAAK,EAAE9J,WAAY;kBACnB+J,QAAQ,EAAE9J,cAAe;kBACzBwK,QAAQ,EAAE,CAAC3K,MAAM,IAAIE,WAAW,KAAK;gBAAQ;kBAAA2I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eAEFnK,OAAA,CAACR,iBAAiB;kBAChB2L,KAAK,EAAE5J,SAAU;kBACjB6J,QAAQ,EAAE5J;gBAAa;kBAAAwI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENnK,OAAA;gBAAK8J,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD/J,OAAA;kBAAK8J,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C/J,OAAA;oBAAK8J,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C/J,OAAA,CAAChB,aAAa;sBACZgM,OAAO,EAAEhF,qBAAsB;sBAC/B8F,QAAQ,EAAE,CAACtB,WAAW,IAAI/I,OAAQ;sBAClCA,OAAO,EAAEA,OAAQ;sBACjBwJ,IAAI,EAAExJ,OAAO,GAAG,IAAI,GAAG,IAAK;sBAC5BqI,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAEtBtI,OAAO,GAAG,eAAe,GAAG;oBAA0B;sBAAAuI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C,CAAC,EAEfM,YAAY,CAAC,CAAC,iBACbzK,OAAA;sBAAK8J,SAAS,EAAC,wFAAwF;sBAAAC,QAAA,gBACrG/J,OAAA;wBAAM8J,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACzCnK,OAAA;wBAAM8J,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5E,CACN,EAEAzC,kBAAkB,GAAG,CAAC,iBACrB1H,OAAA;sBAAK8J,SAAS,EAAC,0FAA0F;sBAAAC,QAAA,gBACvG/J,OAAA;wBAAM8J,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC1CnK,OAAA;wBAAM8J,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,GACjDrC,kBAAkB,EAAC,SAAO,EAACA,kBAAkB,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,QAAM,EAACA,kBAAkB,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;sBAAA;wBAAAsC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,EAEL1J,SAAS,iBACRT,OAAA,CAACf,eAAe;oBACd+L,OAAO,EAAEA,CAAA,KAAM5I,gBAAgB,CAAC,CAACD,aAAa,CAAE;oBAChD8I,IAAI,EAAC,cAAI;oBACTnB,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAEtB5H,aAAa,GAAG,cAAc,GAAG;kBAAW;oBAAA6H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAClB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EAEL,CAACK,WAAW,IAAI7J,aAAa,iBAC5BX,OAAA;kBAAG8J,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAErC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGXnK,OAAA,CAAClB,QAAQ;cAAAiL,QAAA,GACN,CAACtJ,SAAS,gBACTT,OAAA;gBAAK8J,SAAS,EAAC,6DAA6D;gBAAAC,QAAA,gBAC1E/J,OAAA;kBAAK8J,SAAS,EAAC,0GAA0G;kBAAAC,QAAA,eACvH/J,OAAA;oBAAM8J,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACNnK,OAAA;kBAAI8J,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAEzD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnK,OAAA;kBAAG8J,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EAAC;gBAEtC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,gBAENnK,OAAA;gBAAA+J,QAAA,gBACE/J,OAAA,CAACjB,aAAa;kBACZ0I,KAAK,EAAC,gCAAuB;kBAC7ByD,QAAQ,EAAE,GAAGzK,SAAS,CAAC8H,IAAI,CAAC4D,WAAW,CAAC,CAAC,MAAM1L,SAAS,CAAC2L,WAAW,oBAAqB;kBACzFnB,IAAI,EAAC;gBAAI;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAEFnK,OAAA;kBAAK8J,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAClBxC,WAAW,CAAC;gBAAC;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,EAEL1J,SAAS,CAAC4L,OAAO,iBAChBrM,OAAA;kBAAK8J,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,eAC1E/J,OAAA;oBAAG8J,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,GAAC,SAClC,EAACtJ,SAAS,CAAC6D,OAAO;kBAAA;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CACN,EAGAhI,aAAa,iBACZnC,OAAA;kBAAK8J,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBACrE/J,OAAA;oBAAI8J,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7EnK,OAAA;oBAAK8J,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EACnDuC,IAAI,CAACC,SAAS,CAAC9L,SAAS,EAAE,IAAI,EAAE,CAAC;kBAAC;oBAAAuJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN,EAEApI,KAAK,iBACJ/B,OAAA;gBAAK8J,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,eACtE/J,OAAA;kBAAG8J,SAAS,EAAC,cAAc;kBAAAC,QAAA,GAAC,SACxB,EAAChI,KAAK;gBAAA;kBAAAiI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAGXnK,OAAA;cAAK8J,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B/J,OAAA,CAACL,iBAAiB;gBAChB6M,UAAU,EAAErJ,iBAAkB;gBAC9B1B,OAAO,EAAEuB;cAAiB;gBAAAgH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,EAEDlH,cAAc,iBACbjD,OAAA,CAAClB,QAAQ;gBAACgL,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAClC/J,OAAA;kBAAK8J,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxB/J,OAAA;oBAAK8J,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,gBACvD/J,OAAA;sBAAM8J,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnCnK,OAAA;sBAAA+J,QAAA,gBACE/J,OAAA;wBAAG8J,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAiC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAChEnK,OAAA;wBAAG8J,SAAS,EAAC,sBAAsB;wBAAAC,QAAA,EAAC;sBAEpC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENnK,OAAA;oBAAK8J,SAAS,EAAC,iDAAiD;oBAAAC,QAAA,eAC9D/J,OAAA;sBAAK8J,SAAS,EAAC,oEAAoE;sBAAAC,QAAA,EAChF9G;oBAAc;sBAAA+G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENnK,OAAA;oBAAK8J,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpD/J,OAAA,CAACf,eAAe;sBACd+L,OAAO,EAAEA,CAAA,KAAM7H,iBAAiB,CAAC;wBAAEwI,KAAK,EAAE,IAAI;wBAAEC,WAAW,EAAE,WAAW;wBAAEC,uBAAuB,EAAE;sBAAK,CAAC,CAAE;sBAC3GZ,IAAI,EAAC,cAAI;sBACTnB,SAAS,EAAC,sBAAsB;sBAChCgC,QAAQ,EAAE9I,gBAAiB;sBAAA+G,QAAA,EAC5B;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAiB,CAAC,eAClBnK,OAAA,CAACf,eAAe;sBACd+L,OAAO,EAAEA,CAAA,KAAM7H,iBAAiB,CAAC;wBAAEwI,KAAK,EAAE,IAAI;wBAAEC,WAAW,EAAE,UAAU;wBAAEC,uBAAuB,EAAE;sBAAM,CAAC,CAAE;sBAC3GZ,IAAI,EAAC,QAAG;sBACRnB,SAAS,EAAC,sBAAsB;sBAChCgC,QAAQ,EAAE9I,gBAAiB;sBAAA+G,QAAA,EAC5B;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAiB,CAAC,eAClBnK,OAAA,CAACf,eAAe;sBACd+L,OAAO,EAAEA,CAAA,KAAMvH,MAAM,CAACgJ,IAAI,CAAC,8BAA8B,EAAE,QAAQ,CAAE;sBACrExB,IAAI,EAAC,cAAI;sBACTnB,SAAS,EAAC,sDAAsD;sBAAAC,QAAA,EACjE;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAiB,CAAC,eAClBnK,OAAA,CAACf,eAAe;sBACd+L,OAAO,EAAEA,CAAA,KAAMvH,MAAM,CAACgJ,IAAI,CAAC,oCAAoC,EAAE,QAAQ,CAAE;sBAC3ExB,IAAI,EAAC,cAAI;sBACTnB,SAAS,EAAC,wDAAwD;sBAAAC,QAAA,EACnE;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAiB,CAAC,eAClBnK,OAAA,CAACf,eAAe;sBACd+L,OAAO,EAAEA,CAAA,KAAMxI,wBAAwB,CAAC,IAAI,CAAE;sBAC9CyI,IAAI,EAAC,cAAI;sBACTnB,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,EACrE;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAiB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,EAEL,CAAAjH,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEwJ,UAAU,MAAK,YAAY,iBACrC1M,OAAA;oBAAK8J,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,eACpE/J,OAAA;sBAAK8J,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,gBACzC/J,OAAA;wBAAM8J,SAAS,EAAC,sBAAsB;wBAAAC,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAChDnK,OAAA;wBAAK8J,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtC/J,OAAA;0BAAG8J,SAAS,EAAC,kBAAkB;0BAAAC,QAAA,EAAC;wBAA6B;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACjEnK,OAAA;0BAAI8J,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,gBAC/B/J,OAAA;4BAAA+J,QAAA,EAAI;0BAAsD;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAC/DnK,OAAA;4BAAA+J,QAAA,GAAI,wBAAiB,eAAA/J,OAAA;8BAAM8J,SAAS,EAAC,+BAA+B;8BAAAC,QAAA,EAAC;4BAAqB;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACtGnK,OAAA;4BAAA+J,QAAA,EAAI;0BAAuC;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9C,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACX,EAGAjH,SAAS,KAAKA,SAAS,CAACyJ,MAAM,KAAK,OAAO,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,CAAC,iBACpF9M,OAAA,CAAClB,QAAQ;gBAACgL,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,eACrC/J,OAAA;kBAAK8J,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxB/J,OAAA;oBAAK8J,SAAS,EAAC,6CAA6C;oBAAAC,QAAA,gBAC1D/J,OAAA;sBAAM8J,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnCnK,OAAA;sBAAI8J,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAqB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eAENnK,OAAA;oBAAK8J,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,eACpE/J,OAAA;sBAAK8J,SAAS,EAAC,uDAAuD;sBAAAC,QAAA,EACnEuC,IAAI,CAACC,SAAS,CAACrJ,SAAS,EAAE,IAAI,EAAE,CAAC;oBAAC;sBAAA8G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACX,EAGA5H,qBAAqB,iBACpBvC,OAAA,CAACH,iBAAiB;gBAChBkN,OAAO,EAAEA,CAAA,KAAMvK,wBAAwB,CAAC,KAAK;cAAE;gBAAAwH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CACF,eAEDnK,OAAA,CAACJ,qBAAqB;gBACpBmD,SAAS,EAAEA,SAAU;gBACrBiK,WAAW,EAAE,CAAAjK,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEkK,YAAY,KAAI,CAAC,CAAE;gBAC3CC,MAAM,EAAE9J,eAAgB;gBACxB+J,QAAQ,EAAE9J;cAAgB;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnK,OAAA,CAACV,YAAY;MACXuB,KAAK,EAAEA,KAAM;MACbE,KAAK,EAAEA,KAAM;MACbE,MAAM,EAAEA,MAAO;MACfE,MAAM,EAAEA,MAAO;MACf4K,QAAQ,EAAEA,CAAA,KAAMjL,QAAQ,CAAC,IAAI,CAAE;MAC/BkL,QAAQ,EAAEA,CAAA,KAAMhL,QAAQ,CAAC,IAAI,CAAE;MAC/BiL,aAAa,EAAEA,CAAA,KAAM/K,SAAS,CAAC,IAAI,CAAE;MACrCgL,aAAa,EAAEA,CAAA,KAAM9K,SAAS,CAAC,IAAI,CAAE;MACrCgM,SAAS,EAAE3K,gBAAiB;MAC5B4K,QAAQ,EAAC;IAAQ;MAAArD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAGFnK,OAAA,CAACvB,WAAW;MAAAsL,QAAA,EACT9H,QAAQ,gBACPjC,OAAA;QAAK8J,SAAS,EAAC,8BAA8B;QAAAC,QAAA,eAC3C/J,OAAA,CAACP,eAAe;UACdoF,EAAE,EAAE5C,QAAS;UACbsD,MAAM,EAAE;YAAEG,IAAI,EAAE,eAAe;YAAE6C,IAAI,EAAE;UAAU;QAAE;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,GACJ;IAAI;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEjB,CAAC;AAAChK,EAAA,CAhwBID,cAAc;EAAA,QACD3B,WAAW,EAsCxBuB,mBAAmB;AAAA;AAAAwN,EAAA,GAvCnBpN,cAAc;AAkwBpB,eAAeA,cAAc;AAAC,IAAAoN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}