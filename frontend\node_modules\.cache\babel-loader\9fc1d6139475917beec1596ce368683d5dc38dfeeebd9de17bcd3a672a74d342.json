{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIZekri\\\\frontend\\\\src\\\\components\\\\DragDropComponents.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\n/**\n * DragDropComponents - Composants pour l'interface drag & drop\n * Utilise @dnd-kit pour créer des colonnes draggables et des zones de drop\n */\n\nimport React from 'react';\nimport { useDraggable, useDroppable } from '@dnd-kit/core';\nimport { DarkBadge } from './YellowMindUI';\n\n// Composant pour une colonne draggable\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const DraggableColumn = ({\n  column,\n  id\n}) => {\n  _s();\n  const {\n    attributes,\n    listeners,\n    setNodeRef,\n    transform,\n    isDragging\n  } = useDraggable({\n    id: id,\n    data: {\n      column: column,\n      type: 'column'\n    }\n  });\n  const style = transform ? {\n    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,\n    opacity: isDragging ? 0.5 : 1,\n    zIndex: isDragging ? 1000 : 1\n  } : undefined;\n  const getColumnIcon = type => {\n    const dataType = type.toLowerCase();\n    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n      return '🔢';\n    }\n    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n      return '📝';\n    }\n    if (dataType.includes('date') || dataType.includes('time')) {\n      return '📅';\n    }\n    if (dataType.includes('bit') || dataType.includes('boolean')) {\n      return '☑️';\n    }\n    return '📊';\n  };\n  const getColumnColor = type => {\n    const dataType = type.toLowerCase();\n    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n      return 'bg-blue-600/20 border-blue-500 text-blue-300';\n    }\n    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n      return 'bg-green-600/20 border-green-500 text-green-300';\n    }\n    if (dataType.includes('date') || dataType.includes('time')) {\n      return 'bg-purple-600/20 border-purple-500 text-purple-300';\n    }\n    return 'bg-gray-600/20 border-gray-500 text-gray-300';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: setNodeRef,\n    style: style,\n    ...listeners,\n    ...attributes,\n    className: `\n        p-3 rounded-lg border-2 border-dashed cursor-grab active:cursor-grabbing\n        transition-all duration-200 hover:scale-105 hover:shadow-lg\n        ${getColumnColor(column.type)}\n        ${isDragging ? 'shadow-2xl' : 'hover:shadow-md'}\n      `,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-lg\",\n        children: getColumnIcon(column.type)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 min-w-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"font-medium truncate\",\n          children: column.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs opacity-75\",\n          children: column.type\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour une zone de drop\n_s(DraggableColumn, \"uZNJ3/8UzXAvFYh/tqZxqBQOH0I=\", false, function () {\n  return [useDraggable];\n});\n_c = DraggableColumn;\nexport const DropZone = ({\n  id,\n  title,\n  subtitle,\n  icon,\n  children,\n  acceptedColumn,\n  onClear\n}) => {\n  _s2();\n  const {\n    isOver,\n    setNodeRef\n  } = useDroppable({\n    id: id,\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: setNodeRef,\n    className: `\n        p-6 rounded-lg border-2 border-dashed min-h-[140px] transition-all duration-200\n        ${isOver ? 'border-purple-400 bg-purple-600/10 scale-105' : 'border-gray-600 bg-gray-800/30'}\n        ${acceptedColumn ? 'border-purple-500 bg-purple-600/20' : ''}\n      `,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-lg\",\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium text-gray-200\",\n            children: title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), subtitle && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-400\",\n            children: subtitle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 26\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), acceptedColumn && onClear && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClear,\n        className: \"text-gray-400 hover:text-red-400 transition-colors\",\n        title: \"Supprimer\",\n        children: \"\\u2715\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-[60px] flex items-center justify-center\",\n      children: acceptedColumn ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3 p-3 bg-purple-600/30 rounded-lg border border-purple-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xl\",\n            children: acceptedColumn.type.toLowerCase().includes('int') || acceptedColumn.type.toLowerCase().includes('decimal') || acceptedColumn.type.toLowerCase().includes('float') ? '🔢' : acceptedColumn.type.toLowerCase().includes('date') ? '📅' : '📝'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-medium text-purple-200 truncate\",\n              children: acceptedColumn.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-purple-300\",\n              children: acceptedColumn.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 17\n            }, this), acceptedColumn.table && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-purple-400 mt-1\",\n              children: [\"\\uD83D\\uDCCB \", acceptedColumn.table]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 text-sm\",\n          children: isOver ? 'Relâchez ici' : 'Glissez une colonne ici'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this), children]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour afficher les colonnes disponibles\n_s2(DropZone, \"fT702R7NW3L8KUJObOwGrnMsXMQ=\", false, function () {\n  return [useDroppable];\n});\n_c2 = DropZone;\nexport const ColumnsPanel = ({\n  columns,\n  title\n}) => {\n  if (!columns || columns.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-16 h-16 bg-gray-700 rounded-2xl mx-auto mb-4 flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-2xl\",\n          children: \"\\uD83D\\uDCCB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-400\",\n        children: \"Aucune colonne disponible\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-200\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DarkBadge, {\n        variant: \"info\",\n        children: columns.length\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3 max-h-[500px] overflow-y-auto\",\n      children: columns.map((column, index) => /*#__PURE__*/_jsxDEV(DraggableColumn, {\n        id: `column-${column.name}`,\n        column: column\n      }, `${column.name}-${index}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 171,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour les zones de drop organisées\n_c3 = ColumnsPanel;\nexport const DropZonesPanel = ({\n  xAxis,\n  yAxis,\n  legend,\n  values,\n  onClearX,\n  onClearY,\n  onClearLegend,\n  onClearValues\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n    children: [/*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"x-axis\",\n      title: \"Axe X\",\n      subtitle: \"Cat\\xE9gories ou groupes\",\n      icon: \"\\uD83D\\uDCCA\",\n      acceptedColumn: xAxis,\n      onClear: onClearX\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"y-axis\",\n      title: \"Axe Y\",\n      subtitle: \"Valeurs num\\xE9riques (optionnel)\",\n      icon: \"\\uD83D\\uDCC8\",\n      acceptedColumn: yAxis,\n      onClear: onClearY\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"legend\",\n      title: \"L\\xE9gende\",\n      subtitle: \"Sous-cat\\xE9gories (optionnel)\",\n      icon: \"\\uD83C\\uDFF7\\uFE0F\",\n      acceptedColumn: legend,\n      onClear: onClearLegend\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"values\",\n      title: \"Valeurs\",\n      subtitle: \"Donn\\xE9es \\xE0 agr\\xE9ger\",\n      icon: \"\\uD83D\\uDC8E\",\n      acceptedColumn: values,\n      onClear: onClearValues\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 202,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour une zone de drop compacte dans la barre fixe\n_c4 = DropZonesPanel;\nexport const CompactDropZone = ({\n  id,\n  title,\n  icon,\n  acceptedColumn,\n  onClear,\n  isOver\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n        flex items-center justify-between p-3 rounded-lg border-2 border-dashed min-h-[60px]\n        transition-all duration-300 ease-in-out transform\n        ${isOver ? 'border-purple-400 bg-purple-600/20 scale-105 drop-zone-glow drop-zone-hover' : 'border-gray-600 bg-gray-800/50 hover:border-gray-500 hover:bg-gray-800/70'}\n        ${acceptedColumn ? 'border-purple-500 bg-purple-600/30 shadow-lg shadow-purple-500/20' : ''}\n        backdrop-blur-sm hover:backdrop-blur-md\n      `,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2 flex-1 min-w-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: `text-lg transition-transform duration-200 ${isOver ? 'scale-110' : ''}`,\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-w-0 flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: `font-medium text-sm truncate transition-colors duration-200 ${isOver ? 'text-purple-200' : 'text-gray-200'}`,\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), acceptedColumn ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1 mt-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-purple-300 truncate font-medium\",\n            children: acceptedColumn.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this), acceptedColumn.table && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-400\",\n            children: [\"(\", acceptedColumn.table, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-xs transition-colors duration-200 ${isOver ? 'text-purple-300' : 'text-gray-400'}`,\n          children: isOver ? 'Relâchez ici' : 'Glissez ici'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this), acceptedColumn && onClear && /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onClear,\n      className: \"text-gray-400 hover:text-red-400 transition-all duration-200 ml-2 p-1 hover:bg-red-600/20 rounded hover:scale-110 active:scale-95\",\n      title: \"Supprimer\",\n      children: \"\\u2715\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 245,\n    columnNumber: 5\n  }, this);\n};\n\n// Barre fixe avec les zones de drop\n_c5 = CompactDropZone;\nexport const FixedDropBar = ({\n  xAxis,\n  yAxis,\n  legend,\n  values,\n  onClearX,\n  onClearY,\n  onClearLegend,\n  onClearValues,\n  isVisible = true,\n  position = 'bottom' // 'top' ou 'bottom'\n}) => {\n  _s3();\n  const {\n    isOver: isOverX,\n    setNodeRef: setNodeRefX\n  } = useDroppable({\n    id: 'x-axis-fixed',\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  const {\n    isOver: isOverY,\n    setNodeRef: setNodeRefY\n  } = useDroppable({\n    id: 'y-axis-fixed',\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  const {\n    isOver: isOverLegend,\n    setNodeRef: setNodeRefLegend\n  } = useDroppable({\n    id: 'legend-fixed',\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  const {\n    isOver: isOverValues,\n    setNodeRef: setNodeRefValues\n  } = useDroppable({\n    id: 'values-fixed',\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n  if (!isVisible) return null;\n  const positionClasses = position === 'top' ? 'top-0 border-b' : 'bottom-0 border-t';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n      fixed left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-md ${positionClasses} border-gray-700\n      transition-all duration-300 ease-in-out\n      ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'}\n    `,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-6 py-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg\",\n            children: \"\\uD83C\\uDFAF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-sm font-semibold text-gray-200\",\n            children: \"Zones de Drop\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs text-gray-400\",\n          children: \"Glissez vos colonnes ici pour cr\\xE9er votre visualisation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 lg:grid-cols-4 gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          ref: setNodeRefX,\n          children: /*#__PURE__*/_jsxDEV(CompactDropZone, {\n            id: \"x-axis-fixed\",\n            title: \"Axe X\",\n            icon: \"\\uD83D\\uDCCA\",\n            acceptedColumn: xAxis,\n            onClear: onClearX,\n            isOver: isOverX\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: setNodeRefY,\n          children: /*#__PURE__*/_jsxDEV(CompactDropZone, {\n            id: \"y-axis-fixed\",\n            title: \"Axe Y\",\n            icon: \"\\uD83D\\uDCC8\",\n            acceptedColumn: yAxis,\n            onClear: onClearY,\n            isOver: isOverY\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: setNodeRefLegend,\n          children: /*#__PURE__*/_jsxDEV(CompactDropZone, {\n            id: \"legend-fixed\",\n            title: \"L\\xE9gende\",\n            icon: \"\\uD83C\\uDFF7\\uFE0F\",\n            acceptedColumn: legend,\n            onClear: onClearLegend,\n            isOver: isOverLegend\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: setNodeRefValues,\n          children: /*#__PURE__*/_jsxDEV(CompactDropZone, {\n            id: \"values-fixed\",\n            title: \"Valeurs\",\n            icon: \"\\uD83D\\uDC8E\",\n            acceptedColumn: values,\n            onClear: onClearValues,\n            isOver: isOverValues\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 341,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour le sélecteur de fonction d'agrégation\n_s3(FixedDropBar, \"iDE4Z2x6jYrF9tSCiODbcpzg8qI=\", false, function () {\n  return [useDroppable, useDroppable, useDroppable, useDroppable];\n});\n_c6 = FixedDropBar;\nexport const AggregationSelector = ({\n  value,\n  onChange,\n  disabled\n}) => {\n  const aggregations = [{\n    value: 'SUM',\n    label: 'Somme',\n    icon: '➕',\n    description: 'Addition de toutes les valeurs'\n  }, {\n    value: 'AVG',\n    label: 'Moyenne',\n    icon: '📊',\n    description: 'Valeur moyenne'\n  }, {\n    value: 'COUNT',\n    label: 'Nombre',\n    icon: '🔢',\n    description: 'Nombre d\\'occurrences'\n  }, {\n    value: 'MIN',\n    label: 'Minimum',\n    icon: '⬇️',\n    description: 'Valeur minimale'\n  }, {\n    value: 'MAX',\n    label: 'Maximum',\n    icon: '⬆️',\n    description: 'Valeur maximale'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"block text-sm font-medium text-gray-300 mb-2\",\n      children: \"Fonction d'agr\\xE9gation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n      value: value,\n      onChange: e => onChange(e.target.value),\n      disabled: disabled,\n      className: \"w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-gray-100 focus:border-purple-500 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed\",\n      children: aggregations.map(agg => /*#__PURE__*/_jsxDEV(\"option\", {\n        value: agg.value,\n        children: [agg.icon, \" \", agg.label, \" - \", agg.description]\n      }, agg.value, true, {\n        fileName: _jsxFileName,\n        lineNumber: 429,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 418,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant pour le sélecteur de type de graphique\n_c7 = AggregationSelector;\nexport const ChartTypeSelector = ({\n  value,\n  onChange\n}) => {\n  const chartTypes = [{\n    value: 'bar',\n    label: 'Barres',\n    icon: '📊',\n    description: 'Graphique en barres'\n  }, {\n    value: 'line',\n    label: 'Ligne',\n    icon: '📈',\n    description: 'Graphique linéaire'\n  }, {\n    value: 'pie',\n    label: 'Circulaire',\n    icon: '🥧',\n    description: 'Graphique circulaire'\n  }, {\n    value: 'scatter',\n    label: 'Nuage',\n    icon: '⚫',\n    description: 'Nuage de points'\n  }, {\n    value: 'stacked_bar',\n    label: 'Barres empilées',\n    icon: '📚',\n    description: 'Barres empilées'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"block text-sm font-medium text-gray-300 mb-2\",\n      children: \"Type de graphique\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 md:grid-cols-3 gap-2\",\n      children: chartTypes.map(type => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onChange(type.value),\n        className: `p-3 rounded-lg border transition-colors text-center ${value === type.value ? 'border-purple-500 bg-purple-600/20 text-purple-300' : 'border-gray-700 bg-gray-800/50 text-gray-300 hover:border-gray-600'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg mb-1\",\n          children: type.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs font-medium\",\n          children: type.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 13\n        }, this)]\n      }, type.value, true, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 453,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 449,\n    columnNumber: 5\n  }, this);\n};\n_c8 = ChartTypeSelector;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"DraggableColumn\");\n$RefreshReg$(_c2, \"DropZone\");\n$RefreshReg$(_c3, \"ColumnsPanel\");\n$RefreshReg$(_c4, \"DropZonesPanel\");\n$RefreshReg$(_c5, \"CompactDropZone\");\n$RefreshReg$(_c6, \"FixedDropBar\");\n$RefreshReg$(_c7, \"AggregationSelector\");\n$RefreshReg$(_c8, \"ChartTypeSelector\");", "map": {"version": 3, "names": ["React", "useDraggable", "useDroppable", "DarkBadge", "jsxDEV", "_jsxDEV", "DraggableColumn", "column", "id", "_s", "attributes", "listeners", "setNodeRef", "transform", "isDragging", "data", "type", "style", "x", "y", "opacity", "zIndex", "undefined", "getColumnIcon", "dataType", "toLowerCase", "includes", "getColumnColor", "ref", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "_c", "DropZone", "title", "subtitle", "icon", "acceptedColumn", "onClear", "_s2", "isOver", "accepts", "onClick", "table", "_c2", "ColumnsPanel", "columns", "length", "variant", "map", "index", "_c3", "DropZonesPanel", "xAxis", "yAxis", "legend", "values", "onClearX", "onClearY", "onClearLegend", "onClearValues", "_c4", "CompactDropZone", "_c5", "FixedDropBar", "isVisible", "position", "_s3", "isOverX", "setNodeRefX", "isOverY", "setNodeRefY", "isOverLegend", "setNodeRefLegend", "isOverValues", "setNodeRefValues", "positionClasses", "_c6", "AggregationSelector", "value", "onChange", "disabled", "aggregations", "label", "description", "e", "target", "agg", "_c7", "ChartTypeSelector", "chartTypes", "_c8", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/AIZekri/frontend/src/components/DragDropComponents.js"], "sourcesContent": ["/**\n * DragDropComponents - Composants pour l'interface drag & drop\n * Utilise @dnd-kit pour créer des colonnes draggables et des zones de drop\n */\n\nimport React from 'react';\nimport { useDraggable, useDroppable } from '@dnd-kit/core';\nimport { DarkBadge } from './YellowMindUI';\n\n// Composant pour une colonne draggable\nexport const DraggableColumn = ({ column, id }) => {\n  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({\n    id: id,\n    data: {\n      column: column,\n      type: 'column'\n    }\n  });\n\n  const style = transform ? {\n    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,\n    opacity: isDragging ? 0.5 : 1,\n    zIndex: isDragging ? 1000 : 1\n  } : undefined;\n\n  const getColumnIcon = (type) => {\n    const dataType = type.toLowerCase();\n    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n      return '🔢';\n    }\n    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n      return '📝';\n    }\n    if (dataType.includes('date') || dataType.includes('time')) {\n      return '📅';\n    }\n    if (dataType.includes('bit') || dataType.includes('boolean')) {\n      return '☑️';\n    }\n    return '📊';\n  };\n\n  const getColumnColor = (type) => {\n    const dataType = type.toLowerCase();\n    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {\n      return 'bg-blue-600/20 border-blue-500 text-blue-300';\n    }\n    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {\n      return 'bg-green-600/20 border-green-500 text-green-300';\n    }\n    if (dataType.includes('date') || dataType.includes('time')) {\n      return 'bg-purple-600/20 border-purple-500 text-purple-300';\n    }\n    return 'bg-gray-600/20 border-gray-500 text-gray-300';\n  };\n\n  return (\n    <div\n      ref={setNodeRef}\n      style={style}\n      {...listeners}\n      {...attributes}\n      className={`\n        p-3 rounded-lg border-2 border-dashed cursor-grab active:cursor-grabbing\n        transition-all duration-200 hover:scale-105 hover:shadow-lg\n        ${getColumnColor(column.type)}\n        ${isDragging ? 'shadow-2xl' : 'hover:shadow-md'}\n      `}\n    >\n      <div className=\"flex items-center space-x-2\">\n        <span className=\"text-lg\">{getColumnIcon(column.type)}</span>\n        <div className=\"flex-1 min-w-0\">\n          <p className=\"font-medium truncate\">{column.name}</p>\n          <p className=\"text-xs opacity-75\">{column.type}</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Composant pour une zone de drop\nexport const DropZone = ({ id, title, subtitle, icon, children, acceptedColumn, onClear }) => {\n  const { isOver, setNodeRef } = useDroppable({\n    id: id,\n    data: {\n      type: 'dropzone',\n      accepts: ['column']\n    }\n  });\n\n  return (\n    <div\n      ref={setNodeRef}\n      className={`\n        p-6 rounded-lg border-2 border-dashed min-h-[140px] transition-all duration-200\n        ${isOver\n          ? 'border-purple-400 bg-purple-600/10 scale-105'\n          : 'border-gray-600 bg-gray-800/30'\n        }\n        ${acceptedColumn ? 'border-purple-500 bg-purple-600/20' : ''}\n      `}\n    >\n      {/* Header de la zone */}\n      <div className=\"flex items-center justify-between mb-3\">\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-lg\">{icon}</span>\n          <div>\n            <h4 className=\"font-medium text-gray-200\">{title}</h4>\n            {subtitle && <p className=\"text-xs text-gray-400\">{subtitle}</p>}\n          </div>\n        </div>\n        {acceptedColumn && onClear && (\n          <button\n            onClick={onClear}\n            className=\"text-gray-400 hover:text-red-400 transition-colors\"\n            title=\"Supprimer\"\n          >\n            ✕\n          </button>\n        )}\n      </div>\n\n      {/* Contenu de la zone */}\n      <div className=\"min-h-[60px] flex items-center justify-center\">\n        {acceptedColumn ? (\n          <div className=\"w-full\">\n            <div className=\"flex items-center space-x-3 p-3 bg-purple-600/30 rounded-lg border border-purple-500\">\n              <span className=\"text-xl\">\n                {acceptedColumn.type.toLowerCase().includes('int') ||\n                 acceptedColumn.type.toLowerCase().includes('decimal') ||\n                 acceptedColumn.type.toLowerCase().includes('float') ? '🔢' :\n                 acceptedColumn.type.toLowerCase().includes('date') ? '📅' : '📝'}\n              </span>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"font-medium text-purple-200 truncate\">{acceptedColumn.name}</p>\n                <p className=\"text-xs text-purple-300\">{acceptedColumn.type}</p>\n                {acceptedColumn.table && (\n                  <p className=\"text-xs text-purple-400 mt-1\">📋 {acceptedColumn.table}</p>\n                )}\n              </div>\n            </div>\n          </div>\n        ) : (\n          <div className=\"text-center\">\n            <p className=\"text-gray-500 text-sm\">\n              {isOver ? 'Relâchez ici' : 'Glissez une colonne ici'}\n            </p>\n          </div>\n        )}\n      </div>\n\n      {children}\n    </div>\n  );\n};\n\n// Composant pour afficher les colonnes disponibles\nexport const ColumnsPanel = ({ columns, title }) => {\n  if (!columns || columns.length === 0) {\n    return (\n      <div className=\"text-center py-8\">\n        <div className=\"w-16 h-16 bg-gray-700 rounded-2xl mx-auto mb-4 flex items-center justify-center\">\n          <span className=\"text-2xl\">📋</span>\n        </div>\n        <p className=\"text-gray-400\">Aucune colonne disponible</p>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-200\">{title}</h3>\n        <DarkBadge variant=\"info\">{columns.length}</DarkBadge>\n      </div>\n      \n      <div className=\"space-y-3 max-h-[500px] overflow-y-auto\">\n        {columns.map((column, index) => (\n          <DraggableColumn\n            key={`${column.name}-${index}`}\n            id={`column-${column.name}`}\n            column={column}\n          />\n        ))}\n      </div>\n    </div>\n  );\n};\n\n// Composant pour les zones de drop organisées\nexport const DropZonesPanel = ({\n  xAxis,\n  yAxis,\n  legend,\n  values,\n  onClearX,\n  onClearY,\n  onClearLegend,\n  onClearValues\n}) => {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n      <DropZone\n        id=\"x-axis\"\n        title=\"Axe X\"\n        subtitle=\"Catégories ou groupes\"\n        icon=\"📊\"\n        acceptedColumn={xAxis}\n        onClear={onClearX}\n      />\n\n      <DropZone\n        id=\"y-axis\"\n        title=\"Axe Y\"\n        subtitle=\"Valeurs numériques (optionnel)\"\n        icon=\"📈\"\n        acceptedColumn={yAxis}\n        onClear={onClearY}\n      />\n\n      <DropZone\n        id=\"legend\"\n        title=\"Légende\"\n        subtitle=\"Sous-catégories (optionnel)\"\n        icon=\"🏷️\"\n        acceptedColumn={legend}\n        onClear={onClearLegend}\n      />\n\n      <DropZone\n        id=\"values\"\n        title=\"Valeurs\"\n        subtitle=\"Données à agréger\"\n        icon=\"💎\"\n        acceptedColumn={values}\n        onClear={onClearValues}\n      />\n    </div>\n  );\n};\n\n// Composant pour une zone de drop compacte dans la barre fixe\nexport const CompactDropZone = ({ id, title, icon, acceptedColumn, onClear, isOver }) => {\n  return (\n    <div\n      className={`\n        flex items-center justify-between p-3 rounded-lg border-2 border-dashed min-h-[60px]\n        transition-all duration-300 ease-in-out transform\n        ${isOver\n          ? 'border-purple-400 bg-purple-600/20 scale-105 drop-zone-glow drop-zone-hover'\n          : 'border-gray-600 bg-gray-800/50 hover:border-gray-500 hover:bg-gray-800/70'\n        }\n        ${acceptedColumn ? 'border-purple-500 bg-purple-600/30 shadow-lg shadow-purple-500/20' : ''}\n        backdrop-blur-sm hover:backdrop-blur-md\n      `}\n    >\n      <div className=\"flex items-center space-x-2 flex-1 min-w-0\">\n        <span className={`text-lg transition-transform duration-200 ${isOver ? 'scale-110' : ''}`}>\n          {icon}\n        </span>\n        <div className=\"min-w-0 flex-1\">\n          <h4 className={`font-medium text-sm truncate transition-colors duration-200 ${\n            isOver ? 'text-purple-200' : 'text-gray-200'\n          }`}>\n            {title}\n          </h4>\n          {acceptedColumn ? (\n            <div className=\"flex items-center space-x-1 mt-1\">\n              <span className=\"text-xs text-purple-300 truncate font-medium\">\n                {acceptedColumn.name}\n              </span>\n              {acceptedColumn.table && (\n                <span className=\"text-xs text-gray-400\">\n                  ({acceptedColumn.table})\n                </span>\n              )}\n            </div>\n          ) : (\n            <p className={`text-xs transition-colors duration-200 ${\n              isOver ? 'text-purple-300' : 'text-gray-400'\n            }`}>\n              {isOver ? 'Relâchez ici' : 'Glissez ici'}\n            </p>\n          )}\n        </div>\n      </div>\n      {acceptedColumn && onClear && (\n        <button\n          onClick={onClear}\n          className=\"text-gray-400 hover:text-red-400 transition-all duration-200 ml-2 p-1\n                     hover:bg-red-600/20 rounded hover:scale-110 active:scale-95\"\n          title=\"Supprimer\"\n        >\n          ✕\n        </button>\n      )}\n    </div>\n  );\n};\n\n// Barre fixe avec les zones de drop\nexport const FixedDropBar = ({\n  xAxis,\n  yAxis,\n  legend,\n  values,\n  onClearX,\n  onClearY,\n  onClearLegend,\n  onClearValues,\n  isVisible = true,\n  position = 'bottom' // 'top' ou 'bottom'\n}) => {\n  const { isOver: isOverX, setNodeRef: setNodeRefX } = useDroppable({\n    id: 'x-axis-fixed',\n    data: { type: 'dropzone', accepts: ['column'] }\n  });\n\n  const { isOver: isOverY, setNodeRef: setNodeRefY } = useDroppable({\n    id: 'y-axis-fixed',\n    data: { type: 'dropzone', accepts: ['column'] }\n  });\n\n  const { isOver: isOverLegend, setNodeRef: setNodeRefLegend } = useDroppable({\n    id: 'legend-fixed',\n    data: { type: 'dropzone', accepts: ['column'] }\n  });\n\n  const { isOver: isOverValues, setNodeRef: setNodeRefValues } = useDroppable({\n    id: 'values-fixed',\n    data: { type: 'dropzone', accepts: ['column'] }\n  });\n\n  if (!isVisible) return null;\n\n  const positionClasses = position === 'top'\n    ? 'top-0 border-b'\n    : 'bottom-0 border-t';\n\n  return (\n    <div className={`\n      fixed left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-md ${positionClasses} border-gray-700\n      transition-all duration-300 ease-in-out\n      ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'}\n    `}>\n      <div className=\"max-w-7xl mx-auto px-6 py-4\">\n        <div className=\"flex items-center justify-between mb-3\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-lg\">🎯</span>\n            <h3 className=\"text-sm font-semibold text-gray-200\">Zones de Drop</h3>\n          </div>\n          <div className=\"text-xs text-gray-400\">\n            Glissez vos colonnes ici pour créer votre visualisation\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-3\">\n          <div ref={setNodeRefX}>\n            <CompactDropZone\n              id=\"x-axis-fixed\"\n              title=\"Axe X\"\n              icon=\"📊\"\n              acceptedColumn={xAxis}\n              onClear={onClearX}\n              isOver={isOverX}\n            />\n          </div>\n\n          <div ref={setNodeRefY}>\n            <CompactDropZone\n              id=\"y-axis-fixed\"\n              title=\"Axe Y\"\n              icon=\"📈\"\n              acceptedColumn={yAxis}\n              onClear={onClearY}\n              isOver={isOverY}\n            />\n          </div>\n\n          <div ref={setNodeRefLegend}>\n            <CompactDropZone\n              id=\"legend-fixed\"\n              title=\"Légende\"\n              icon=\"🏷️\"\n              acceptedColumn={legend}\n              onClear={onClearLegend}\n              isOver={isOverLegend}\n            />\n          </div>\n\n          <div ref={setNodeRefValues}>\n            <CompactDropZone\n              id=\"values-fixed\"\n              title=\"Valeurs\"\n              icon=\"💎\"\n              acceptedColumn={values}\n              onClear={onClearValues}\n              isOver={isOverValues}\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Composant pour le sélecteur de fonction d'agrégation\nexport const AggregationSelector = ({ value, onChange, disabled }) => {\n  const aggregations = [\n    { value: 'SUM', label: 'Somme', icon: '➕', description: 'Addition de toutes les valeurs' },\n    { value: 'AVG', label: 'Moyenne', icon: '📊', description: 'Valeur moyenne' },\n    { value: 'COUNT', label: 'Nombre', icon: '🔢', description: 'Nombre d\\'occurrences' },\n    { value: 'MIN', label: 'Minimum', icon: '⬇️', description: 'Valeur minimale' },\n    { value: 'MAX', label: 'Maximum', icon: '⬆️', description: 'Valeur maximale' }\n  ];\n\n  return (\n    <div>\n      <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n        Fonction d'agrégation\n      </label>\n      <select\n        value={value}\n        onChange={(e) => onChange(e.target.value)}\n        disabled={disabled}\n        className=\"w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-gray-100 focus:border-purple-500 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed\"\n      >\n        {aggregations.map((agg) => (\n          <option key={agg.value} value={agg.value}>\n            {agg.icon} {agg.label} - {agg.description}\n          </option>\n        ))}\n      </select>\n    </div>\n  );\n};\n\n// Composant pour le sélecteur de type de graphique\nexport const ChartTypeSelector = ({ value, onChange }) => {\n  const chartTypes = [\n    { value: 'bar', label: 'Barres', icon: '📊', description: 'Graphique en barres' },\n    { value: 'line', label: 'Ligne', icon: '📈', description: 'Graphique linéaire' },\n    { value: 'pie', label: 'Circulaire', icon: '🥧', description: 'Graphique circulaire' },\n    { value: 'scatter', label: 'Nuage', icon: '⚫', description: 'Nuage de points' },\n    { value: 'stacked_bar', label: 'Barres empilées', icon: '📚', description: 'Barres empilées' }\n  ];\n\n  return (\n    <div>\n      <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n        Type de graphique\n      </label>\n      <div className=\"grid grid-cols-2 md:grid-cols-3 gap-2\">\n        {chartTypes.map((type) => (\n          <button\n            key={type.value}\n            onClick={() => onChange(type.value)}\n            className={`p-3 rounded-lg border transition-colors text-center ${\n              value === type.value\n                ? 'border-purple-500 bg-purple-600/20 text-purple-300'\n                : 'border-gray-700 bg-gray-800/50 text-gray-300 hover:border-gray-600'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">{type.icon}</div>\n            <div className=\"text-xs font-medium\">{type.label}</div>\n          </button>\n        ))}\n      </div>\n    </div>\n  );\n};\n"], "mappings": ";;;;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,EAAEC,YAAY,QAAQ,eAAe;AAC1D,SAASC,SAAS,QAAQ,gBAAgB;;AAE1C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,eAAe,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAG,CAAC,KAAK;EAAAC,EAAA;EACjD,MAAM;IAAEC,UAAU;IAAEC,SAAS;IAAEC,UAAU;IAAEC,SAAS;IAAEC;EAAW,CAAC,GAAGb,YAAY,CAAC;IAChFO,EAAE,EAAEA,EAAE;IACNO,IAAI,EAAE;MACJR,MAAM,EAAEA,MAAM;MACdS,IAAI,EAAE;IACR;EACF,CAAC,CAAC;EAEF,MAAMC,KAAK,GAAGJ,SAAS,GAAG;IACxBA,SAAS,EAAE,eAAeA,SAAS,CAACK,CAAC,OAAOL,SAAS,CAACM,CAAC,QAAQ;IAC/DC,OAAO,EAAEN,UAAU,GAAG,GAAG,GAAG,CAAC;IAC7BO,MAAM,EAAEP,UAAU,GAAG,IAAI,GAAG;EAC9B,CAAC,GAAGQ,SAAS;EAEb,MAAMC,aAAa,GAAIP,IAAI,IAAK;IAC9B,MAAMQ,QAAQ,GAAGR,IAAI,CAACS,WAAW,CAAC,CAAC;IACnC,IAAID,QAAQ,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MACxH,OAAO,IAAI;IACb;IACA,IAAIF,QAAQ,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1F,OAAO,IAAI;IACb;IACA,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1D,OAAO,IAAI;IACb;IACA,IAAIF,QAAQ,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;MAC5D,OAAO,IAAI;IACb;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMC,cAAc,GAAIX,IAAI,IAAK;IAC/B,MAAMQ,QAAQ,GAAGR,IAAI,CAACS,WAAW,CAAC,CAAC;IACnC,IAAID,QAAQ,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MACxH,OAAO,8CAA8C;IACvD;IACA,IAAIF,QAAQ,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1F,OAAO,iDAAiD;IAC1D;IACA,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1D,OAAO,oDAAoD;IAC7D;IACA,OAAO,8CAA8C;EACvD,CAAC;EAED,oBACErB,OAAA;IACEuB,GAAG,EAAEhB,UAAW;IAChBK,KAAK,EAAEA,KAAM;IAAA,GACTN,SAAS;IAAA,GACTD,UAAU;IACdmB,SAAS,EAAE;AACjB;AACA;AACA,UAAUF,cAAc,CAACpB,MAAM,CAACS,IAAI,CAAC;AACrC,UAAUF,UAAU,GAAG,YAAY,GAAG,iBAAiB;AACvD,OAAQ;IAAAgB,QAAA,eAEFzB,OAAA;MAAKwB,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CzB,OAAA;QAAMwB,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAEP,aAAa,CAAChB,MAAM,CAACS,IAAI;MAAC;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC7D7B,OAAA;QAAKwB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BzB,OAAA;UAAGwB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAEvB,MAAM,CAAC4B;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrD7B,OAAA;UAAGwB,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEvB,MAAM,CAACS;QAAI;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAzB,EAAA,CAtEaH,eAAe;EAAA,QAC2CL,YAAY;AAAA;AAAAmC,EAAA,GADtE9B,eAAe;AAuE5B,OAAO,MAAM+B,QAAQ,GAAGA,CAAC;EAAE7B,EAAE;EAAE8B,KAAK;EAAEC,QAAQ;EAAEC,IAAI;EAAEV,QAAQ;EAAEW,cAAc;EAAEC;AAAQ,CAAC,KAAK;EAAAC,GAAA;EAC5F,MAAM;IAAEC,MAAM;IAAEhC;EAAW,CAAC,GAAGV,YAAY,CAAC;IAC1CM,EAAE,EAAEA,EAAE;IACNO,IAAI,EAAE;MACJC,IAAI,EAAE,UAAU;MAChB6B,OAAO,EAAE,CAAC,QAAQ;IACpB;EACF,CAAC,CAAC;EAEF,oBACExC,OAAA;IACEuB,GAAG,EAAEhB,UAAW;IAChBiB,SAAS,EAAE;AACjB;AACA,UAAUe,MAAM,GACJ,8CAA8C,GAC9C,gCAAgC;AAC5C,UACUH,cAAc,GAAG,oCAAoC,GAAG,EAAE;AACpE,OAAQ;IAAAX,QAAA,gBAGFzB,OAAA;MAAKwB,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDzB,OAAA;QAAKwB,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CzB,OAAA;UAAMwB,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAEU;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvC7B,OAAA;UAAAyB,QAAA,gBACEzB,OAAA;YAAIwB,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAEQ;UAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EACrDK,QAAQ,iBAAIlC,OAAA;YAAGwB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAES;UAAQ;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACLO,cAAc,IAAIC,OAAO,iBACxBrC,OAAA;QACEyC,OAAO,EAAEJ,OAAQ;QACjBb,SAAS,EAAC,oDAAoD;QAC9DS,KAAK,EAAC,WAAW;QAAAR,QAAA,EAClB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,+CAA+C;MAAAC,QAAA,EAC3DW,cAAc,gBACbpC,OAAA;QAAKwB,SAAS,EAAC,QAAQ;QAAAC,QAAA,eACrBzB,OAAA;UAAKwB,SAAS,EAAC,sFAAsF;UAAAC,QAAA,gBACnGzB,OAAA;YAAMwB,SAAS,EAAC,SAAS;YAAAC,QAAA,EACtBW,cAAc,CAACzB,IAAI,CAACS,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,IACjDe,cAAc,CAACzB,IAAI,CAACS,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,SAAS,CAAC,IACrDe,cAAc,CAACzB,IAAI,CAACS,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,GAAG,IAAI,GAC1De,cAAc,CAACzB,IAAI,CAACS,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG;UAAI;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACP7B,OAAA;YAAKwB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BzB,OAAA;cAAGwB,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAEW,cAAc,CAACN;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7E7B,OAAA;cAAGwB,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAEW,cAAc,CAACzB;YAAI;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC/DO,cAAc,CAACM,KAAK,iBACnB1C,OAAA;cAAGwB,SAAS,EAAC,8BAA8B;cAAAC,QAAA,GAAC,eAAG,EAACW,cAAc,CAACM,KAAK;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACzE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAEN7B,OAAA;QAAKwB,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BzB,OAAA;UAAGwB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EACjCc,MAAM,GAAG,cAAc,GAAG;QAAyB;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELJ,QAAQ;EAAA;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AAAAS,GAAA,CA3EaN,QAAQ;EAAA,QACYnC,YAAY;AAAA;AAAA8C,GAAA,GADhCX,QAAQ;AA4ErB,OAAO,MAAMY,YAAY,GAAGA,CAAC;EAAEC,OAAO;EAAEZ;AAAM,CAAC,KAAK;EAClD,IAAI,CAACY,OAAO,IAAIA,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;IACpC,oBACE9C,OAAA;MAAKwB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BzB,OAAA;QAAKwB,SAAS,EAAC,iFAAiF;QAAAC,QAAA,eAC9FzB,OAAA;UAAMwB,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACN7B,OAAA;QAAGwB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD,CAAC;EAEV;EAEA,oBACE7B,OAAA;IAAAyB,QAAA,gBACEzB,OAAA;MAAKwB,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDzB,OAAA;QAAIwB,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAEQ;MAAK;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAChE7B,OAAA,CAACF,SAAS;QAACiD,OAAO,EAAC,MAAM;QAAAtB,QAAA,EAAEoB,OAAO,CAACC;MAAM;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC,eAEN7B,OAAA;MAAKwB,SAAS,EAAC,yCAAyC;MAAAC,QAAA,EACrDoB,OAAO,CAACG,GAAG,CAAC,CAAC9C,MAAM,EAAE+C,KAAK,kBACzBjD,OAAA,CAACC,eAAe;QAEdE,EAAE,EAAE,UAAUD,MAAM,CAAC4B,IAAI,EAAG;QAC5B5B,MAAM,EAAEA;MAAO,GAFV,GAAGA,MAAM,CAAC4B,IAAI,IAAImB,KAAK,EAAE;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAG/B,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAqB,GAAA,GAhCaN,YAAY;AAiCzB,OAAO,MAAMO,cAAc,GAAGA,CAAC;EAC7BC,KAAK;EACLC,KAAK;EACLC,MAAM;EACNC,MAAM;EACNC,QAAQ;EACRC,QAAQ;EACRC,aAAa;EACbC;AACF,CAAC,KAAK;EACJ,oBACE3D,OAAA;IAAKwB,SAAS,EAAC,uCAAuC;IAAAC,QAAA,gBACpDzB,OAAA,CAACgC,QAAQ;MACP7B,EAAE,EAAC,QAAQ;MACX8B,KAAK,EAAC,OAAO;MACbC,QAAQ,EAAC,0BAAuB;MAChCC,IAAI,EAAC,cAAI;MACTC,cAAc,EAAEgB,KAAM;MACtBf,OAAO,EAAEmB;IAAS;MAAA9B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eAEF7B,OAAA,CAACgC,QAAQ;MACP7B,EAAE,EAAC,QAAQ;MACX8B,KAAK,EAAC,OAAO;MACbC,QAAQ,EAAC,mCAAgC;MACzCC,IAAI,EAAC,cAAI;MACTC,cAAc,EAAEiB,KAAM;MACtBhB,OAAO,EAAEoB;IAAS;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eAEF7B,OAAA,CAACgC,QAAQ;MACP7B,EAAE,EAAC,QAAQ;MACX8B,KAAK,EAAC,YAAS;MACfC,QAAQ,EAAC,gCAA6B;MACtCC,IAAI,EAAC,oBAAK;MACVC,cAAc,EAAEkB,MAAO;MACvBjB,OAAO,EAAEqB;IAAc;MAAAhC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAEF7B,OAAA,CAACgC,QAAQ;MACP7B,EAAE,EAAC,QAAQ;MACX8B,KAAK,EAAC,SAAS;MACfC,QAAQ,EAAC,4BAAmB;MAC5BC,IAAI,EAAC,cAAI;MACTC,cAAc,EAAEmB,MAAO;MACvBlB,OAAO,EAAEsB;IAAc;MAAAjC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;;AAED;AAAA+B,GAAA,GAnDaT,cAAc;AAoD3B,OAAO,MAAMU,eAAe,GAAGA,CAAC;EAAE1D,EAAE;EAAE8B,KAAK;EAAEE,IAAI;EAAEC,cAAc;EAAEC,OAAO;EAAEE;AAAO,CAAC,KAAK;EACvF,oBACEvC,OAAA;IACEwB,SAAS,EAAE;AACjB;AACA;AACA,UAAUe,MAAM,GACJ,6EAA6E,GAC7E,2EAA2E;AACvF,UACUH,cAAc,GAAG,mEAAmE,GAAG,EAAE;AACnG;AACA,OAAQ;IAAAX,QAAA,gBAEFzB,OAAA;MAAKwB,SAAS,EAAC,4CAA4C;MAAAC,QAAA,gBACzDzB,OAAA;QAAMwB,SAAS,EAAE,6CAA6Ce,MAAM,GAAG,WAAW,GAAG,EAAE,EAAG;QAAAd,QAAA,EACvFU;MAAI;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACP7B,OAAA;QAAKwB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BzB,OAAA;UAAIwB,SAAS,EAAE,+DACbe,MAAM,GAAG,iBAAiB,GAAG,eAAe,EAC3C;UAAAd,QAAA,EACAQ;QAAK;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EACJO,cAAc,gBACbpC,OAAA;UAAKwB,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/CzB,OAAA;YAAMwB,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAC3DW,cAAc,CAACN;UAAI;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,EACNO,cAAc,CAACM,KAAK,iBACnB1C,OAAA;YAAMwB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,GACrC,EAACW,cAAc,CAACM,KAAK,EAAC,GACzB;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAEN7B,OAAA;UAAGwB,SAAS,EAAE,0CACZe,MAAM,GAAG,iBAAiB,GAAG,eAAe,EAC3C;UAAAd,QAAA,EACAc,MAAM,GAAG,cAAc,GAAG;QAAa;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EACLO,cAAc,IAAIC,OAAO,iBACxBrC,OAAA;MACEyC,OAAO,EAAEJ,OAAQ;MACjBb,SAAS,EAAC,mIAC6D;MACvES,KAAK,EAAC,WAAW;MAAAR,QAAA,EAClB;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CACT;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAiC,GAAA,GA1DaD,eAAe;AA2D5B,OAAO,MAAME,YAAY,GAAGA,CAAC;EAC3BX,KAAK;EACLC,KAAK;EACLC,MAAM;EACNC,MAAM;EACNC,QAAQ;EACRC,QAAQ;EACRC,aAAa;EACbC,aAAa;EACbK,SAAS,GAAG,IAAI;EAChBC,QAAQ,GAAG,QAAQ,CAAC;AACtB,CAAC,KAAK;EAAAC,GAAA;EACJ,MAAM;IAAE3B,MAAM,EAAE4B,OAAO;IAAE5D,UAAU,EAAE6D;EAAY,CAAC,GAAGvE,YAAY,CAAC;IAChEM,EAAE,EAAE,cAAc;IAClBO,IAAI,EAAE;MAAEC,IAAI,EAAE,UAAU;MAAE6B,OAAO,EAAE,CAAC,QAAQ;IAAE;EAChD,CAAC,CAAC;EAEF,MAAM;IAAED,MAAM,EAAE8B,OAAO;IAAE9D,UAAU,EAAE+D;EAAY,CAAC,GAAGzE,YAAY,CAAC;IAChEM,EAAE,EAAE,cAAc;IAClBO,IAAI,EAAE;MAAEC,IAAI,EAAE,UAAU;MAAE6B,OAAO,EAAE,CAAC,QAAQ;IAAE;EAChD,CAAC,CAAC;EAEF,MAAM;IAAED,MAAM,EAAEgC,YAAY;IAAEhE,UAAU,EAAEiE;EAAiB,CAAC,GAAG3E,YAAY,CAAC;IAC1EM,EAAE,EAAE,cAAc;IAClBO,IAAI,EAAE;MAAEC,IAAI,EAAE,UAAU;MAAE6B,OAAO,EAAE,CAAC,QAAQ;IAAE;EAChD,CAAC,CAAC;EAEF,MAAM;IAAED,MAAM,EAAEkC,YAAY;IAAElE,UAAU,EAAEmE;EAAiB,CAAC,GAAG7E,YAAY,CAAC;IAC1EM,EAAE,EAAE,cAAc;IAClBO,IAAI,EAAE;MAAEC,IAAI,EAAE,UAAU;MAAE6B,OAAO,EAAE,CAAC,QAAQ;IAAE;EAChD,CAAC,CAAC;EAEF,IAAI,CAACwB,SAAS,EAAE,OAAO,IAAI;EAE3B,MAAMW,eAAe,GAAGV,QAAQ,KAAK,KAAK,GACtC,gBAAgB,GAChB,mBAAmB;EAEvB,oBACEjE,OAAA;IAAKwB,SAAS,EAAE;AACpB,kEAAkEmD,eAAe;AACjF;AACA,QAAQX,SAAS,GAAG,2BAA2B,GAAG,4BAA4B;AAC9E,KAAM;IAAAvC,QAAA,eACAzB,OAAA;MAAKwB,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CzB,OAAA;QAAKwB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDzB,OAAA;UAAKwB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CzB,OAAA;YAAMwB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnC7B,OAAA;YAAIwB,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACN7B,OAAA;UAAKwB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAEvC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7B,OAAA;QAAKwB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDzB,OAAA;UAAKuB,GAAG,EAAE6C,WAAY;UAAA3C,QAAA,eACpBzB,OAAA,CAAC6D,eAAe;YACd1D,EAAE,EAAC,cAAc;YACjB8B,KAAK,EAAC,OAAO;YACbE,IAAI,EAAC,cAAI;YACTC,cAAc,EAAEgB,KAAM;YACtBf,OAAO,EAAEmB,QAAS;YAClBjB,MAAM,EAAE4B;UAAQ;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN7B,OAAA;UAAKuB,GAAG,EAAE+C,WAAY;UAAA7C,QAAA,eACpBzB,OAAA,CAAC6D,eAAe;YACd1D,EAAE,EAAC,cAAc;YACjB8B,KAAK,EAAC,OAAO;YACbE,IAAI,EAAC,cAAI;YACTC,cAAc,EAAEiB,KAAM;YACtBhB,OAAO,EAAEoB,QAAS;YAClBlB,MAAM,EAAE8B;UAAQ;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN7B,OAAA;UAAKuB,GAAG,EAAEiD,gBAAiB;UAAA/C,QAAA,eACzBzB,OAAA,CAAC6D,eAAe;YACd1D,EAAE,EAAC,cAAc;YACjB8B,KAAK,EAAC,YAAS;YACfE,IAAI,EAAC,oBAAK;YACVC,cAAc,EAAEkB,MAAO;YACvBjB,OAAO,EAAEqB,aAAc;YACvBnB,MAAM,EAAEgC;UAAa;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN7B,OAAA;UAAKuB,GAAG,EAAEmD,gBAAiB;UAAAjD,QAAA,eACzBzB,OAAA,CAAC6D,eAAe;YACd1D,EAAE,EAAC,cAAc;YACjB8B,KAAK,EAAC,SAAS;YACfE,IAAI,EAAC,cAAI;YACTC,cAAc,EAAEmB,MAAO;YACvBlB,OAAO,EAAEsB,aAAc;YACvBpB,MAAM,EAAEkC;UAAa;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAqC,GAAA,CAzGaH,YAAY;EAAA,QAY8BlE,YAAY,EAKZA,YAAY,EAKFA,YAAY,EAKZA,YAAY;AAAA;AAAA+E,GAAA,GA3BhEb,YAAY;AA0GzB,OAAO,MAAMc,mBAAmB,GAAGA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EACpE,MAAMC,YAAY,GAAG,CACnB;IAAEH,KAAK,EAAE,KAAK;IAAEI,KAAK,EAAE,OAAO;IAAE/C,IAAI,EAAE,GAAG;IAAEgD,WAAW,EAAE;EAAiC,CAAC,EAC1F;IAAEL,KAAK,EAAE,KAAK;IAAEI,KAAK,EAAE,SAAS;IAAE/C,IAAI,EAAE,IAAI;IAAEgD,WAAW,EAAE;EAAiB,CAAC,EAC7E;IAAEL,KAAK,EAAE,OAAO;IAAEI,KAAK,EAAE,QAAQ;IAAE/C,IAAI,EAAE,IAAI;IAAEgD,WAAW,EAAE;EAAwB,CAAC,EACrF;IAAEL,KAAK,EAAE,KAAK;IAAEI,KAAK,EAAE,SAAS;IAAE/C,IAAI,EAAE,IAAI;IAAEgD,WAAW,EAAE;EAAkB,CAAC,EAC9E;IAAEL,KAAK,EAAE,KAAK;IAAEI,KAAK,EAAE,SAAS;IAAE/C,IAAI,EAAE,IAAI;IAAEgD,WAAW,EAAE;EAAkB,CAAC,CAC/E;EAED,oBACEnF,OAAA;IAAAyB,QAAA,gBACEzB,OAAA;MAAOwB,SAAS,EAAC,8CAA8C;MAAAC,QAAA,EAAC;IAEhE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACR7B,OAAA;MACE8E,KAAK,EAAEA,KAAM;MACbC,QAAQ,EAAGK,CAAC,IAAKL,QAAQ,CAACK,CAAC,CAACC,MAAM,CAACP,KAAK,CAAE;MAC1CE,QAAQ,EAAEA,QAAS;MACnBxD,SAAS,EAAC,mKAAmK;MAAAC,QAAA,EAE5KwD,YAAY,CAACjC,GAAG,CAAEsC,GAAG,iBACpBtF,OAAA;QAAwB8E,KAAK,EAAEQ,GAAG,CAACR,KAAM;QAAArD,QAAA,GACtC6D,GAAG,CAACnD,IAAI,EAAC,GAAC,EAACmD,GAAG,CAACJ,KAAK,EAAC,KAAG,EAACI,GAAG,CAACH,WAAW;MAAA,GAD9BG,GAAG,CAACR,KAAK;QAAApD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEd,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AAAA0D,GAAA,GA9BaV,mBAAmB;AA+BhC,OAAO,MAAMW,iBAAiB,GAAGA,CAAC;EAAEV,KAAK;EAAEC;AAAS,CAAC,KAAK;EACxD,MAAMU,UAAU,GAAG,CACjB;IAAEX,KAAK,EAAE,KAAK;IAAEI,KAAK,EAAE,QAAQ;IAAE/C,IAAI,EAAE,IAAI;IAAEgD,WAAW,EAAE;EAAsB,CAAC,EACjF;IAAEL,KAAK,EAAE,MAAM;IAAEI,KAAK,EAAE,OAAO;IAAE/C,IAAI,EAAE,IAAI;IAAEgD,WAAW,EAAE;EAAqB,CAAC,EAChF;IAAEL,KAAK,EAAE,KAAK;IAAEI,KAAK,EAAE,YAAY;IAAE/C,IAAI,EAAE,IAAI;IAAEgD,WAAW,EAAE;EAAuB,CAAC,EACtF;IAAEL,KAAK,EAAE,SAAS;IAAEI,KAAK,EAAE,OAAO;IAAE/C,IAAI,EAAE,GAAG;IAAEgD,WAAW,EAAE;EAAkB,CAAC,EAC/E;IAAEL,KAAK,EAAE,aAAa;IAAEI,KAAK,EAAE,iBAAiB;IAAE/C,IAAI,EAAE,IAAI;IAAEgD,WAAW,EAAE;EAAkB,CAAC,CAC/F;EAED,oBACEnF,OAAA;IAAAyB,QAAA,gBACEzB,OAAA;MAAOwB,SAAS,EAAC,8CAA8C;MAAAC,QAAA,EAAC;IAEhE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACR7B,OAAA;MAAKwB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EACnDgE,UAAU,CAACzC,GAAG,CAAErC,IAAI,iBACnBX,OAAA;QAEEyC,OAAO,EAAEA,CAAA,KAAMsC,QAAQ,CAACpE,IAAI,CAACmE,KAAK,CAAE;QACpCtD,SAAS,EAAE,uDACTsD,KAAK,KAAKnE,IAAI,CAACmE,KAAK,GAChB,oDAAoD,GACpD,oEAAoE,EACvE;QAAArD,QAAA,gBAEHzB,OAAA;UAAKwB,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAEd,IAAI,CAACwB;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/C7B,OAAA;UAAKwB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAEd,IAAI,CAACuE;QAAK;UAAAxD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,GATlDlB,IAAI,CAACmE,KAAK;QAAApD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUT,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC6D,GAAA,GAhCWF,iBAAiB;AAAA,IAAAzD,EAAA,EAAAY,GAAA,EAAAO,GAAA,EAAAU,GAAA,EAAAE,GAAA,EAAAc,GAAA,EAAAW,GAAA,EAAAG,GAAA;AAAAC,YAAA,CAAA5D,EAAA;AAAA4D,YAAA,CAAAhD,GAAA;AAAAgD,YAAA,CAAAzC,GAAA;AAAAyC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAAf,GAAA;AAAAe,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}