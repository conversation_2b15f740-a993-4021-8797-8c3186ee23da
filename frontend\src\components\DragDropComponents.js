/**
 * DragDropComponents - Composants pour l'interface drag & drop
 * Utilise @dnd-kit pour créer des colonnes draggables et des zones de drop
 */

import React, { useState, useRef, useEffect } from 'react';
import { useDraggable, useDroppable } from '@dnd-kit/core';
import { DarkBadge } from './YellowMindUI';

// Composant de menu contextuel pour les colonnes
export const ColumnContextMenu = ({
  isOpen,
  onClose,
  position,
  column,
  onAddToAxis,
  selectedTable
}) => {
  const menuRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const menuItems = [
    { id: 'x-axis', label: 'Ajouter à Axe X', icon: '📊', color: 'text-blue-400' },
    { id: 'y-axis', label: 'Ajouter à Axe Y', icon: '📈', color: 'text-green-400' },
    { id: 'legend', label: 'Ajouter à Légende', icon: '🏷️', color: 'text-yellow-400' },
    { id: 'values', label: 'Ajouter aux Valeurs', icon: '💎', color: 'text-purple-400' }
  ];

  return (
    <div
      ref={menuRef}
      className="fixed z-50 bg-gray-800 border border-gray-600 rounded-lg shadow-xl backdrop-blur-sm"
      style={{
        left: position.x,
        top: position.y,
        minWidth: '200px'
      }}
    >
      <div className="p-2">
        <div className="px-3 py-2 border-b border-gray-600 mb-2">
          <p className="text-sm font-medium text-gray-200">{column.name}</p>
          <p className="text-xs text-gray-400">{column.type}</p>
        </div>

        {menuItems.map((item) => (
          <button
            key={item.id}
            onClick={() => {
              const columnWithTable = { ...column, table: selectedTable };
              onAddToAxis(item.id, columnWithTable);
              onClose();
            }}
            className={`
              w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left
              hover:bg-gray-700 transition-colors duration-200 group
            `}
          >
            <span className="text-lg">{item.icon}</span>
            <span className={`text-sm ${item.color} group-hover:text-gray-100`}>
              {item.label}
            </span>
          </button>
        ))}
      </div>
    </div>
  );
};

// Composant pour une colonne draggable
export const DraggableColumn = ({ column, id, onAddToAxis, selectedTable }) => {
  const [contextMenu, setContextMenu] = useState({ isOpen: false, position: { x: 0, y: 0 } });

  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: id,
    data: {
      column: column,
      type: 'column'
    }
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
    opacity: isDragging ? 0.7 : 1,
    zIndex: isDragging ? 1000 : 1
  } : undefined;

  const handleContextMenu = (e) => {
    e.preventDefault();
    setContextMenu({
      isOpen: true,
      position: { x: e.clientX, y: e.clientY }
    });
  };

  const handleQuickAdd = (e) => {
    e.stopPropagation();
    setContextMenu({
      isOpen: true,
      position: {
        x: e.currentTarget.getBoundingClientRect().right + 10,
        y: e.currentTarget.getBoundingClientRect().top
      }
    });
  };

  const getColumnIcon = (type) => {
    const dataType = type.toLowerCase();
    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {
      return '🔢';
    }
    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {
      return '📝';
    }
    if (dataType.includes('date') || dataType.includes('time')) {
      return '📅';
    }
    if (dataType.includes('bit') || dataType.includes('boolean')) {
      return '☑️';
    }
    return '📊';
  };

  const getColumnColor = (type) => {
    const dataType = type.toLowerCase();
    if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') || dataType.includes('money')) {
      return 'bg-blue-600/20 border-blue-500 text-blue-300';
    }
    if (dataType.includes('varchar') || dataType.includes('text') || dataType.includes('char')) {
      return 'bg-green-600/20 border-green-500 text-green-300';
    }
    if (dataType.includes('date') || dataType.includes('time')) {
      return 'bg-purple-600/20 border-purple-500 text-purple-300';
    }
    return 'bg-gray-600/20 border-gray-500 text-gray-300';
  };

  return (
    <>
      <div
        ref={setNodeRef}
        style={style}
        {...listeners}
        {...attributes}
        onContextMenu={handleContextMenu}
        className={`
          p-3 rounded-lg border-2 border-dashed cursor-grab active:cursor-grabbing
          transition-all duration-300 ease-in-out transform hover:scale-105 hover:shadow-lg
          ${getColumnColor(column.type)}
          ${isDragging ? 'shadow-2xl column-drag-start rotate-3 scale-110' : 'hover:shadow-md hover:shadow-purple-500/10'}
          group relative
        `}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 flex-1 min-w-0">
            <span className={`text-lg transition-transform duration-200 ${
              isDragging ? 'scale-110' : 'group-hover:scale-105'
            }`}>
              {getColumnIcon(column.type)}
            </span>
            <div className="flex-1 min-w-0">
              <p className={`font-medium truncate transition-colors duration-200 ${
                isDragging ? 'text-purple-200' : 'group-hover:text-gray-100'
              }`}>
                {column.name}
              </p>
              <p className={`text-xs transition-all duration-200 ${
                isDragging ? 'opacity-90 text-purple-300' : 'opacity-75 group-hover:opacity-90'
              }`}>
                {column.type}
              </p>
            </div>
          </div>

          {/* Bouton d'ajout rapide */}
          {onAddToAxis && (
            <button
              onClick={handleQuickAdd}
              className="opacity-0 group-hover:opacity-100 transition-all duration-200
                         p-1 rounded hover:bg-gray-700 text-gray-400 hover:text-purple-400
                         transform hover:scale-110 active:scale-95"
              title="Ajouter rapidement"
            >
              ⚡
            </button>
          )}
        </div>
      </div>

      {/* Menu contextuel */}
      <ColumnContextMenu
        isOpen={contextMenu.isOpen}
        onClose={() => setContextMenu({ ...contextMenu, isOpen: false })}
        position={contextMenu.position}
        column={column}
        onAddToAxis={onAddToAxis}
        selectedTable={selectedTable}
      />
    </>
  );
};

// Composant pour une zone de drop
export const DropZone = ({ id, title, subtitle, icon, children, acceptedColumn, onClear }) => {
  const { isOver, setNodeRef } = useDroppable({
    id: id,
    data: {
      type: 'dropzone',
      accepts: ['column']
    }
  });

  return (
    <div
      ref={setNodeRef}
      className={`
        p-6 rounded-lg border-2 border-dashed min-h-[140px] transition-all duration-200
        ${isOver
          ? 'border-purple-400 bg-purple-600/10 scale-105'
          : 'border-gray-600 bg-gray-800/30'
        }
        ${acceptedColumn ? 'border-purple-500 bg-purple-600/20' : ''}
      `}
    >
      {/* Header de la zone */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <span className="text-lg">{icon}</span>
          <div>
            <h4 className="font-medium text-gray-200">{title}</h4>
            {subtitle && <p className="text-xs text-gray-400">{subtitle}</p>}
          </div>
        </div>
        {acceptedColumn && onClear && (
          <button
            onClick={onClear}
            className="text-gray-400 hover:text-red-400 transition-colors"
            title="Supprimer"
          >
            ✕
          </button>
        )}
      </div>

      {/* Contenu de la zone */}
      <div className="min-h-[60px] flex items-center justify-center">
        {acceptedColumn ? (
          <div className="w-full">
            <div className="flex items-center space-x-3 p-3 bg-purple-600/30 rounded-lg border border-purple-500">
              <span className="text-xl">
                {acceptedColumn.type.toLowerCase().includes('int') ||
                 acceptedColumn.type.toLowerCase().includes('decimal') ||
                 acceptedColumn.type.toLowerCase().includes('float') ? '🔢' :
                 acceptedColumn.type.toLowerCase().includes('date') ? '📅' : '📝'}
              </span>
              <div className="flex-1 min-w-0">
                <p className="font-medium text-purple-200 truncate">{acceptedColumn.name}</p>
                <p className="text-xs text-purple-300">{acceptedColumn.type}</p>
                {acceptedColumn.table && (
                  <p className="text-xs text-purple-400 mt-1">📋 {acceptedColumn.table}</p>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center">
            <p className="text-gray-500 text-sm">
              {isOver ? 'Relâchez ici' : 'Glissez une colonne ici'}
            </p>
          </div>
        )}
      </div>

      {children}
    </div>
  );
};

// Composant pour afficher les colonnes disponibles
export const ColumnsPanel = ({ columns, title, onAddToAxis, selectedTable }) => {
  if (!columns || columns.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="w-16 h-16 bg-gray-700 rounded-2xl mx-auto mb-4 flex items-center justify-center">
          <span className="text-2xl">📋</span>
        </div>
        <p className="text-gray-400">Aucune colonne disponible</p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-200">{title}</h3>
        <DarkBadge variant="info">{columns.length}</DarkBadge>
      </div>

      <div className="space-y-3 max-h-[500px] overflow-y-auto">
        {columns.map((column, index) => (
          <DraggableColumn
            key={`${column.name}-${index}`}
            id={`column-${column.name}`}
            column={column}
            onAddToAxis={onAddToAxis}
            selectedTable={selectedTable}
          />
        ))}
      </div>
    </div>
  );
};

// Composant pour les zones de drop organisées
export const DropZonesPanel = ({
  xAxis,
  yAxis,
  legend,
  values,
  onClearX,
  onClearY,
  onClearLegend,
  onClearValues
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <DropZone
        id="x-axis"
        title="Axe X"
        subtitle="Catégories ou groupes"
        icon="📊"
        acceptedColumn={xAxis}
        onClear={onClearX}
      />

      <DropZone
        id="y-axis"
        title="Axe Y"
        subtitle="Valeurs numériques (optionnel)"
        icon="📈"
        acceptedColumn={yAxis}
        onClear={onClearY}
      />

      <DropZone
        id="legend"
        title="Légende"
        subtitle="Sous-catégories (optionnel)"
        icon="🏷️"
        acceptedColumn={legend}
        onClear={onClearLegend}
      />

      <DropZone
        id="values"
        title="Valeurs"
        subtitle="Données à agréger"
        icon="💎"
        acceptedColumn={values}
        onClear={onClearValues}
      />
    </div>
  );
};

// Composant pour une zone de drop compacte dans la barre fixe
export const CompactDropZone = ({ id, title, icon, acceptedColumn, onClear, isOver }) => {
  return (
    <div
      className={`
        flex items-center justify-between p-3 rounded-lg border-2 border-dashed min-h-[60px]
        transition-all duration-300 ease-in-out transform
        ${isOver
          ? 'border-purple-400 bg-purple-600/20 scale-105 drop-zone-glow drop-zone-hover'
          : 'border-gray-600 bg-gray-800/50 hover:border-gray-500 hover:bg-gray-800/70'
        }
        ${acceptedColumn ? 'border-purple-500 bg-purple-600/30 shadow-lg shadow-purple-500/20' : ''}
        backdrop-blur-sm hover:backdrop-blur-md
      `}
    >
      <div className="flex items-center space-x-2 flex-1 min-w-0">
        <span className={`text-lg transition-transform duration-200 ${isOver ? 'scale-110' : ''}`}>
          {icon}
        </span>
        <div className="min-w-0 flex-1">
          <h4 className={`font-medium text-sm truncate transition-colors duration-200 ${
            isOver ? 'text-purple-200' : 'text-gray-200'
          }`}>
            {title}
          </h4>
          {acceptedColumn ? (
            <div className="flex items-center space-x-1 mt-1">
              <span className="text-xs text-purple-300 truncate font-medium">
                {acceptedColumn.name}
              </span>
              {acceptedColumn.table && (
                <span className="text-xs text-gray-400">
                  ({acceptedColumn.table})
                </span>
              )}
            </div>
          ) : (
            <p className={`text-xs transition-colors duration-200 ${
              isOver ? 'text-purple-300' : 'text-gray-400'
            }`}>
              {isOver ? 'Relâchez ici' : 'Glissez ici'}
            </p>
          )}
        </div>
      </div>
      {acceptedColumn && onClear && (
        <button
          onClick={onClear}
          className="text-gray-400 hover:text-red-400 transition-all duration-200 ml-2 p-1
                     hover:bg-red-600/20 rounded hover:scale-110 active:scale-95"
          title="Supprimer"
        >
          ✕
        </button>
      )}
    </div>
  );
};

// Barre fixe avec les zones de drop
export const FixedDropBar = ({
  xAxis,
  yAxis,
  legend,
  values,
  onClearX,
  onClearY,
  onClearLegend,
  onClearValues,
  isVisible = true,
  position = 'bottom' // 'top' ou 'bottom'
}) => {
  const { isOver: isOverX, setNodeRef: setNodeRefX } = useDroppable({
    id: 'x-axis-fixed',
    data: { type: 'dropzone', accepts: ['column'] }
  });

  const { isOver: isOverY, setNodeRef: setNodeRefY } = useDroppable({
    id: 'y-axis-fixed',
    data: { type: 'dropzone', accepts: ['column'] }
  });

  const { isOver: isOverLegend, setNodeRef: setNodeRefLegend } = useDroppable({
    id: 'legend-fixed',
    data: { type: 'dropzone', accepts: ['column'] }
  });

  const { isOver: isOverValues, setNodeRef: setNodeRefValues } = useDroppable({
    id: 'values-fixed',
    data: { type: 'dropzone', accepts: ['column'] }
  });

  if (!isVisible) return null;

  const positionClasses = position === 'top'
    ? 'top-0 border-b'
    : 'bottom-0 border-t';

  const hasAnyColumn = xAxis || yAxis || legend || values;

  return (
    <div className={`
      fixed left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-md ${positionClasses} border-gray-700
      transition-all duration-300 ease-in-out shadow-2xl
      ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'}
    `}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 py-3 sm:py-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <span className="text-lg animate-pulse">🎯</span>
            <div>
              <h3 className="text-sm font-semibold text-gray-200">Zones de Drop</h3>
              {hasAnyColumn && (
                <p className="text-xs text-purple-400">
                  {[xAxis, yAxis, legend, values].filter(Boolean).length} colonne(s) assignée(s)
                </p>
              )}
            </div>
          </div>
          <div className="hidden sm:block text-xs text-gray-400">
            Glissez vos colonnes ici pour créer votre visualisation
          </div>
        </div>

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3">
          <div ref={setNodeRefX}>
            <CompactDropZone
              id="x-axis-fixed"
              title="Axe X"
              icon="📊"
              acceptedColumn={xAxis}
              onClear={onClearX}
              isOver={isOverX}
            />
          </div>

          <div ref={setNodeRefY}>
            <CompactDropZone
              id="y-axis-fixed"
              title="Axe Y"
              icon="📈"
              acceptedColumn={yAxis}
              onClear={onClearY}
              isOver={isOverY}
            />
          </div>

          <div ref={setNodeRefLegend}>
            <CompactDropZone
              id="legend-fixed"
              title="Légende"
              icon="🏷️"
              acceptedColumn={legend}
              onClear={onClearLegend}
              isOver={isOverLegend}
            />
          </div>

          <div ref={setNodeRefValues}>
            <CompactDropZone
              id="values-fixed"
              title="Valeurs"
              icon="💎"
              acceptedColumn={values}
              onClear={onClearValues}
              isOver={isOverValues}
            />
          </div>
        </div>

        {/* Indicateur mobile */}
        <div className="sm:hidden mt-2 text-center">
          <p className="text-xs text-gray-500">
            Glissez ou utilisez le menu ⚡ des colonnes
          </p>
        </div>
      </div>
    </div>
  );
};

// Composant pour le sélecteur de fonction d'agrégation
export const AggregationSelector = ({ value, onChange, disabled }) => {
  const aggregations = [
    { value: 'SUM', label: 'Somme', icon: '➕', description: 'Addition de toutes les valeurs' },
    { value: 'AVG', label: 'Moyenne', icon: '📊', description: 'Valeur moyenne' },
    { value: 'COUNT', label: 'Nombre', icon: '🔢', description: 'Nombre d\'occurrences' },
    { value: 'MIN', label: 'Minimum', icon: '⬇️', description: 'Valeur minimale' },
    { value: 'MAX', label: 'Maximum', icon: '⬆️', description: 'Valeur maximale' }
  ];

  return (
    <div>
      <label className="block text-sm font-medium text-gray-300 mb-2">
        Fonction d'agrégation
      </label>
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        className="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-gray-100 focus:border-purple-500 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {aggregations.map((agg) => (
          <option key={agg.value} value={agg.value}>
            {agg.icon} {agg.label} - {agg.description}
          </option>
        ))}
      </select>
    </div>
  );
};

// Composant pour le sélecteur de type de graphique
export const ChartTypeSelector = ({ value, onChange }) => {
  const chartTypes = [
    { value: 'bar', label: 'Barres', icon: '📊', description: 'Graphique en barres' },
    { value: 'line', label: 'Ligne', icon: '📈', description: 'Graphique linéaire' },
    { value: 'pie', label: 'Circulaire', icon: '🥧', description: 'Graphique circulaire' },
    { value: 'scatter', label: 'Nuage', icon: '⚫', description: 'Nuage de points' },
    { value: 'stacked_bar', label: 'Barres empilées', icon: '📚', description: 'Barres empilées' }
  ];

  return (
    <div>
      <label className="block text-sm font-medium text-gray-300 mb-2">
        Type de graphique
      </label>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
        {chartTypes.map((type) => (
          <button
            key={type.value}
            onClick={() => onChange(type.value)}
            className={`p-3 rounded-lg border transition-colors text-center ${
              value === type.value
                ? 'border-purple-500 bg-purple-600/20 text-purple-300'
                : 'border-gray-700 bg-gray-800/50 text-gray-300 hover:border-gray-600'
            }`}
          >
            <div className="text-lg mb-1">{type.icon}</div>
            <div className="text-xs font-medium">{type.label}</div>
          </button>
        ))}
      </div>
    </div>
  );
};
