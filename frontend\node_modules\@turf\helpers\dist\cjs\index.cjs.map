{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-helpers/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACuGO,IAAM,YAAA,EAAc,WAAA;AASpB,IAAM,QAAA,EAAiC;AAAA,EAC5C,WAAA,EAAa,YAAA,EAAc,GAAA;AAAA,EAC3B,WAAA,EAAa,YAAA,EAAc,GAAA;AAAA,EAC3B,OAAA,EAAS,IAAA,EAAA,CAAO,EAAA,EAAI,IAAA,CAAK,EAAA,CAAA;AAAA,EACzB,IAAA,EAAM,YAAA,EAAc,OAAA;AAAA,EACpB,MAAA,EAAQ,YAAA,EAAc,KAAA;AAAA,EACtB,UAAA,EAAY,YAAA,EAAc,GAAA;AAAA,EAC1B,UAAA,EAAY,YAAA,EAAc,GAAA;AAAA,EAC1B,MAAA,EAAQ,WAAA;AAAA,EACR,MAAA,EAAQ,WAAA;AAAA,EACR,KAAA,EAAO,YAAA,EAAc,QAAA;AAAA,EACrB,WAAA,EAAa,YAAA,EAAc,GAAA;AAAA,EAC3B,WAAA,EAAa,YAAA,EAAc,GAAA;AAAA,EAC3B,aAAA,EAAe,YAAA,EAAc,IAAA;AAAA,EAC7B,OAAA,EAAS,CAAA;AAAA,EACT,KAAA,EAAO,YAAA,EAAc;AACvB,CAAA;AAQO,IAAM,YAAA,EAAyC;AAAA,EACpD,KAAA,EAAO,SAAA;AAAA,EACP,WAAA,EAAa,GAAA;AAAA,EACb,WAAA,EAAa,GAAA;AAAA,EACb,IAAA,EAAM,YAAA;AAAA,EACN,QAAA,EAAU,IAAA;AAAA,EACV,MAAA,EAAQ,cAAA;AAAA,EACR,UAAA,EAAY,IAAA;AAAA,EACZ,UAAA,EAAY,IAAA;AAAA,EACZ,MAAA,EAAQ,CAAA;AAAA,EACR,MAAA,EAAQ,CAAA;AAAA,EACR,KAAA,EAAO,MAAA;AAAA,EACP,aAAA,EAAe,qBAAA;AAAA,EACf,WAAA,EAAa,GAAA;AAAA,EACb,WAAA,EAAa,GAAA;AAAA,EACb,KAAA,EAAO;AACT,CAAA;AAsBO,SAAS,OAAA,CAId,IAAA,EACA,UAAA,EACA,QAAA,EAAoC,CAAC,CAAA,EACtB;AACf,EAAA,MAAM,KAAA,EAAY,EAAE,IAAA,EAAM,UAAU,CAAA;AACpC,EAAA,GAAA,CAAI,OAAA,CAAQ,GAAA,IAAO,EAAA,GAAK,OAAA,CAAQ,EAAA,EAAI;AAClC,IAAA,IAAA,CAAK,GAAA,EAAK,OAAA,CAAQ,EAAA;AAAA,EACpB;AACA,EAAA,GAAA,CAAI,OAAA,CAAQ,IAAA,EAAM;AAChB,IAAA,IAAA,CAAK,KAAA,EAAO,OAAA,CAAQ,IAAA;AAAA,EACtB;AACA,EAAA,IAAA,CAAK,WAAA,EAAa,WAAA,GAAc,CAAC,CAAA;AACjC,EAAA,IAAA,CAAK,SAAA,EAAW,IAAA;AAChB,EAAA,OAAO,IAAA;AACT;AAiBO,SAAS,QAAA,CACd,IAAA,EAOA,WAAA,EACA,SAAA,EAAkC,CAAC,CAAA,EACnC;AACA,EAAA,OAAA,CAAQ,IAAA,EAAM;AAAA,IACZ,KAAK,OAAA;AACH,MAAA,OAAO,KAAA,CAAM,WAAW,CAAA,CAAE,QAAA;AAAA,IAC5B,KAAK,YAAA;AACH,MAAA,OAAO,UAAA,CAAW,WAAW,CAAA,CAAE,QAAA;AAAA,IACjC,KAAK,SAAA;AACH,MAAA,OAAO,OAAA,CAAQ,WAAW,CAAA,CAAE,QAAA;AAAA,IAC9B,KAAK,YAAA;AACH,MAAA,OAAO,UAAA,CAAW,WAAW,CAAA,CAAE,QAAA;AAAA,IACjC,KAAK,iBAAA;AACH,MAAA,OAAO,eAAA,CAAgB,WAAW,CAAA,CAAE,QAAA;AAAA,IACtC,KAAK,cAAA;AACH,MAAA,OAAO,YAAA,CAAa,WAAW,CAAA,CAAE,QAAA;AAAA,IACnC,OAAA;AACE,MAAA,MAAM,IAAI,KAAA,CAAM,KAAA,EAAO,aAAa,CAAA;AAAA,EACxC;AACF;AAiBO,SAAS,KAAA,CACd,WAAA,EACA,UAAA,EACA,QAAA,EAAoC,CAAC,CAAA,EAClB;AACnB,EAAA,GAAA,CAAI,CAAC,WAAA,EAAa;AAChB,IAAA,MAAM,IAAI,KAAA,CAAM,yBAAyB,CAAA;AAAA,EAC3C;AACA,EAAA,GAAA,CAAI,CAAC,KAAA,CAAM,OAAA,CAAQ,WAAW,CAAA,EAAG;AAC/B,IAAA,MAAM,IAAI,KAAA,CAAM,8BAA8B,CAAA;AAAA,EAChD;AACA,EAAA,GAAA,CAAI,WAAA,CAAY,OAAA,EAAS,CAAA,EAAG;AAC1B,IAAA,MAAM,IAAI,KAAA,CAAM,6CAA6C,CAAA;AAAA,EAC/D;AACA,EAAA,GAAA,CAAI,CAAC,QAAA,CAAS,WAAA,CAAY,CAAC,CAAC,EAAA,GAAK,CAAC,QAAA,CAAS,WAAA,CAAY,CAAC,CAAC,CAAA,EAAG;AAC1D,IAAA,MAAM,IAAI,KAAA,CAAM,kCAAkC,CAAA;AAAA,EACpD;AAEA,EAAA,MAAM,KAAA,EAAc;AAAA,IAClB,IAAA,EAAM,OAAA;AAAA,IACN;AAAA,EACF,CAAA;AACA,EAAA,OAAO,OAAA,CAAQ,IAAA,EAAM,UAAA,EAAY,OAAO,CAAA;AAC1C;AAsBO,SAAS,MAAA,CACd,WAAA,EACA,UAAA,EACA,QAAA,EAAoC,CAAC,CAAA,EACR;AAC7B,EAAA,OAAO,iBAAA;AAAA,IACL,WAAA,CAAY,GAAA,CAAI,CAAC,MAAA,EAAA,GAAW;AAC1B,MAAA,OAAO,KAAA,CAAM,MAAA,EAAQ,UAAU,CAAA;AAAA,IACjC,CAAC,CAAA;AAAA,IACD;AAAA,EACF,CAAA;AACF;AAiBO,SAAS,OAAA,CACd,WAAA,EACA,UAAA,EACA,QAAA,EAAoC,CAAC,CAAA,EAChB;AACrB,EAAA,IAAA,CAAA,MAAW,KAAA,GAAQ,WAAA,EAAa;AAC9B,IAAA,GAAA,CAAI,IAAA,CAAK,OAAA,EAAS,CAAA,EAAG;AACnB,MAAA,MAAM,IAAI,KAAA;AAAA,QACR;AAAA,MACF,CAAA;AAAA,IACF;AAEA,IAAA,GAAA,CAAI,IAAA,CAAK,IAAA,CAAK,OAAA,EAAS,CAAC,CAAA,CAAE,OAAA,IAAW,IAAA,CAAK,CAAC,CAAA,CAAE,MAAA,EAAQ;AACnD,MAAA,MAAM,IAAI,KAAA,CAAM,6CAA6C,CAAA;AAAA,IAC/D;AAEA,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,IAAA,CAAK,IAAA,CAAK,OAAA,EAAS,CAAC,CAAA,CAAE,MAAA,EAAQ,CAAA,EAAA,EAAK;AAErD,MAAA,GAAA,CAAI,IAAA,CAAK,IAAA,CAAK,OAAA,EAAS,CAAC,CAAA,CAAE,CAAC,EAAA,IAAM,IAAA,CAAK,CAAC,CAAA,CAAE,CAAC,CAAA,EAAG;AAC3C,QAAA,MAAM,IAAI,KAAA,CAAM,6CAA6C,CAAA;AAAA,MAC/D;AAAA,IACF;AAAA,EACF;AACA,EAAA,MAAM,KAAA,EAAgB;AAAA,IACpB,IAAA,EAAM,SAAA;AAAA,IACN;AAAA,EACF,CAAA;AACA,EAAA,OAAO,OAAA,CAAQ,IAAA,EAAM,UAAA,EAAY,OAAO,CAAA;AAC1C;AAoBO,SAAS,QAAA,CACd,WAAA,EACA,UAAA,EACA,QAAA,EAAoC,CAAC,CAAA,EACN;AAC/B,EAAA,OAAO,iBAAA;AAAA,IACL,WAAA,CAAY,GAAA,CAAI,CAAC,MAAA,EAAA,GAAW;AAC1B,MAAA,OAAO,OAAA,CAAQ,MAAA,EAAQ,UAAU,CAAA;AAAA,IACnC,CAAC,CAAA;AAAA,IACD;AAAA,EACF,CAAA;AACF;AAmBO,SAAS,UAAA,CACd,WAAA,EACA,UAAA,EACA,QAAA,EAAoC,CAAC,CAAA,EACb;AACxB,EAAA,GAAA,CAAI,WAAA,CAAY,OAAA,EAAS,CAAA,EAAG;AAC1B,IAAA,MAAM,IAAI,KAAA,CAAM,uDAAuD,CAAA;AAAA,EACzE;AACA,EAAA,MAAM,KAAA,EAAmB;AAAA,IACvB,IAAA,EAAM,YAAA;AAAA,IACN;AAAA,EACF,CAAA;AACA,EAAA,OAAO,OAAA,CAAQ,IAAA,EAAM,UAAA,EAAY,OAAO,CAAA;AAC1C;AAqBO,SAAS,WAAA,CACd,WAAA,EACA,UAAA,EACA,QAAA,EAAoC,CAAC,CAAA,EACH;AAClC,EAAA,OAAO,iBAAA;AAAA,IACL,WAAA,CAAY,GAAA,CAAI,CAAC,MAAA,EAAA,GAAW;AAC1B,MAAA,OAAO,UAAA,CAAW,MAAA,EAAQ,UAAU,CAAA;AAAA,IACtC,CAAC,CAAA;AAAA,IACD;AAAA,EACF,CAAA;AACF;AAwBO,SAAS,iBAAA,CAId,QAAA,EACA,QAAA,EAAoC,CAAC,CAAA,EACZ;AACzB,EAAA,MAAM,GAAA,EAAU,EAAE,IAAA,EAAM,oBAAoB,CAAA;AAC5C,EAAA,GAAA,CAAI,OAAA,CAAQ,EAAA,EAAI;AACd,IAAA,EAAA,CAAG,GAAA,EAAK,OAAA,CAAQ,EAAA;AAAA,EAClB;AACA,EAAA,GAAA,CAAI,OAAA,CAAQ,IAAA,EAAM;AAChB,IAAA,EAAA,CAAG,KAAA,EAAO,OAAA,CAAQ,IAAA;AAAA,EACpB;AACA,EAAA,EAAA,CAAG,SAAA,EAAW,QAAA;AACd,EAAA,OAAO,EAAA;AACT;AAmBO,SAAS,eAAA,CAGd,WAAA,EACA,UAAA,EACA,QAAA,EAAoC,CAAC,CAAA,EACR;AAC7B,EAAA,MAAM,KAAA,EAAwB;AAAA,IAC5B,IAAA,EAAM,iBAAA;AAAA,IACN;AAAA,EACF,CAAA;AACA,EAAA,OAAO,OAAA,CAAQ,IAAA,EAAM,UAAA,EAAY,OAAO,CAAA;AAC1C;AAmBO,SAAS,UAAA,CACd,WAAA,EACA,UAAA,EACA,QAAA,EAAoC,CAAC,CAAA,EACb;AACxB,EAAA,MAAM,KAAA,EAAmB;AAAA,IACvB,IAAA,EAAM,YAAA;AAAA,IACN;AAAA,EACF,CAAA;AACA,EAAA,OAAO,OAAA,CAAQ,IAAA,EAAM,UAAA,EAAY,OAAO,CAAA;AAC1C;AAoBO,SAAS,YAAA,CACd,WAAA,EACA,UAAA,EACA,QAAA,EAAoC,CAAC,CAAA,EACX;AAC1B,EAAA,MAAM,KAAA,EAAqB;AAAA,IACzB,IAAA,EAAM,cAAA;AAAA,IACN;AAAA,EACF,CAAA;AACA,EAAA,OAAO,OAAA,CAAQ,IAAA,EAAM,UAAA,EAAY,OAAO,CAAA;AAC1C;AAoBO,SAAS,kBAAA,CAGd,UAAA,EAGA,UAAA,EACA,QAAA,EAAoC,CAAC,CAAA,EACL;AAChC,EAAA,MAAM,KAAA,EAA2B;AAAA,IAC/B,IAAA,EAAM,oBAAA;AAAA,IACN;AAAA,EACF,CAAA;AACA,EAAA,OAAO,OAAA,CAAQ,IAAA,EAAM,UAAA,EAAY,OAAO,CAAA;AAC1C;AAgBO,SAAS,KAAA,CAAM,GAAA,EAAa,UAAA,EAAY,CAAA,EAAW;AACxD,EAAA,GAAA,CAAI,UAAA,GAAa,CAAA,CAAE,UAAA,GAAa,CAAA,CAAA,EAAI;AAClC,IAAA,MAAM,IAAI,KAAA,CAAM,qCAAqC,CAAA;AAAA,EACvD;AACA,EAAA,MAAM,WAAA,EAAa,IAAA,CAAK,GAAA,CAAI,EAAA,EAAI,UAAA,GAAa,CAAC,CAAA;AAC9C,EAAA,OAAO,IAAA,CAAK,KAAA,CAAM,IAAA,EAAM,UAAU,EAAA,EAAI,UAAA;AACxC;AAYO,SAAS,eAAA,CACd,OAAA,EACA,MAAA,EAAe,YAAA,EACP;AACR,EAAA,MAAM,OAAA,EAAS,OAAA,CAAQ,KAAK,CAAA;AAC5B,EAAA,GAAA,CAAI,CAAC,MAAA,EAAQ;AACX,IAAA,MAAM,IAAI,KAAA,CAAM,MAAA,EAAQ,mBAAmB,CAAA;AAAA,EAC7C;AACA,EAAA,OAAO,QAAA,EAAU,MAAA;AACnB;AAYO,SAAS,eAAA,CACd,QAAA,EACA,MAAA,EAAe,YAAA,EACP;AACR,EAAA,MAAM,OAAA,EAAS,OAAA,CAAQ,KAAK,CAAA;AAC5B,EAAA,GAAA,CAAI,CAAC,MAAA,EAAQ;AACX,IAAA,MAAM,IAAI,KAAA,CAAM,MAAA,EAAQ,mBAAmB,CAAA;AAAA,EAC7C;AACA,EAAA,OAAO,SAAA,EAAW,MAAA;AACpB;AAYO,SAAS,eAAA,CAAgB,QAAA,EAAkB,KAAA,EAAuB;AACvE,EAAA,OAAO,gBAAA,CAAiB,eAAA,CAAgB,QAAA,EAAU,KAAK,CAAC,CAAA;AAC1D;AAUO,SAAS,gBAAA,CAAiB,OAAA,EAAyB;AACxD,EAAA,IAAI,MAAA,EAAQ,QAAA,EAAU,GAAA;AACtB,EAAA,GAAA,CAAI,MAAA,EAAQ,CAAA,EAAG;AACb,IAAA,MAAA,GAAS,GAAA;AAAA,EACX;AACA,EAAA,OAAO,KAAA;AACT;AAUO,SAAS,gBAAA,CAAiB,KAAA,EAAuB;AAEtD,EAAA,MAAA,EAAQ,MAAA,EAAQ,GAAA;AAEhB,EAAA,GAAA,CAAI,MAAA,EAAQ,GAAA,EAAK;AACf,IAAA,OAAO,MAAA,EAAQ,GAAA;AAAA,EACjB,EAAA,KAAA,GAAA,CAAW,MAAA,EAAQ,CAAA,GAAA,EAAM;AACvB,IAAA,OAAO,MAAA,EAAQ,GAAA;AAAA,EACjB;AAEA,EAAA,OAAO,KAAA;AACT;AASO,SAAS,gBAAA,CAAiB,OAAA,EAAyB;AAExD,EAAA,MAAM,kBAAA,EAAoB,QAAA,EAAA,CAAW,EAAA,EAAI,IAAA,CAAK,EAAA,CAAA;AAC9C,EAAA,OAAQ,kBAAA,EAAoB,IAAA,EAAO,IAAA,CAAK,EAAA;AAC1C;AASO,SAAS,gBAAA,CAAiB,OAAA,EAAyB;AAExD,EAAA,MAAM,kBAAA,EAAoB,QAAA,EAAU,GAAA;AACpC,EAAA,OAAQ,kBAAA,EAAoB,IAAA,CAAK,GAAA,EAAM,GAAA;AACzC;AAWO,SAAS,aAAA,CACd,MAAA,EACA,aAAA,EAAsB,YAAA,EACtB,UAAA,EAAmB,YAAA,EACX;AACR,EAAA,GAAA,CAAI,CAAA,CAAE,OAAA,GAAU,CAAA,CAAA,EAAI;AAClB,IAAA,MAAM,IAAI,KAAA,CAAM,kCAAkC,CAAA;AAAA,EACpD;AACA,EAAA,OAAO,eAAA,CAAgB,eAAA,CAAgB,MAAA,EAAQ,YAAY,CAAA,EAAG,SAAS,CAAA;AACzE;AAWO,SAAS,WAAA,CACd,IAAA,EACA,aAAA,EAA0B,QAAA,EAC1B,UAAA,EAAuB,YAAA,EACf;AACR,EAAA,GAAA,CAAI,CAAA,CAAE,KAAA,GAAQ,CAAA,CAAA,EAAI;AAChB,IAAA,MAAM,IAAI,KAAA,CAAM,gCAAgC,CAAA;AAAA,EAClD;AAEA,EAAA,MAAM,YAAA,EAAc,WAAA,CAAY,YAAY,CAAA;AAC5C,EAAA,GAAA,CAAI,CAAC,WAAA,EAAa;AAChB,IAAA,MAAM,IAAI,KAAA,CAAM,wBAAwB,CAAA;AAAA,EAC1C;AAEA,EAAA,MAAM,YAAA,EAAc,WAAA,CAAY,SAAS,CAAA;AACzC,EAAA,GAAA,CAAI,CAAC,WAAA,EAAa;AAChB,IAAA,MAAM,IAAI,KAAA,CAAM,qBAAqB,CAAA;AAAA,EACvC;AAEA,EAAA,OAAQ,KAAA,EAAO,YAAA,EAAe,WAAA;AAChC;AAcO,SAAS,QAAA,CAAS,GAAA,EAAmB;AAC1C,EAAA,OAAO,CAAC,KAAA,CAAM,GAAG,EAAA,GAAK,IAAA,IAAQ,KAAA,GAAQ,CAAC,KAAA,CAAM,OAAA,CAAQ,GAAG,CAAA;AAC1D;AAcO,SAAS,QAAA,CAAS,KAAA,EAAqB;AAC5C,EAAA,OAAO,MAAA,IAAU,KAAA,GAAQ,OAAO,MAAA,IAAU,SAAA,GAAY,CAAC,KAAA,CAAM,OAAA,CAAQ,KAAK,CAAA;AAC5E;AAuBO,SAAS,YAAA,CAAa,IAAA,EAAiB;AAC5C,EAAA,GAAA,CAAI,CAAC,IAAA,EAAM;AACT,IAAA,MAAM,IAAI,KAAA,CAAM,kBAAkB,CAAA;AAAA,EACpC;AACA,EAAA,GAAA,CAAI,CAAC,KAAA,CAAM,OAAA,CAAQ,IAAI,CAAA,EAAG;AACxB,IAAA,MAAM,IAAI,KAAA,CAAM,uBAAuB,CAAA;AAAA,EACzC;AACA,EAAA,GAAA,CAAI,IAAA,CAAK,OAAA,IAAW,EAAA,GAAK,IAAA,CAAK,OAAA,IAAW,CAAA,EAAG;AAC1C,IAAA,MAAM,IAAI,KAAA,CAAM,yCAAyC,CAAA;AAAA,EAC3D;AACA,EAAA,IAAA,CAAK,OAAA,CAAQ,CAAC,GAAA,EAAA,GAAQ;AACpB,IAAA,GAAA,CAAI,CAAC,QAAA,CAAS,GAAG,CAAA,EAAG;AAClB,MAAA,MAAM,IAAI,KAAA,CAAM,gCAAgC,CAAA;AAAA,IAClD;AAAA,EACF,CAAC,CAAA;AACH;AAuBO,SAAS,UAAA,CAAW,EAAA,EAAe;AACxC,EAAA,GAAA,CAAI,CAAC,EAAA,EAAI;AACP,IAAA,MAAM,IAAI,KAAA,CAAM,gBAAgB,CAAA;AAAA,EAClC;AACA,EAAA,GAAA,CAAI,CAAC,QAAA,EAAU,QAAQ,CAAA,CAAE,OAAA,CAAQ,OAAO,EAAE,EAAA,IAAM,CAAA,CAAA,EAAI;AAClD,IAAA,MAAM,IAAI,KAAA,CAAM,iCAAiC,CAAA;AAAA,EACnD;AACF;ADnnBA;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACF,2iCAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-helpers/dist/cjs/index.cjs", "sourcesContent": [null, "import {\n  BBox,\n  Feature,\n  FeatureCollection,\n  Geometry,\n  GeometryCollection,\n  GeometryObject,\n  LineString,\n  MultiLineString,\n  MultiPoint,\n  MultiPolygon,\n  Point,\n  Polygon,\n  Position,\n  GeoJsonProperties,\n} from \"geojson\";\n\nimport { Id } from \"./lib/geojson.js\";\nexport * from \"./lib/geojson.js\";\n\n/**\n * @module helpers\n */\n\n// TurfJS Combined Types\nexport type Coord = Feature<Point> | Point | Position;\n\n/**\n * Linear measurement units.\n *\n * ⚠️ Warning. Be aware of the implications of using radian or degree units to\n * measure distance. The distance represented by a degree of longitude *varies*\n * depending on latitude.\n *\n * See https://www.thoughtco.com/degree-of-latitude-and-longitude-distance-4070616\n * for an illustration of this behaviour.\n *\n * @typedef\n */\nexport type Units =\n  | \"meters\"\n  | \"metres\"\n  | \"millimeters\"\n  | \"millimetres\"\n  | \"centimeters\"\n  | \"centimetres\"\n  | \"kilometers\"\n  | \"kilometres\"\n  | \"miles\"\n  | \"nauticalmiles\"\n  | \"inches\"\n  | \"yards\"\n  | \"feet\"\n  | \"radians\"\n  | \"degrees\";\n\n/**\n * Area measurement units.\n *\n * @typedef\n */\nexport type AreaUnits =\n  | Exclude<Units, \"radians\" | \"degrees\">\n  | \"acres\"\n  | \"hectares\";\n\n/**\n * Grid types.\n *\n * @typedef\n */\nexport type Grid = \"point\" | \"square\" | \"hex\" | \"triangle\";\n\n/**\n * Shorthand corner identifiers.\n *\n * @typedef\n */\nexport type Corners = \"sw\" | \"se\" | \"nw\" | \"ne\" | \"center\" | \"centroid\";\n\n/**\n * Geometries made up of lines i.e. lines and polygons.\n *\n * @typedef\n */\nexport type Lines = LineString | MultiLineString | Polygon | MultiPolygon;\n\n/**\n * Convenience type for all possible GeoJSON.\n *\n * @typedef\n */\nexport type AllGeoJSON =\n  | Feature\n  | FeatureCollection\n  | Geometry\n  | GeometryCollection;\n\n/**\n * The Earth radius in kilometers. Used by Turf modules that model the Earth as a sphere. The {@link https://en.wikipedia.org/wiki/Earth_radius#Arithmetic_mean_radius mean radius} was selected because it is {@link https://rosettacode.org/wiki/Haversine_formula#:~:text=This%20value%20is%20recommended recommended } by the Haversine formula (used by turf/distance) to reduce error.\n *\n * @constant\n */\nexport const earthRadius = 6371008.8;\n\n/**\n * Unit of measurement factors based on earthRadius.\n *\n * Keys are the name of the unit, values are the number of that unit in a single radian\n *\n * @constant\n */\nexport const factors: Record<Units, number> = {\n  centimeters: earthRadius * 100,\n  centimetres: earthRadius * 100,\n  degrees: 360 / (2 * Math.PI),\n  feet: earthRadius * 3.28084,\n  inches: earthRadius * 39.37,\n  kilometers: earthRadius / 1000,\n  kilometres: earthRadius / 1000,\n  meters: earthRadius,\n  metres: earthRadius,\n  miles: earthRadius / 1609.344,\n  millimeters: earthRadius * 1000,\n  millimetres: earthRadius * 1000,\n  nauticalmiles: earthRadius / 1852,\n  radians: 1,\n  yards: earthRadius * 1.0936,\n};\n\n/**\n\n * Area of measurement factors based on 1 square meter.\n *\n * @constant\n */\nexport const areaFactors: Record<AreaUnits, number> = {\n  acres: 0.000247105,\n  centimeters: 10000,\n  centimetres: 10000,\n  feet: 10.763910417,\n  hectares: 0.0001,\n  inches: 1550.003100006,\n  kilometers: 0.000001,\n  kilometres: 0.000001,\n  meters: 1,\n  metres: 1,\n  miles: 3.86e-7,\n  nauticalmiles: 2.9155334959812285e-7,\n  millimeters: 1000000,\n  millimetres: 1000000,\n  yards: 1.195990046,\n};\n\n/**\n * Wraps a GeoJSON {@link Geometry} in a GeoJSON {@link Feature}.\n *\n * @function\n * @param {GeometryObject} geometry input geometry\n * @param {GeoJsonProperties} [properties={}] an Object of key-value pairs to add as properties\n * @param {Object} [options={}] Optional Parameters\n * @param {BBox} [options.bbox] Bounding Box Array [west, south, east, north] associated with the Feature\n * @param {Id} [options.id] Identifier associated with the Feature\n * @returns {Feature<GeometryObject, GeoJsonProperties>} a GeoJSON Feature\n * @example\n * var geometry = {\n *   \"type\": \"Point\",\n *   \"coordinates\": [110, 50]\n * };\n *\n * var feature = turf.feature(geometry);\n *\n * //=feature\n */\nexport function feature<\n  G extends GeometryObject = Geometry,\n  P extends GeoJsonProperties = GeoJsonProperties,\n>(\n  geom: G | null,\n  properties?: P,\n  options: { bbox?: BBox; id?: Id } = {}\n): Feature<G, P> {\n  const feat: any = { type: \"Feature\" };\n  if (options.id === 0 || options.id) {\n    feat.id = options.id;\n  }\n  if (options.bbox) {\n    feat.bbox = options.bbox;\n  }\n  feat.properties = properties || {};\n  feat.geometry = geom;\n  return feat;\n}\n\n/**\n * Creates a GeoJSON {@link Geometry} from a Geometry string type & coordinates.\n * For GeometryCollection type use `helpers.geometryCollection`\n *\n * @function\n * @param {(\"Point\" | \"LineString\" | \"Polygon\" | \"MultiPoint\" | \"MultiLineString\" | \"MultiPolygon\")} type Geometry Type\n * @param {Array<any>} coordinates Coordinates\n * @param {Object} [options={}] Optional Parameters\n * @returns {Geometry} a GeoJSON Geometry\n * @example\n * var type = \"Point\";\n * var coordinates = [110, 50];\n * var geometry = turf.geometry(type, coordinates);\n * // => geometry\n */\nexport function geometry(\n  type:\n    | \"Point\"\n    | \"LineString\"\n    | \"Polygon\"\n    | \"MultiPoint\"\n    | \"MultiLineString\"\n    | \"MultiPolygon\",\n  coordinates: any[],\n  _options: Record<string, never> = {}\n) {\n  switch (type) {\n    case \"Point\":\n      return point(coordinates).geometry;\n    case \"LineString\":\n      return lineString(coordinates).geometry;\n    case \"Polygon\":\n      return polygon(coordinates).geometry;\n    case \"MultiPoint\":\n      return multiPoint(coordinates).geometry;\n    case \"MultiLineString\":\n      return multiLineString(coordinates).geometry;\n    case \"MultiPolygon\":\n      return multiPolygon(coordinates).geometry;\n    default:\n      throw new Error(type + \" is invalid\");\n  }\n}\n\n/**\n * Creates a {@link Point} {@link Feature} from a Position.\n *\n * @function\n * @param {Position} coordinates longitude, latitude position (each in decimal degrees)\n * @param {GeoJsonProperties} [properties={}] an Object of key-value pairs to add as properties\n * @param {Object} [options={}] Optional Parameters\n * @param {BBox} [options.bbox] Bounding Box Array [west, south, east, north] associated with the Feature\n * @param {Id} [options.id] Identifier associated with the Feature\n * @returns {Feature<Point, GeoJsonProperties>} a Point feature\n * @example\n * var point = turf.point([-75.343, 39.984]);\n *\n * //=point\n */\nexport function point<P extends GeoJsonProperties = GeoJsonProperties>(\n  coordinates: Position,\n  properties?: P,\n  options: { bbox?: BBox; id?: Id } = {}\n): Feature<Point, P> {\n  if (!coordinates) {\n    throw new Error(\"coordinates is required\");\n  }\n  if (!Array.isArray(coordinates)) {\n    throw new Error(\"coordinates must be an Array\");\n  }\n  if (coordinates.length < 2) {\n    throw new Error(\"coordinates must be at least 2 numbers long\");\n  }\n  if (!isNumber(coordinates[0]) || !isNumber(coordinates[1])) {\n    throw new Error(\"coordinates must contain numbers\");\n  }\n\n  const geom: Point = {\n    type: \"Point\",\n    coordinates,\n  };\n  return feature(geom, properties, options);\n}\n\n/**\n * Creates a {@link Point} {@link FeatureCollection} from an Array of Point coordinates.\n *\n * @function\n * @param {Position[]} coordinates an array of Points\n * @param {GeoJsonProperties} [properties={}] Translate these properties to each Feature\n * @param {Object} [options={}] Optional Parameters\n * @param {BBox} [options.bbox] Bounding Box Array [west, south, east, north]\n * associated with the FeatureCollection\n * @param {Id} [options.id] Identifier associated with the FeatureCollection\n * @returns {FeatureCollection<Point>} Point Feature\n * @example\n * var points = turf.points([\n *   [-75, 39],\n *   [-80, 45],\n *   [-78, 50]\n * ]);\n *\n * //=points\n */\nexport function points<P extends GeoJsonProperties = GeoJsonProperties>(\n  coordinates: Position[],\n  properties?: P,\n  options: { bbox?: BBox; id?: Id } = {}\n): FeatureCollection<Point, P> {\n  return featureCollection(\n    coordinates.map((coords) => {\n      return point(coords, properties);\n    }),\n    options\n  );\n}\n\n/**\n * Creates a {@link Polygon} {@link Feature} from an Array of LinearRings.\n *\n * @function\n * @param {Position[][]} coordinates an array of LinearRings\n * @param {GeoJsonProperties} [properties={}] an Object of key-value pairs to add as properties\n * @param {Object} [options={}] Optional Parameters\n * @param {BBox} [options.bbox] Bounding Box Array [west, south, east, north] associated with the Feature\n * @param {Id} [options.id] Identifier associated with the Feature\n * @returns {Feature<Polygon, GeoJsonProperties>} Polygon Feature\n * @example\n * var polygon = turf.polygon([[[-5, 52], [-4, 56], [-2, 51], [-7, 54], [-5, 52]]], { name: 'poly1' });\n *\n * //=polygon\n */\nexport function polygon<P extends GeoJsonProperties = GeoJsonProperties>(\n  coordinates: Position[][],\n  properties?: P,\n  options: { bbox?: BBox; id?: Id } = {}\n): Feature<Polygon, P> {\n  for (const ring of coordinates) {\n    if (ring.length < 4) {\n      throw new Error(\n        \"Each LinearRing of a Polygon must have 4 or more Positions.\"\n      );\n    }\n\n    if (ring[ring.length - 1].length !== ring[0].length) {\n      throw new Error(\"First and last Position are not equivalent.\");\n    }\n\n    for (let j = 0; j < ring[ring.length - 1].length; j++) {\n      // Check if first point of Polygon contains two numbers\n      if (ring[ring.length - 1][j] !== ring[0][j]) {\n        throw new Error(\"First and last Position are not equivalent.\");\n      }\n    }\n  }\n  const geom: Polygon = {\n    type: \"Polygon\",\n    coordinates,\n  };\n  return feature(geom, properties, options);\n}\n\n/**\n * Creates a {@link Polygon} {@link FeatureCollection} from an Array of Polygon coordinates.\n *\n * @function\n * @param {Position[][][]} coordinates an array of Polygon coordinates\n * @param {GeoJsonProperties} [properties={}] an Object of key-value pairs to add as properties\n * @param {Object} [options={}] Optional Parameters\n * @param {BBox} [options.bbox] Bounding Box Array [west, south, east, north] associated with the Feature\n * @param {Id} [options.id] Identifier associated with the FeatureCollection\n * @returns {FeatureCollection<Polygon, GeoJsonProperties>} Polygon FeatureCollection\n * @example\n * var polygons = turf.polygons([\n *   [[[-5, 52], [-4, 56], [-2, 51], [-7, 54], [-5, 52]]],\n *   [[[-15, 42], [-14, 46], [-12, 41], [-17, 44], [-15, 42]]],\n * ]);\n *\n * //=polygons\n */\nexport function polygons<P extends GeoJsonProperties = GeoJsonProperties>(\n  coordinates: Position[][][],\n  properties?: P,\n  options: { bbox?: BBox; id?: Id } = {}\n): FeatureCollection<Polygon, P> {\n  return featureCollection(\n    coordinates.map((coords) => {\n      return polygon(coords, properties);\n    }),\n    options\n  );\n}\n\n/**\n * Creates a {@link LineString} {@link Feature} from an Array of Positions.\n *\n * @function\n * @param {Position[]} coordinates an array of Positions\n * @param {GeoJsonProperties} [properties={}] an Object of key-value pairs to add as properties\n * @param {Object} [options={}] Optional Parameters\n * @param {BBox} [options.bbox] Bounding Box Array [west, south, east, north] associated with the Feature\n * @param {Id} [options.id] Identifier associated with the Feature\n * @returns {Feature<LineString, GeoJsonProperties>} LineString Feature\n * @example\n * var linestring1 = turf.lineString([[-24, 63], [-23, 60], [-25, 65], [-20, 69]], {name: 'line 1'});\n * var linestring2 = turf.lineString([[-14, 43], [-13, 40], [-15, 45], [-10, 49]], {name: 'line 2'});\n *\n * //=linestring1\n * //=linestring2\n */\nexport function lineString<P extends GeoJsonProperties = GeoJsonProperties>(\n  coordinates: Position[],\n  properties?: P,\n  options: { bbox?: BBox; id?: Id } = {}\n): Feature<LineString, P> {\n  if (coordinates.length < 2) {\n    throw new Error(\"coordinates must be an array of two or more positions\");\n  }\n  const geom: LineString = {\n    type: \"LineString\",\n    coordinates,\n  };\n  return feature(geom, properties, options);\n}\n\n/**\n * Creates a {@link LineString} {@link FeatureCollection} from an Array of LineString coordinates.\n *\n * @function\n * @param {Position[][]} coordinates an array of LinearRings\n * @param {GeoJsonProperties} [properties={}] an Object of key-value pairs to add as properties\n * @param {Object} [options={}] Optional Parameters\n * @param {BBox} [options.bbox] Bounding Box Array [west, south, east, north]\n * associated with the FeatureCollection\n * @param {Id} [options.id] Identifier associated with the FeatureCollection\n * @returns {FeatureCollection<LineString, GeoJsonProperties>} LineString FeatureCollection\n * @example\n * var linestrings = turf.lineStrings([\n *   [[-24, 63], [-23, 60], [-25, 65], [-20, 69]],\n *   [[-14, 43], [-13, 40], [-15, 45], [-10, 49]]\n * ]);\n *\n * //=linestrings\n */\nexport function lineStrings<P extends GeoJsonProperties = GeoJsonProperties>(\n  coordinates: Position[][],\n  properties?: P,\n  options: { bbox?: BBox; id?: Id } = {}\n): FeatureCollection<LineString, P> {\n  return featureCollection(\n    coordinates.map((coords) => {\n      return lineString(coords, properties);\n    }),\n    options\n  );\n}\n\n/**\n * Takes one or more {@link Feature|Features} and creates a {@link FeatureCollection}.\n *\n * @function\n * @param {Array<Feature<GeometryObject, GeoJsonProperties>>} features input features\n * @param {Object} [options={}] Optional Parameters\n * @param {BBox} [options.bbox] Bounding Box Array [west, south, east, north] associated with the Feature\n * @param {Id} [options.id] Identifier associated with the Feature\n * @returns {FeatureCollection<GeometryObject, GeoJsonProperties>} FeatureCollection of Features\n * @example\n * var locationA = turf.point([-75.343, 39.984], {name: 'Location A'});\n * var locationB = turf.point([-75.833, 39.284], {name: 'Location B'});\n * var locationC = turf.point([-75.534, 39.123], {name: 'Location C'});\n *\n * var collection = turf.featureCollection([\n *   locationA,\n *   locationB,\n *   locationC\n * ]);\n *\n * //=collection\n */\nexport function featureCollection<\n  G extends GeometryObject = Geometry,\n  P extends GeoJsonProperties = GeoJsonProperties,\n>(\n  features: Array<Feature<G, P>>,\n  options: { bbox?: BBox; id?: Id } = {}\n): FeatureCollection<G, P> {\n  const fc: any = { type: \"FeatureCollection\" };\n  if (options.id) {\n    fc.id = options.id;\n  }\n  if (options.bbox) {\n    fc.bbox = options.bbox;\n  }\n  fc.features = features;\n  return fc;\n}\n\n/**\n * Creates a {@link Feature}<{@link MultiLineString}> based on a\n * coordinate array. Properties can be added optionally.\n *\n * @function\n * @param {Position[][]} coordinates an array of LineStrings\n * @param {GeoJsonProperties} [properties={}] an Object of key-value pairs to add as properties\n * @param {Object} [options={}] Optional Parameters\n * @param {BBox} [options.bbox] Bounding Box Array [west, south, east, north] associated with the Feature\n * @param {Id} [options.id] Identifier associated with the Feature\n * @returns {Feature<MultiLineString, GeoJsonProperties>} a MultiLineString feature\n * @throws {Error} if no coordinates are passed\n * @example\n * var multiLine = turf.multiLineString([[[0,0],[10,10]]]);\n *\n * //=multiLine\n */\nexport function multiLineString<\n  P extends GeoJsonProperties = GeoJsonProperties,\n>(\n  coordinates: Position[][],\n  properties?: P,\n  options: { bbox?: BBox; id?: Id } = {}\n): Feature<MultiLineString, P> {\n  const geom: MultiLineString = {\n    type: \"MultiLineString\",\n    coordinates,\n  };\n  return feature(geom, properties, options);\n}\n\n/**\n * Creates a {@link Feature}<{@link MultiPoint}> based on a\n * coordinate array. Properties can be added optionally.\n *\n * @function\n * @param {Position[]} coordinates an array of Positions\n * @param {GeoJsonProperties} [properties={}] an Object of key-value pairs to add as properties\n * @param {Object} [options={}] Optional Parameters\n * @param {BBox} [options.bbox] Bounding Box Array [west, south, east, north] associated with the Feature\n * @param {Id} [options.id] Identifier associated with the Feature\n * @returns {Feature<MultiPoint, GeoJsonProperties>} a MultiPoint feature\n * @throws {Error} if no coordinates are passed\n * @example\n * var multiPt = turf.multiPoint([[0,0],[10,10]]);\n *\n * //=multiPt\n */\nexport function multiPoint<P extends GeoJsonProperties = GeoJsonProperties>(\n  coordinates: Position[],\n  properties?: P,\n  options: { bbox?: BBox; id?: Id } = {}\n): Feature<MultiPoint, P> {\n  const geom: MultiPoint = {\n    type: \"MultiPoint\",\n    coordinates,\n  };\n  return feature(geom, properties, options);\n}\n\n/**\n * Creates a {@link Feature}<{@link MultiPolygon}> based on a\n * coordinate array. Properties can be added optionally.\n *\n * @function\n * @param {Position[][][]} coordinates an array of Polygons\n * @param {GeoJsonProperties} [properties={}] an Object of key-value pairs to add as properties\n * @param {Object} [options={}] Optional Parameters\n * @param {BBox} [options.bbox] Bounding Box Array [west, south, east, north] associated with the Feature\n * @param {Id} [options.id] Identifier associated with the Feature\n * @returns {Feature<MultiPolygon, GeoJsonProperties>} a multipolygon feature\n * @throws {Error} if no coordinates are passed\n * @example\n * var multiPoly = turf.multiPolygon([[[[0,0],[0,10],[10,10],[10,0],[0,0]]]]);\n *\n * //=multiPoly\n *\n */\nexport function multiPolygon<P extends GeoJsonProperties = GeoJsonProperties>(\n  coordinates: Position[][][],\n  properties?: P,\n  options: { bbox?: BBox; id?: Id } = {}\n): Feature<MultiPolygon, P> {\n  const geom: MultiPolygon = {\n    type: \"MultiPolygon\",\n    coordinates,\n  };\n  return feature(geom, properties, options);\n}\n\n/**\n * Creates a Feature<GeometryCollection> based on a\n * coordinate array. Properties can be added optionally.\n *\n * @function\n * @param {Array<Point | LineString | Polygon | MultiPoint | MultiLineString | MultiPolygon>} geometries an array of GeoJSON Geometries\n * @param {GeoJsonProperties} [properties={}] an Object of key-value pairs to add as properties\n * @param {Object} [options={}] Optional Parameters\n * @param {BBox} [options.bbox] Bounding Box Array [west, south, east, north] associated with the Feature\n * @param {Id} [options.id] Identifier associated with the Feature\n * @returns {Feature<GeometryCollection, GeoJsonProperties>} a GeoJSON GeometryCollection Feature\n * @example\n * var pt = turf.geometry(\"Point\", [100, 0]);\n * var line = turf.geometry(\"LineString\", [[101, 0], [102, 1]]);\n * var collection = turf.geometryCollection([pt, line]);\n *\n * // => collection\n */\nexport function geometryCollection<\n  P extends GeoJsonProperties = GeoJsonProperties,\n>(\n  geometries: Array<\n    Point | LineString | Polygon | MultiPoint | MultiLineString | MultiPolygon\n  >,\n  properties?: P,\n  options: { bbox?: BBox; id?: Id } = {}\n): Feature<GeometryCollection, P> {\n  const geom: GeometryCollection = {\n    type: \"GeometryCollection\",\n    geometries,\n  };\n  return feature(geom, properties, options);\n}\n\n/**\n * Round number to precision\n *\n * @function\n * @param {number} num Number\n * @param {number} [precision=0] Precision\n * @returns {number} rounded number\n * @example\n * turf.round(120.4321)\n * //=120\n *\n * turf.round(120.4321, 2)\n * //=120.43\n */\nexport function round(num: number, precision = 0): number {\n  if (precision && !(precision >= 0)) {\n    throw new Error(\"precision must be a positive number\");\n  }\n  const multiplier = Math.pow(10, precision || 0);\n  return Math.round(num * multiplier) / multiplier;\n}\n\n/**\n * Convert a distance measurement (assuming a spherical Earth) from radians to a more friendly unit.\n * Valid units: miles, nauticalmiles, inches, yards, meters, metres, kilometers, centimeters, feet\n *\n * @function\n * @param {number} radians in radians across the sphere\n * @param {Units} [units=\"kilometers\"] can be degrees, radians, miles, inches, yards, metres,\n * meters, kilometres, kilometers.\n * @returns {number} distance\n */\nexport function radiansToLength(\n  radians: number,\n  units: Units = \"kilometers\"\n): number {\n  const factor = factors[units];\n  if (!factor) {\n    throw new Error(units + \" units is invalid\");\n  }\n  return radians * factor;\n}\n\n/**\n * Convert a distance measurement (assuming a spherical Earth) from a real-world unit into radians\n * Valid units: miles, nauticalmiles, inches, yards, meters, metres, kilometers, centimeters, feet\n *\n * @function\n * @param {number} distance in real units\n * @param {Units} [units=\"kilometers\"] can be degrees, radians, miles, inches, yards, metres,\n * meters, kilometres, kilometers.\n * @returns {number} radians\n */\nexport function lengthToRadians(\n  distance: number,\n  units: Units = \"kilometers\"\n): number {\n  const factor = factors[units];\n  if (!factor) {\n    throw new Error(units + \" units is invalid\");\n  }\n  return distance / factor;\n}\n\n/**\n * Convert a distance measurement (assuming a spherical Earth) from a real-world unit into degrees\n * Valid units: miles, nauticalmiles, inches, yards, meters, metres, centimeters, kilometres, feet\n *\n * @function\n * @param {number} distance in real units\n * @param {Units} [units=\"kilometers\"] can be degrees, radians, miles, inches, yards, metres,\n * meters, kilometres, kilometers.\n * @returns {number} degrees\n */\nexport function lengthToDegrees(distance: number, units?: Units): number {\n  return radiansToDegrees(lengthToRadians(distance, units));\n}\n\n/**\n * Converts any bearing angle from the north line direction (positive clockwise)\n * and returns an angle between 0-360 degrees (positive clockwise), 0 being the north line\n *\n * @function\n * @param {number} bearing angle, between -180 and +180 degrees\n * @returns {number} angle between 0 and 360 degrees\n */\nexport function bearingToAzimuth(bearing: number): number {\n  let angle = bearing % 360;\n  if (angle < 0) {\n    angle += 360;\n  }\n  return angle;\n}\n\n/**\n * Converts any azimuth angle from the north line direction (positive clockwise)\n * and returns an angle between -180 and +180 degrees (positive clockwise), 0 being the north line\n *\n * @function\n * @param {number} angle between 0 and 360 degrees\n * @returns {number} bearing between -180 and +180 degrees\n */\nexport function azimuthToBearing(angle: number): number {\n  // Ignore full revolutions (multiples of 360)\n  angle = angle % 360;\n\n  if (angle > 180) {\n    return angle - 360;\n  } else if (angle < -180) {\n    return angle + 360;\n  }\n\n  return angle;\n}\n\n/**\n * Converts an angle in radians to degrees\n *\n * @function\n * @param {number} radians angle in radians\n * @returns {number} degrees between 0 and 360 degrees\n */\nexport function radiansToDegrees(radians: number): number {\n  // % (2 * Math.PI) radians in case someone passes value > 2π\n  const normalisedRadians = radians % (2 * Math.PI);\n  return (normalisedRadians * 180) / Math.PI;\n}\n\n/**\n * Converts an angle in degrees to radians\n *\n * @function\n * @param {number} degrees angle between 0 and 360 degrees\n * @returns {number} angle in radians\n */\nexport function degreesToRadians(degrees: number): number {\n  // % 360 degrees in case someone passes value > 360\n  const normalisedDegrees = degrees % 360;\n  return (normalisedDegrees * Math.PI) / 180;\n}\n\n/**\n * Converts a length from one unit to another.\n *\n * @function\n * @param {number} length Length to be converted\n * @param {Units} [originalUnit=\"kilometers\"] Input length unit\n * @param {Units} [finalUnit=\"kilometers\"] Returned length unit\n * @returns {number} The converted length\n */\nexport function convertLength(\n  length: number,\n  originalUnit: Units = \"kilometers\",\n  finalUnit: Units = \"kilometers\"\n): number {\n  if (!(length >= 0)) {\n    throw new Error(\"length must be a positive number\");\n  }\n  return radiansToLength(lengthToRadians(length, originalUnit), finalUnit);\n}\n\n/**\n * Converts an area from one unit to another.\n *\n * @function\n * @param {number} area Area to be converted\n * @param {AreaUnits} [originalUnit=\"meters\"] Input area unit\n * @param {AreaUnits} [finalUnit=\"kilometers\"] Returned area unit\n * @returns {number} The converted length\n */\nexport function convertArea(\n  area: number,\n  originalUnit: AreaUnits = \"meters\",\n  finalUnit: AreaUnits = \"kilometers\"\n): number {\n  if (!(area >= 0)) {\n    throw new Error(\"area must be a positive number\");\n  }\n\n  const startFactor = areaFactors[originalUnit];\n  if (!startFactor) {\n    throw new Error(\"invalid original units\");\n  }\n\n  const finalFactor = areaFactors[finalUnit];\n  if (!finalFactor) {\n    throw new Error(\"invalid final units\");\n  }\n\n  return (area / startFactor) * finalFactor;\n}\n\n/**\n * isNumber\n *\n * @function\n * @param {any} num Number to validate\n * @returns {boolean} true/false\n * @example\n * turf.isNumber(123)\n * //=true\n * turf.isNumber('foo')\n * //=false\n */\nexport function isNumber(num: any): boolean {\n  return !isNaN(num) && num !== null && !Array.isArray(num);\n}\n\n/**\n * isObject\n *\n * @function\n * @param {any} input variable to validate\n * @returns {boolean} true/false, including false for Arrays and Functions\n * @example\n * turf.isObject({elevation: 10})\n * //=true\n * turf.isObject('foo')\n * //=false\n */\nexport function isObject(input: any): boolean {\n  return input !== null && typeof input === \"object\" && !Array.isArray(input);\n}\n\n/**\n * Validate BBox\n *\n * @private\n * @param {any} bbox BBox to validate\n * @returns {void}\n * @throws {Error} if BBox is not valid\n * @example\n * validateBBox([-180, -40, 110, 50])\n * //=OK\n * validateBBox([-180, -40])\n * //=Error\n * validateBBox('Foo')\n * //=Error\n * validateBBox(5)\n * //=Error\n * validateBBox(null)\n * //=Error\n * validateBBox(undefined)\n * //=Error\n */\nexport function validateBBox(bbox: any): void {\n  if (!bbox) {\n    throw new Error(\"bbox is required\");\n  }\n  if (!Array.isArray(bbox)) {\n    throw new Error(\"bbox must be an Array\");\n  }\n  if (bbox.length !== 4 && bbox.length !== 6) {\n    throw new Error(\"bbox must be an Array of 4 or 6 numbers\");\n  }\n  bbox.forEach((num) => {\n    if (!isNumber(num)) {\n      throw new Error(\"bbox must only contain numbers\");\n    }\n  });\n}\n\n/**\n * Validate Id\n *\n * @private\n * @param {any} id Id to validate\n * @returns {void}\n * @throws {Error} if Id is not valid\n * @example\n * validateId([-180, -40, 110, 50])\n * //=Error\n * validateId([-180, -40])\n * //=Error\n * validateId('Foo')\n * //=OK\n * validateId(5)\n * //=OK\n * validateId(null)\n * //=Error\n * validateId(undefined)\n * //=Error\n */\nexport function validateId(id: any): void {\n  if (!id) {\n    throw new Error(\"id is required\");\n  }\n  if ([\"string\", \"number\"].indexOf(typeof id) === -1) {\n    throw new Error(\"id must be a number or a string\");\n  }\n}\n"]}