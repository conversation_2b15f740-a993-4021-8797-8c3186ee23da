{"version": 3, "names": ["GLOBAL_TYPES", "exports", "WeakMap", "isGlobalType", "scope", "name", "hasBinding", "get", "has", "console", "warn", "registerGlobalType", "programScope", "add"], "sources": ["../src/global-types.ts"], "sourcesContent": ["import type { <PERSON>de<PERSON><PERSON>, Scope } from \"@babel/core\";\n\nexport const GLOBAL_TYPES = new WeakMap<Scope, Set<string>>();\n\nexport function isGlobalType({ scope }: NodePath, name: string) {\n  if (scope.hasBinding(name)) return false;\n  if (GLOBAL_TYPES.get(scope).has(name)) return true;\n\n  console.warn(\n    `The exported identifier \"${name}\" is not declared in Babel's scope tracker\\n` +\n      `as a JavaScript value binding, and \"@babel/plugin-transform-typescript\"\\n` +\n      `never encountered it as a TypeScript type declaration.\\n` +\n      `It will be treated as a JavaScript value.\\n\\n` +\n      `This problem is likely caused by another plugin injecting\\n` +\n      `\"${name}\" without registering it in the scope tracker. If you are the author\\n` +\n      ` of that plugin, please use \"scope.registerDeclaration(declarationPath)\".`,\n  );\n\n  return false;\n}\n\nexport function registerGlobalType(programScope: Scope, name: string) {\n  GLOBAL_TYPES.get(programScope).add(name);\n}\n"], "mappings": ";;;;;;;;AAEO,MAAMA,YAAY,GAAAC,OAAA,CAAAD,YAAA,GAAG,IAAIE,OAAO,CAAqB,CAAC;AAEtD,SAASC,YAAYA,CAAC;EAAEC;AAAgB,CAAC,EAAEC,IAAY,EAAE;EAC9D,IAAID,KAAK,CAACE,UAAU,CAACD,IAAI,CAAC,EAAE,OAAO,KAAK;EACxC,IAAIL,YAAY,CAACO,GAAG,CAACH,KAAK,CAAC,CAACI,GAAG,CAACH,IAAI,CAAC,EAAE,OAAO,IAAI;EAElDI,OAAO,CAACC,IAAI,CACV,4BAA4BL,IAAI,8CAA8C,GAC5E,2EAA2E,GAC3E,0DAA0D,GAC1D,+CAA+C,GAC/C,6DAA6D,GAC7D,IAAIA,IAAI,wEAAwE,GAChF,2EACJ,CAAC;EAED,OAAO,KAAK;AACd;AAEO,SAASM,kBAAkBA,CAACC,YAAmB,EAAEP,IAAY,EAAE;EACpEL,YAAY,CAACO,GAAG,CAACK,YAAY,CAAC,CAACC,GAAG,CAACR,IAAI,CAAC;AAC1C", "ignoreList": []}