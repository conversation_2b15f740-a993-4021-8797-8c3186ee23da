/* App.css - BrAInBI Dark Theme */

/* Import des polices */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@400;500;600;700&display=swap');

/* Variables CSS personnalisées BrAInBI */
:root {
  --bg-primary: #0d0f1a;
  --text-gradient: linear-gradient(to right, #a74eff, #3db5ff);
  --purple-primary: #a74eff;
  --blue-primary: #3db5ff;
  --purple-dark: #8b3fd9;
  --blue-dark: #2a9de6;
}

/* Reset et classes utilitaires Tailwind-like */
* {
  box-sizing: border-box;
}

/* Smooth scrolling global pour BrAInBI */
html {
  scroll-behavior: smooth;
}

/* Classes utilitaires de base */
.min-h-screen { min-height: 100vh; }
.bg-gray-950 { background-color: #0d0f1a; }
.text-gray-100 { color: #F3F4F6; }
.max-w-7xl { max-width: 80rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-12 { margin-bottom: 3rem; }
.flex { display: flex; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.w-8 { width: 2rem; }
.h-8 { height: 2rem; }
.w-10 { width: 2.5rem; }
.h-10 { height: 2.5rem; }
.w-16 { width: 4rem; }
.h-16 { height: 4rem; }
.w-6 { width: 1.5rem; }
.h-6 { height: 1.5rem; }
.w-2 { width: 0.5rem; }
.h-2 { height: 0.5rem; }
.w-full { width: 100%; }
.h-96 { height: 24rem; }
.text-xl { font-size: 1.25rem; }
.text-lg { font-size: 1.125rem; }
.text-sm { font-size: 0.875rem; }
.text-xs { font-size: 0.75rem; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-medium { font-weight: 500; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-full { border-radius: 9999px; }
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-dashed { border-style: dashed; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.text-center { text-align: center; }
.text-left { text-align: left; }
.cursor-pointer { cursor: pointer; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }
.hidden { display: none; }
.grid { display: grid; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-6 { gap: 1.5rem; }
.flex-1 { flex: 1 1 0%; }
.flex-wrap { flex-wrap: wrap; }
.max-w-md { max-width: 28rem; }
.max-w-2xl { max-width: 42rem; }
.leading-relaxed { line-height: 1.625; }
.whitespace-pre-wrap { white-space: pre-wrap; }
.sticky { position: sticky; }
.top-0 { top: 0; }
.fixed { position: fixed; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.bottom-6 { bottom: 1.5rem; }
.right-6 { right: 1.5rem; }
.z-50 { z-index: 50; }
.z-1000 { z-index: 1000; }
.relative { position: relative; }
.absolute { position: absolute; }
.left-3 { left: 0.75rem; }
.top-1\/2 { top: 50%; }
.transform { transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.-translate-y-1\/2 { --tw-translate-y: -50%; }
.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.animate-spin { animation: spin 1s linear infinite; }
.opacity-50 { opacity: 0.5; }
.opacity-60 { opacity: 0.6; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }
.shadow-xl { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); }
.backdrop-blur-sm { backdrop-filter: blur(4px); }
.min-w-0 { min-width: 0px; }
.self-end { align-self: flex-end; }
.self-start { align-self: flex-start; }
.ml-12 { margin-left: 3rem; }
.mr-12 { margin-right: 3rem; }
.mt-4 { margin-top: 1rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-1 { margin-top: 0.25rem; }
.pl-10 { padding-left: 2.5rem; }

/* Couleurs spécifiques */
.bg-gray-900 { background-color: #111827; }
.bg-gray-800 { background-color: #1F2937; }
.bg-gray-700 { background-color: #374151; }
.bg-gray-950 { background-color: #0E1117; }
.bg-indigo-600 { background-color: #4F46E5; }
.bg-indigo-700 { background-color: #4338CA; }
.bg-purple-600 { background-color: #a74eff; }
.bg-purple-700 { background-color: #8b3fd9; }
.bg-blue-600 { background-color: #3db5ff; }
.bg-blue-700 { background-color: #2a9de6; }
.bg-red-500 { background-color: #EF4444; }
.bg-green-400 { background-color: #4ADE80; }
.bg-yellow-500 { background-color: #EAB308; }
.bg-transparent { background-color: transparent; }

.text-white { color: #FFFFFF; }
.text-gray-100 { color: #F3F4F6; }
.text-gray-200 { color: #E5E7EB; }
.text-gray-300 { color: #D1D5DB; }
.text-gray-400 { color: #9CA3AF; }
.text-gray-500 { color: #6B7280; }
.text-red-200 { color: #FECACA; }
.text-red-300 { color: #FCA5A5; }
.text-yellow-200 { color: #FEF08A; }
.text-green-400 { color: #4ADE80; }
.text-red-400 { color: #F87171; }

.border-gray-800 { border-color: #1F2937; }
.border-gray-700 { border-color: #374151; }
.border-gray-600 { border-color: #4B5563; }
.border-gray-500 { border-color: #6B7280; }
.border-indigo-500 { border-color: #6366F1; }
.border-red-500 { border-color: #EF4444; }
.border-yellow-500 { border-color: #EAB308; }

.hover\:bg-indigo-700:hover { background-color: #4338CA; }
.hover\:bg-gray-700:hover { background-color: #374151; }
.hover\:bg-gray-800:hover { background-color: #1F2937; }
.hover\:border-gray-500:hover { border-color: #6B7280; }
.hover\:border-indigo-500:hover { border-color: #6366F1; }
.hover\:text-gray-100:hover { color: #F3F4F6; }
.hover\:text-gray-200:hover { color: #E5E7EB; }

.focus\:border-indigo-500:focus { border-color: #6366F1; }
.focus\:ring-1:focus { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); }
.focus\:ring-indigo-500:focus { --tw-ring-color: #6366F1; }

.disabled\:bg-gray-700:disabled { background-color: #374151; }
.disabled\:opacity-50:disabled { opacity: 0.5; }
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }

/* Classes de grille */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

/* Responsive */
@media (min-width: 768px) {
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:text-6xl { font-size: 3.75rem; }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

/* Tailles de texte */
.text-5xl { font-size: 3rem; }
.text-6xl { font-size: 3.75rem; }
.text-2xl { font-size: 1.5rem; }

/* Dégradés */
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.from-indigo-400 { --tw-gradient-from: #818CF8; --tw-gradient-to: rgb(129 140 248 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.to-pink-500 { --tw-gradient-to: #EC4899; }
.from-purple-400 { --tw-gradient-from: #a74eff; --tw-gradient-to: rgb(167 78 255 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.to-blue-400 { --tw-gradient-to: #3db5ff; }
.from-purple-600 { --tw-gradient-from: #a74eff; --tw-gradient-to: rgb(167 78 255 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.to-blue-600 { --tw-gradient-to: #3db5ff; }
.from-purple-700 { --tw-gradient-from: #8b3fd9; --tw-gradient-to: rgb(139 63 217 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.to-blue-700 { --tw-gradient-to: #2a9de6; }
.bg-clip-text { -webkit-background-clip: text; background-clip: text; }
.text-transparent { color: transparent; }

/* Classes spéciales pour les effets */
.backdrop-blur-sm { backdrop-filter: blur(4px); -webkit-backdrop-filter: blur(4px); }

/* Effets de glow */
.shadow-indigo-500\/25 { box-shadow: 0 10px 15px -3px rgb(99 102 241 / 0.25), 0 4px 6px -4px rgb(99 102 241 / 0.25); }
.hover\:shadow-indigo-500\/25:hover { box-shadow: 0 10px 15px -3px rgb(99 102 241 / 0.25), 0 4px 6px -4px rgb(99 102 241 / 0.25); }
.shadow-purple-500\/25 { box-shadow: 0 10px 15px -3px rgb(167 78 255 / 0.25), 0 4px 6px -4px rgb(167 78 255 / 0.25); }
.hover\:shadow-purple-500\/25:hover { box-shadow: 0 10px 15px -3px rgb(167 78 255 / 0.25), 0 4px 6px -4px rgb(167 78 255 / 0.25); }
.hover\:shadow-xl:hover { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); }

/* Backgrounds avec opacité */
.bg-gray-900\/50 { background-color: rgb(17 24 39 / 0.5); }
.bg-gray-900\/70 { background-color: rgb(17 24 39 / 0.7); }
.bg-gray-900\/30 { background-color: rgb(17 24 39 / 0.3); }
.bg-gray-800\/50 { background-color: rgb(31 41 55 / 0.5); }
.bg-gray-800\/70 { background-color: rgb(31 41 55 / 0.7); }
.bg-red-900\/20 { background-color: rgb(127 29 29 / 0.2); }
.bg-yellow-900\/10 { background-color: rgb(113 63 18 / 0.1); }
.bg-blue-900\/50 { background-color: rgb(30 58 138 / 0.5); }
.bg-green-900\/50 { background-color: rgb(20 83 45 / 0.5); }
.bg-yellow-900\/50 { background-color: rgb(113 63 18 / 0.5); }
.bg-red-900\/50 { background-color: rgb(127 29 29 / 0.5); }
.bg-black\/50 { background-color: rgb(0 0 0 / 0.5); }

/* Bordures avec opacité */
.border-gray-800\/30 { border-color: rgb(31 41 55 / 0.3); }
.border-red-500\/50 { border-color: rgb(239 68 68 / 0.5); }
.border-yellow-500\/30 { border-color: rgb(234 179 8 / 0.3); }
.border-green-700 { border-color: #15803D; }
.border-yellow-700 { border-color: #A16207; }
.border-red-700 { border-color: #B91C1C; }
.border-blue-700 { border-color: #1D4ED8; }

/* Couleurs de texte supplémentaires */
.text-green-300 { color: #86EFAC; }
.text-yellow-300 { color: #FDE047; }
.text-blue-300 { color: #93C5FD; }

/* Hover states pour les bordures */
.hover\:border-gray-600:hover { border-color: #4B5563; }
.hover\:bg-blue-800\/70:hover { background-color: rgb(30 64 175 / 0.7); }

/* Variables CSS personnalisées */
:root {
  --bg-primary: #0E1117;
  --text-gradient: linear-gradient(to right, #818CF8, #EC4899);
}

/* Styles de base */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background-color: #0d0f1a;
  color: #F3F4F6;
  margin: 0;
  padding: 0;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Scrollbar personnalisée */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1F2937;
}

::-webkit-scrollbar-thumb {
  background: #4B5563;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6B7280;
}

/* Animations personnalisées BrAInBI */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(99, 102, 241, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.8);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.fade-in {
  animation: fadeIn 0.8s ease-out;
}

.slide-in-top {
  animation: slideInFromTop 0.6s ease-out;
}

.brainbi-entrance {
  animation: fadeIn 1s ease-out, slideInFromTop 0.8s ease-out;
}

/* Classes utilitaires pour le scroll */
.scroll-smooth {
  scroll-behavior: smooth;
}

.scroll-auto {
  overflow: auto;
}

.scroll-y-auto {
  overflow-y: auto;
}

.scroll-mt-4 {
  scroll-margin-top: 1rem;
}

.pulse-glow {
  animation: pulse-glow 2s infinite;
}

/* Styles pour les graphiques Plotly */
.plotly-graph-div {
  border-radius: 0.75rem !important;
  background: transparent !important;
}

.plotly-graph-div .main-svg {
  border-radius: 0.75rem !important;
}

/* Styles pour les éléments interactifs */
.interactive-hover {
  transition: all 0.2s ease-in-out;
}

.interactive-hover:hover {
  transform: translateY(-2px);
}

/* Gradient text utility */
.gradient-text {
  background: linear-gradient(to right, #818CF8, #EC4899);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Backdrop blur utility */
.backdrop-blur-custom {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Focus styles pour l'accessibilité */
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
  outline: 2px solid #6366F1;
  outline-offset: 2px;
}

/* Styles pour les éléments désactivés */
.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Styles pour les tables */
.data-table {
  border-collapse: collapse;
  width: 100%;
}

.data-table th,
.data-table td {
  text-align: left;
  padding: 0.75rem;
  border-bottom: 1px solid #374151;
}

.data-table th {
  background-color: rgba(55, 65, 81, 0.5);
  font-weight: 600;
  color: #D1D5DB;
}

.data-table tr:hover {
  background-color: rgba(55, 65, 81, 0.3);
}

/* Responsive utilities */
@media (max-width: 768px) {
  .mobile-stack {
    flex-direction: column;
  }

  .mobile-full {
    width: 100%;
  }

  .mobile-text-center {
    text-align: center;
  }
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #374151 25%, #4B5563 50%, #374151 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Custom utilities */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.glass-effect {
  background: rgba(17, 24, 39, 0.7);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(55, 65, 81, 0.3);
}

/* Animations pour le drag & drop */
@keyframes dropZoneHover {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(167, 78, 255, 0);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 20px rgba(167, 78, 255, 0.3);
  }
  100% {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(167, 78, 255, 0.5);
  }
}

@keyframes columnDragStart {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: scale(1.1) rotate(3deg);
    opacity: 0.8;
  }
}

@keyframes fixedBarSlideIn {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fixedBarSlideOut {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(100%);
    opacity: 0;
  }
}

.drop-zone-hover {
  animation: dropZoneHover 0.3s ease-out forwards;
}

.column-drag-start {
  animation: columnDragStart 0.2s ease-out forwards;
}

.fixed-bar-enter {
  animation: fixedBarSlideIn 0.3s ease-out forwards;
}

.fixed-bar-exit {
  animation: fixedBarSlideOut 0.3s ease-out forwards;
}

/* Effet de glow pour les zones de drop actives */
.drop-zone-glow {
  box-shadow:
    0 0 20px rgba(167, 78, 255, 0.4),
    0 0 40px rgba(167, 78, 255, 0.2),
    inset 0 0 20px rgba(167, 78, 255, 0.1);
}

/* Animation de pulsation pour les colonnes draggables */
.draggable-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .header {
    padding: 1rem;
  }

  .upload-section,
  .data-display,
  .chat-section {
    padding: 1rem;
  }

  .chat-input-section {
    flex-direction: column;
    gap: 0.5rem;
  }

  .table {
    font-size: 0.8rem;
  }

  .table th,
  .table td {
    padding: 0.5rem;
  }

  .question-suggestions {
    padding: 0.5rem;
  }

  .btn-suggestion {
    font-size: 0.7rem;
    padding: 0.2rem 0.6rem;
  }

  .kpi-modal-content {
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }
}
